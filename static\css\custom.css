/* 自定义CSS样式 - 黄金ERP系统 */

:root {
  --primary-color: #d4af37;      /* 金色 */
  --primary-dark: #b8860b;       /* 深金色 */
  --primary-light: #f5e7c1;      /* 浅金色 */
  --secondary-color: #343a40;    /* 深灰色 */
  --accent-color: #5d4037;       /* 褐色 */
  --success-color: #2e7d32;      /* 深绿色 */
  --info-color: #0288d1;         /* 深蓝色 */
  --warning-color: #ff8f00;      /* 橙色 */
  --danger-color: #c62828;       /* 深红色 */
  --light-color: #f8f9fa;        /* 浅灰色 */
  --dark-color: #212529;         /* 黑色 */
  --border-radius: 0.5rem;       /* 圆角大小 */
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* 阴影效果 */
}

/* ========== 全局样式 ========== */
body {
  background-color: #f9f7f2;
  color: var(--dark-color);
  font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.sidebar {
  background: linear-gradient(to bottom, var(--secondary-color), #232323);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
  border-radius: var(--border-radius);
  margin-bottom: 5px;
}

.sidebar .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
  background-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.sidebar .nav-link i {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ========== 标题和内容区域 ========== */
.content-wrapper {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(249, 247, 242, 0.9));
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.page-header {
  border-bottom: 2px solid var(--primary-color);
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
}

.page-title {
  color: var(--accent-color);
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* ========== 按钮样式 ========== */
.btn {
  border-radius: var(--border-radius);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  font-weight: 500;
  letter-spacing: 0.3px;
  padding: 0.5rem 1rem;

}



.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--dark-color);
}

.btn-primary:hover, .btn-primary:focus {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: white;
}

.btn-outline-secondary {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
}

.btn-outline-secondary:hover {
  background-color: var(--secondary-color);
  color: white;
}

.btn-group-sm .btn {
  border-radius: calc(var(--border-radius) - 0.25rem);
}

.btn i {
  margin-right: 5px;
}

/* ========== 卡片样式 ========== */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  overflow: hidden;
}

.card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: rgba(212, 175, 55, 0.25) !important;
  background-image: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(212, 175, 55, 0.2)) !important;
  border-bottom: 2px solid rgba(212, 175, 55, 0.5) !important;
  font-weight: 700 !important;
  color: var(--accent-color) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.card-body {
  padding: 1.5rem;
}

/* 确保卡片标题文字清晰可见 */
.card-title {
  color: var(--accent-color) !important;
  font-weight: 600 !important;
  margin-bottom: 0 !important;
}

/* 卡片标题样式 */
.card-header {
  background-color: rgba(212, 175, 55, 0.3) !important;
  background-image: linear-gradient(135deg, rgba(212, 175, 55, 0.35), rgba(212, 175, 55, 0.25)) !important;
  border-bottom: 2px solid rgba(212, 175, 55, 0.6) !important;
  color: var(--accent-color) !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.card-title {
  color: var(--accent-color) !important;
  font-weight: 600 !important;
}

/* 删除冲突的表格标题样式 */

/* 模态框标题样式 */
.modal-header {
  background-color: rgba(212, 175, 55, 0.1) !important;
  color: var(--accent-color) !important;
}

/* ========== 表单控件 ========== */
.form-control, .form-select {
  border: 1px solid #ced4da;
  border-radius: var(--border-radius);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 0.6rem 0.75rem;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

.input-group {
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
}

.input-group .form-control {
  box-shadow: none;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}

/* 删除表格基础样式 */

/* 删除冲突的表格行样式 */

/* 删除冲突的鼠标悬停效果 */



/* 删除冲突的表格单元格样式 */

/* 删除表格容器样式 */

/* ========== 删除卡片布局影响 ========== */

/* 删除表格响应式容器影响 */

/* 删除重复的表格样式 */

/* 删除固定高度表格样式 */

/* 删除表格内按钮样式 */

/* 删除表格内徽章样式 */

/* 删除表格数据对齐样式 */

/* 删除表格数字和金额列样式 */

/* ========== 徽章样式 ========== */
.badge {
  border-radius: 50rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  padding: 0.4em 0.8em;
}

.bg-warning {
  background-color: var(--primary-color) !important;
  color: var(--dark-color) !important;
}

.bg-primary {
  background-color: var(--info-color) !important;
}

.bg-success {
  background-color: var(--success-color) !important;
}

.bg-danger {
  background-color: var(--danger-color) !important;
}

/* ========== 分页样式 ========== */
.pagination {
  margin-top: 1.5rem;
}

.page-link {
  border: none;
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  margin: 0 3px;
  padding: 0.5rem 0.75rem;
}

.page-link:hover {
  background-color: rgba(212, 175, 55, 0.1);
  color: var(--primary-dark);
}

.page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--dark-color);
  font-weight: 600;
}

.page-item.disabled .page-link {
  color: #6c757d;
  opacity: 0.6;
}

/* ========== 模态框样式 ========== */
.modal-content {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modal-header {
  background-color: rgba(212, 175, 55, 0.1);
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.modal-header .modal-title {
  color: var(--accent-color);
  font-weight: 600;
}

.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* ========== 统计卡片样式 ========== */
.card.border-0.shadow-sm {
  background: linear-gradient(135deg, #ffffff, #fcfaf5);
  border: 1px solid rgba(212, 175, 55, 0.2) !important;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.1) !important;
}

.card.border-0.shadow-sm:hover {
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.2) !important;
}

/* 统计卡片标题样式 */
.card.border-0.shadow-sm .card-subtitle {
  font-weight: 700 !important;
  color: var(--accent-color) !important;
  font-size: 0.9rem !important;
}

/* 统计卡片数值样式 */
.card.border-0.shadow-sm .card-text {
  font-weight: 800 !important;
  font-size: 1.1rem !important;
}

/* 不同类型统计卡片的颜色主题 */
.stats-card-primary {
  background: linear-gradient(135deg, rgba(13, 110, 253, 0.1), rgba(13, 110, 253, 0.05)) !important;
  border-color: rgba(13, 110, 253, 0.3) !important;
}

.stats-card-success {
  background: linear-gradient(135deg, rgba(25, 135, 84, 0.1), rgba(25, 135, 84, 0.05)) !important;
  border-color: rgba(25, 135, 84, 0.3) !important;
}

.stats-card-warning {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)) !important;
  border-color: rgba(255, 193, 7, 0.3) !important;
}

.stats-card-info {
  background: linear-gradient(135deg, rgba(13, 202, 240, 0.1), rgba(13, 202, 240, 0.05)) !important;
  border-color: rgba(13, 202, 240, 0.3) !important;
}

.stats-card-secondary {
  background: linear-gradient(135deg, rgba(108, 117, 125, 0.1), rgba(108, 117, 125, 0.05)) !important;
  border-color: rgba(108, 117, 125, 0.3) !important;
}

.stats-card-danger {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05)) !important;
  border-color: rgba(220, 53, 69, 0.3) !important;
}

.card.border-0.shadow-sm:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.card .card-subtitle {
  color: var(--accent-color);
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.card .display-6 {
  color: var(--primary-dark);
  font-weight: 700;
  letter-spacing: 0.5px;
}



/* ========== 响应式调整 ========== */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  /* 删除冲突的响应式表格样式 */

  .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}

/* ========== 打印样式 ========== */
@media print {
  .btn, .sidebar, .no-print {
    display: none !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #dee2e6 !important;
  }
  
  /* 删除冲突的表头样式 */
}

/* ========== 其他元素样式 ========== */
.alert {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.alert-info {
  background-color: rgba(2, 136, 209, 0.1);
  color: #01579b;
}

.dropdown-menu {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
}

.dropdown-item:hover {
  background-color: rgba(212, 175, 55, 0.1);
}

.dropdown-item i {
  color: var(--primary-color);
}

/* 删除表格固定高度样式 */

/* 删除滚动条样式 */

/* ========== 删除Grid布局影响 ========== */

/* 无内边距的卡片主体 */
.card .card-body.p-0 {
  padding: 0 !important;
}

/* 删除表格响应式容器样式 */

/* 删除重复的表格样式 */

/* 删除表格响应式滚动条样式 */

/* 删除影响列宽的样式 */

/* 删除表格文本换行样式 */