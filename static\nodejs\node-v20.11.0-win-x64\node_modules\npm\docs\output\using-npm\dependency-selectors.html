<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>Dependency Selector Syntax &amp; Querying</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFM<PERSON>-<PERSON>, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, <PERSON><PERSON>, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="dependency-selector-syntax--querying">Dependency Selector Syntax &amp; Querying</h1>
<span class="description">Dependency Selector Syntax &amp; Querying</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="description">Description</h3>
<p>The <a href="../commands/npm-query.html"><code>npm query</code></a> command exposes a new dependency selector syntax (informed by &amp; respecting many aspects of the <a href="https://dev.w3.org/csswg/selectors4/#relational">CSS Selectors 4 Spec</a>) which:</p>
<ul>
<li>Standardizes the shape of, &amp; querying of, dependency graphs with a robust object model, metadata &amp; selector syntax</li>
<li>Leverages existing, known language syntax &amp; operators from CSS to make disparate package information broadly accessible</li>
<li>Unlocks the ability to answer complex, multi-faceted questions about dependencies, their relationships &amp; associative metadata</li>
<li>Consolidates redundant logic of similar query commands in <code>npm</code> (ex. <code>npm fund</code>, <code>npm ls</code>, <code>npm outdated</code>, <code>npm audit</code> ...)</li>
</ul>
<h3 id="dependency-selector-syntax-v100">Dependency Selector Syntax <code>v1.0.0</code></h3>
<h4 id="overview">Overview:</h4>
<ul>
<li>there is no "type" or "tag" selectors (ex. <code>div, h1, a</code>) as a dependency/target is the only type of <code>Node</code> that can be queried</li>
<li>the term "dependencies" is in reference to any <code>Node</code> found in a <code>tree</code> returned by <code>Arborist</code></li>
</ul>
<h4 id="combinators">Combinators</h4>
<ul>
<li><code>&gt;</code> direct descendant/child</li>
<li><code> </code> any descendant/child</li>
<li><code>~</code> sibling</li>
</ul>
<h4 id="selectors">Selectors</h4>
<ul>
<li><code>*</code> universal selector</li>
<li><code>#&lt;name&gt;</code> dependency selector (equivalent to <code>[name="..."]</code>)</li>
<li><code>#&lt;name&gt;@&lt;version&gt;</code> (equivalent to <code>[name=&lt;name&gt;]:semver(&lt;version&gt;)</code>)</li>
<li><code>,</code> selector list delimiter</li>
<li><code>.</code> dependency type selector</li>
<li><code>:</code> pseudo selector</li>
</ul>
<h4 id="dependency-type-selectors">Dependency Type Selectors</h4>
<ul>
<li><code>.prod</code> dependency found in the <code>dependencies</code> section of <code>package.json</code>, or is a child of said dependency</li>
<li><code>.dev</code> dependency found in the <code>devDependencies</code> section of <code>package.json</code>, or is a child of said dependency</li>
<li><code>.optional</code> dependency found in the <code>optionalDependencies</code> section of <code>package.json</code>, or has <code>"optional": true</code> set in its entry in the <code>peerDependenciesMeta</code> section of <code>package.json</code>, or a child of said dependency</li>
<li><code>.peer</code> dependency found in the <code>peerDependencies</code> section of <code>package.json</code></li>
<li><code>.workspace</code> dependency found in the <a href="https://docs.npmjs.com/cli/v8/using-npm/workspaces"><code>workspaces</code></a> section of <code>package.json</code></li>
<li><code>.bundled</code> dependency found in the <code>bundleDependencies</code> section of <code>package.json</code>, or is a child of said dependency</li>
</ul>
<h4 id="pseudo-selectors">Pseudo Selectors</h4>
<ul>
<li><a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:not"><code>:not(&lt;selector&gt;)</code></a></li>
<li><a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:has"><code>:has(&lt;selector&gt;)</code></a></li>
<li><a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:is"><code>:is(&lt;selector list&gt;)</code></a></li>
<li><a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:root"><code>:root</code></a> matches the root node/dependency</li>
<li><a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:scope"><code>:scope</code></a> matches node/dependency it was queried against</li>
<li><a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:empty"><code>:empty</code></a> when a dependency has no dependencies</li>
<li><a href="https://docs.npmjs.com/cli/v8/configuring-npm/package-json#private"><code>:private</code></a> when a dependency is private</li>
<li><code>:link</code> when a dependency is linked (for instance, workspaces or packages manually <a href="https://docs.npmjs.com/cli/v8/commands/npm-link"><code>linked</code></a></li>
<li><code>:deduped</code> when a dependency has been deduped (note that this does <em>not</em> always mean the dependency has been hoisted to the root of node_modules)</li>
<li><code>:overridden</code> when a dependency has been overridden</li>
<li><code>:extraneous</code> when a dependency exists but is not defined as a dependency of any node</li>
<li><code>:invalid</code> when a dependency version is out of its ancestors specified range</li>
<li><code>:missing</code> when a dependency is not found on disk</li>
<li><code>:semver(&lt;spec&gt;, [selector], [function])</code> match a valid <a href="https://github.com/npm/node-semver"><code>node-semver</code></a> version or range to a selector</li>
<li><code>:path(&lt;path&gt;)</code> <a href="https://www.npmjs.com/package/glob">glob</a> matching based on dependencies path relative to the project</li>
<li><code>:type(&lt;type&gt;)</code> <a href="https://github.com/npm/npm-package-arg#result-object">based on currently recognized types</a></li>
<li><code>:outdated(&lt;type&gt;)</code> when a dependency is outdated</li>
</ul>
<h5 id="semverspec-selector-function"><code>:semver(&lt;spec&gt;, [selector], [function])</code></h5>
<p>The <code>:semver()</code> pseudo selector allows comparing fields from each node's <code>package.json</code> using <a href="https://github.com/npm/node-semver#readme">semver</a> methods. It accepts up to 3 parameters, all but the first of which are optional.</p>
<ul>
<li><code>spec</code> a semver version or range</li>
<li><code>selector</code> an attribute selector for each node (default <code>[version]</code>)</li>
<li><code>function</code> a semver method to apply, one of: <code>satisfies</code>, <code>intersects</code>, <code>subset</code>, <code>gt</code>, <code>gte</code>, <code>gtr</code>, <code>lt</code>, <code>lte</code>, <code>ltr</code>, <code>eq</code>, <code>neq</code> or the special function <code>infer</code> (default <code>infer</code>)</li>
</ul>
<p>When the special <code>infer</code> function is used the <code>spec</code> and the actual value from the node are compared. If both are versions, according to <code>semver.valid()</code>, <code>eq</code> is used. If both values are ranges, according to <code>!semver.valid()</code>, <code>intersects</code> is used. If the values are mixed types <code>satisfies</code> is used.</p>
<p>Some examples:</p>
<ul>
<li><code>:semver(^1.0.0)</code> returns every node that has a <code>version</code> satisfied by the provided range <code>^1.0.0</code></li>
<li><code>:semver(16.0.0, :attr(engines, [node]))</code> returns every node which has an <code>engines.node</code> property satisfying the version <code>16.0.0</code></li>
<li><code>:semver(1.0.0, [version], lt)</code> every node with a <code>version</code> less than <code>1.0.0</code></li>
</ul>
<h5 id="outdatedtype"><code>:outdated(&lt;type&gt;)</code></h5>
<p>The <code>:outdated</code> pseudo selector retrieves data from the registry and returns information about which of your dependencies are outdated. The type parameter may be one of the following:</p>
<ul>
<li><code>any</code> (default) a version exists that is greater than the current one</li>
<li><code>in-range</code> a version exists that is greater than the current one, and satisfies at least one if its dependents</li>
<li><code>out-of-range</code> a version exists that is greater than the current one, does not satisfy at least one of its dependents</li>
<li><code>major</code> a version exists that is a semver major greater than the current one</li>
<li><code>minor</code> a version exists that is a semver minor greater than the current one</li>
<li><code>patch</code> a version exists that is a semver patch greater than the current one</li>
</ul>
<p>In addition to the filtering performed by the pseudo selector, some extra data is added to the resulting objects. The following data can be found under the <code>queryContext</code> property of each node.</p>
<ul>
<li><code>versions</code> an array of every available version of the given node</li>
<li><code>outdated.inRange</code> an array of objects, each with a <code>from</code> and <code>versions</code>, where <code>from</code> is the on-disk location of the node that depends on the current node and <code>versions</code> is an array of all available versions that satisfies that dependency. This is only populated if <code>:outdated(in-range)</code> is used.</li>
<li><code>outdated.outOfRange</code> an array of objects, identical in shape to <code>inRange</code>, but where the <code>versions</code> array is every available version that does not satisfy the dependency. This is only populated if <code>:outdated(out-of-range)</code> is used.</li>
</ul>
<p>Some examples:</p>
<ul>
<li><code>:root &gt; :outdated(major)</code> returns every direct dependency that has a new semver major release</li>
<li><code>.prod:outdated(in-range)</code> returns production dependencies that have a new release that satisfies at least one of its edges in</li>
</ul>
<h4 id="attribute-selectors"><a href="https://developer.mozilla.org/en-US/docs/Web/CSS/Attribute_selectors">Attribute Selectors</a></h4>
<p>The attribute selector evaluates the key/value pairs in <code>package.json</code> if they are <code>String</code>s.</p>
<ul>
<li><code>[]</code> attribute selector (ie. existence of attribute)</li>
<li><code>[attribute=value]</code> attribute value is equivalant...</li>
<li><code>[attribute~=value]</code> attribute value contains word...</li>
<li><code>[attribute*=value]</code> attribute value contains string...</li>
<li><code>[attribute|=value]</code> attribute value is equal to or starts with...</li>
<li><code>[attribute^=value]</code> attribute value starts with...</li>
<li><code>[attribute$=value]</code> attribute value ends with...</li>
</ul>
<h4 id="array--object-attribute-selectors"><code>Array</code> &amp; <code>Object</code> Attribute Selectors</h4>
<p>The generic <code>:attr()</code> pseudo selector standardizes a pattern which can be used for attribute selection of <code>Object</code>s, <code>Array</code>s or <code>Arrays</code> of <code>Object</code>s accessible via <code>Arborist</code>'s <code>Node.package</code> metadata. This allows for iterative attribute selection beyond top-level <code>String</code> evaluation. The last argument passed to <code>:attr()</code> must be an <code>attribute</code> selector or a nested <code>:attr()</code>. See examples below:</p>
<h4 id="objects"><code>Objects</code></h4>
<pre><code class="language-css">/* return dependencies that have a `scripts.test` containing `"tap"` */
*:attr(scripts, [test~=tap])
</code></pre>
<h4 id="nested-objects">Nested <code>Objects</code></h4>
<p>Nested objects are expressed as sequential arguments to <code>:attr()</code>.</p>
<pre><code class="language-css">/* return dependencies that have a testling config for opera browsers */
*:attr(testling, browsers, [~=opera])
</code></pre>
<h4 id="arrays"><code>Arrays</code></h4>
<p><code>Array</code>s specifically uses a special/reserved <code>.</code> character in place of a typical attribute name. <code>Arrays</code> also support exact <code>value</code> matching when a <code>String</code> is passed to the selector.</p>
<h5 id="example-of-an-array-attribute-selection">Example of an <code>Array</code> Attribute Selection:</h5>
<pre><code class="language-css">/* removes the distinction between properties &amp; arrays */
/* ie. we'd have to check the property &amp; iterate to match selection */
*:attr([keywords^=react])
*:attr(contributors, :attr([name~=Jordan]))
</code></pre>
<h5 id="example-of-an-array-matching-directly-to-a-value">Example of an <code>Array</code> matching directly to a value:</h5>
<pre><code class="language-css">/* return dependencies that have the exact keyword "react" */
/* this is equivalent to `*:keywords([value="react"])` */
*:attr([keywords=react])
</code></pre>
<h5 id="example-of-an-array-of-objects">Example of an <code>Array</code> of <code>Object</code>s:</h5>
<pre><code class="language-css">/* returns */
*:attr(contributors, [email=<EMAIL>])
</code></pre>
<h3 id="groups">Groups</h3>
<p>Dependency groups are defined by the package relationships to their ancestors (ie. the dependency types that are defined in <code>package.json</code>). This approach is user-centric as the ecosystem has been taught to think about dependencies in these groups first-and-foremost. Dependencies are allowed to be included in multiple groups (ex. a <code>prod</code> dependency may also be a <code>dev</code> dependency (in that it's also required by another <code>dev</code> dependency) &amp; may also be <code>bundled</code> - a selector for that type of dependency would look like: <code>*.prod.dev.bundled</code>).</p>
<ul>
<li><code>.prod</code></li>
<li><code>.dev</code></li>
<li><code>.optional</code></li>
<li><code>.peer</code></li>
<li><code>.bundled</code></li>
<li><code>.workspace</code></li>
</ul>
<p>Please note that currently <code>workspace</code> deps are always <code>prod</code> dependencies.  Additionally the <code>.root</code> dependency is also considered a <code>prod</code> dependency.</p>
<h3 id="programmatic-usage">Programmatic Usage</h3>
<ul>
<li><code>Arborist</code>'s <code>Node</code> Class has a <code>.querySelectorAll()</code> method
<ul>
<li>this method will return a filtered, flattened dependency Arborist <code>Node</code> list based on a valid query selector</li>
</ul>
</li>
</ul>
<pre><code class="language-js">const Arborist = require('@npmcli/arborist')
const arb = new Arborist({})
</code></pre>
<pre><code class="language-js">// root-level
arb.loadActual().then(async (tree) =&gt; {
  // query all production dependencies
  const results = await tree.querySelectorAll('.prod')
  console.log(results)
})
</code></pre>
<pre><code class="language-js">// iterative
arb.loadActual().then(async (tree) =&gt; {
  // query for the deduped version of react
  const results = await tree.querySelectorAll('#react:not(:deduped)')
  // query the deduped react for git deps
  const deps = await results[0].querySelectorAll(':type(git)')
  console.log(deps)
})
</code></pre>
<h2 id="see-also">See Also</h2>
<ul>
<li><a href="../commands/npm-query.html">npm query</a></li>
<li><a href="https://npm.im/@npmcli/arborist">@npmcli/arborist</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/using-npm/dependency-selectors.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>