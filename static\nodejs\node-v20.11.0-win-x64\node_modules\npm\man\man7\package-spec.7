.TH "PACKAGE-SPEC" "7" "November 2023" "" ""
.SH "NAME"
\fBpackage-spec\fR - Package name specifier
.SS "Description"
.P
Commands like \fBnpm install\fR and the dependency sections in the \fBpackage.json\fR use a package name specifier. This can be many different things that all refer to a "package". Examples include a package name, git url, tarball, or local directory. These will generally be referred to as \fB<package-spec>\fR in the help output for the npm commands that use this package name specifier.
.SS "Package name"
.RS 0
.IP \(bu 4
\fB\[lB]<@scope>/\[rB]<pkg>\fR
.IP \(bu 4
\fB\[lB]<@scope>/\[rB]<pkg>@<tag>\fR
.IP \(bu 4
\fB\[lB]<@scope>/\[rB]<pkg>@<version>\fR
.IP \(bu 4
\fB\[lB]<@scope>/\[rB]<pkg>@<version range>\fR
.RE 0

.P
Refers to a package by name, with or without a scope, and optionally tag, version, or version range. This is typically used in combination with the \fBregistry\fR \fI\(la/using-npm/config#registry\(ra\fR config to refer to a package in a registry.
.P
Examples:
.RS 0
.IP \(bu 4
\fBnpm\fR
.IP \(bu 4
\fB@npmcli/arborist\fR
.IP \(bu 4
\fB@npmcli/arborist@latest\fR
.IP \(bu 4
\fBnpm@6.13.1\fR
.IP \(bu 4
\fBnpm@^4.0.0\fR
.RE 0

.SS "Aliases"
.RS 0
.IP \(bu 4
\fB<alias>@npm:<name>\fR
.RE 0

.P
Primarily used by commands like \fBnpm install\fR and in the dependency sections in the \fBpackage.json\fR, this refers to a package by an alias. The \fB<alias>\fR is the name of the package as it is reified in the \fBnode_modules\fR folder, and the \fB<name>\fR refers to a package name as found in the configured registry.
.P
See \fBPackage name\fR above for more info on referring to a package by name, and \fBregistry\fR \fI\(la/using-npm/config#registry\(ra\fR for configuring which registry is used when referring to a package by name.
.P
Examples:
.RS 0
.IP \(bu 4
\fBsemver:@npm:@npmcli/semver-with-patch\fR
.IP \(bu 4
\fBsemver:@npm:semver@7.2.2\fR
.IP \(bu 4
\fBsemver:@npm:semver@legacy\fR
.RE 0

.SS "Folders"
.RS 0
.IP \(bu 4
\fB<folder>\fR
.RE 0

.P
This refers to a package on the local filesystem. Specifically this is a folder with a \fBpackage.json\fR file in it. This \fIshould\fR always be prefixed with a \fB/\fR or \fB./\fR (or your OS equivalent) to reduce confusion. npm currently will parse a string with more than one \fB/\fR in it as a folder, but this is legacy behavior that may be removed in a future version.
.P
Examples:
.RS 0
.IP \(bu 4
\fB./my-package\fR
.IP \(bu 4
\fB/opt/npm/my-package\fR
.RE 0

.SS "Tarballs"
.RS 0
.IP \(bu 4
\fB<tarball file>\fR
.IP \(bu 4
\fB<tarball url>\fR
.RE 0

.P
Examples:
.RS 0
.IP \(bu 4
\fB./my-package.tgz\fR
.IP \(bu 4
\fBhttps://registry.npmjs.org/semver/-/semver-1.0.0.tgz\fR
.RE 0

.P
Refers to a package in a tarball format, either on the local filesystem or remotely via url. This is the format that packages exist in when uploaded to a registry.
.SS "git urls"
.RS 0
.IP \(bu 4
\fB<git:// url>\fR
.IP \(bu 4
\fB<github username>/<github project>\fR
.RE 0

.P
Refers to a package in a git repo. This can be a full git url, git shorthand, or a username/package on GitHub. You can specify a git tag, branch, or other git ref by appending \fB#ref\fR.
.P
Examples:
.RS 0
.IP \(bu 4
\fBhttps://github.com/npm/cli.git\fR
.IP \(bu 4
\<EMAIL>:npm/cli.git\fR
.IP \(bu 4
\fBgit+ssh://**************/npm/cli#v6.0.0\fR
.IP \(bu 4
\fBgithub:npm/cli#HEAD\fR
.IP \(bu 4
\fBnpm/cli#c12ea07\fR
.RE 0

.SS "See also"
.RS 0
.IP \(bu 4
\fBnpm-package-arg\fR \fI\(lahttps://npm.im/npm-package-arg\(ra\fR
.IP \(bu 4
npm help scope
.IP \(bu 4
npm help config
.RE 0
