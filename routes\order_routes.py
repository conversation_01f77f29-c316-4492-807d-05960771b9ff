from flask import request, jsonify
from gold_erp_system import app, db
from gold_erp_system.models import Order, OrderItem, Payment
from datetime import datetime
from flask_login import current_user

@app.route('/orders/add', methods=['POST'])
def order_add():
    try:
        # 获取请求数据
        data = request.get_json()
        app.logger.info(f"接收到的订单数据: {data}")
        
        # 创建订单
        order = Order(
            customer_id=data.get('customerId'),
            order_number=data.get('orderNumber'),
            business_time=datetime.strptime(data.get('businessTime'), '%Y-%m-%dT%H:%M'),
            remarks=data.get('notes'),
            payable_amount=data.get('grandTotal', 0),
            is_paid=data.get('isPaid', False),
            created_by=current_user.username if current_user else 'system'
        )
        
        # 计算黄金出库、退货、收料总量
        gold_out = 0
        gold_return = 0
        gold_recycle = 0
        
        db.session.add(order)
        db.session.flush()  # 获取订单ID
        
        app.logger.info(f"创建订单成功，ID: {order.id}")
        
        # 添加订单项目
        for item_data in data.get('items', []):
            app.logger.info(f"处理订单项目: {item_data}")
            
            # 确保item_type字段存在
            item_type = item_data.get('type', 'new')
            app.logger.info(f"项目类型: {item_type}")
            
            item = OrderItem(
                order_id=order.id,
                item_type=item_type,
                product_code=item_data.get('productCode'),
                product_name=item_data.get('productName'),
                weight=item_data.get('weight', 0),
                labor_cost=item_data.get('labor', 0),
                premium_cost=item_data.get('premium', 0),
                gold_price=item_data.get('goldPrice', 0),
                amount=item_data.get('amount', 0),
                settlement_type=item_data.get('settlementType'),
                material_name=item_data.get('materialName'),
                purity=item_data.get('purity', 1),
                net_weight=item_data.get('netWeight', 0),
                material_price=item_data.get('materialPrice', 0),
                other_type=item_data.get('otherType')
            )
            
            # 根据项目类型更新黄金统计
            if item_type == 'new':
                gold_out += float(item_data.get('weight', 0))
            elif item_type == 'return':
                gold_return += float(item_data.get('weight', 0))
            elif item_type == 'old' and item_data.get('materialName', '') == '旧料足金':
                # 只有结算方式为存料、还料或结料时才计入旧料克重（黄金）
                settlement_type = item_data.get('settlementType', '')
                if settlement_type in ['store', 'return', 'settle']:
                    gold_recycle += float(item_data.get('netWeight', 0))
                
            db.session.add(item)
            app.logger.info(f"添加订单项目成功: {item}")
        
        # 更新订单的黄金出库、退货、收料总量
        order.gold_out = gold_out
        order.gold_return = gold_return
        order.gold_recycle = gold_recycle
        
        # 添加支付记录
        for payment_data in data.get('payments', []):
            app.logger.info(f"处理支付记录: {payment_data}")
            payment = Payment(
                order_id=order.id,
                payment_type=payment_data.get('type', 'cash'),
                amount=payment_data.get('amount', 0),
                payment_date=datetime.now()
            )
            db.session.add(payment)
            app.logger.info(f"添加支付记录成功: {payment}")
        
        # 提交事务
        db.session.commit()
        app.logger.info(f"订单保存成功，ID: {order.id}")
        
        return jsonify({
            'success': True,
            'message': '订单保存成功',
            'orderId': order.id
        })
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"保存订单失败: {str(e)}")
        app.logger.exception(e)  # 记录完整的异常堆栈
        return jsonify({'success': False, 'message': f'保存订单失败: {str(e)}'}), 500