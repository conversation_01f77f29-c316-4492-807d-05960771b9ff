/**
 * pinyin-pro 本地版本 - 简化版中文转拼音库
 * 基于常用汉字的拼音映射表
 */
(function() {
    'use strict';
    
    // 常用汉字拼音映射表（简化版）
    const pinyinMap = {
        // 常用姓氏
        '陈': 'chen', '李': 'li', '王': 'wang', '张': 'zhang', '刘': 'liu', '杨': 'yang', '黄': 'huang', '赵': 'zhao',
        '周': 'zhou', '吴': 'wu', '徐': 'xu', '孙': 'sun', '马': 'ma', '朱': 'zhu', '胡': 'hu', '林': 'lin',
        '郭': 'guo', '何': 'he', '高': 'gao', '罗': 'luo', '郑': 'zheng', '梁': 'liang', '谢': 'xie', '宋': 'song',
        
        // 常用名字
        '明': 'ming', '华': 'hua', '建': 'jian', '文': 'wen', '军': 'jun', '强': 'qiang', '伟': 'wei', '勇': 'yong',
        '杰': 'jie', '涛': 'tao', '超': 'chao', '辉': 'hui', '鹏': 'peng', '磊': 'lei', '娜': 'na', '敏': 'min',
        '静': 'jing', '丽': 'li', '洁': 'jie', '雅': 'ya', '芳': 'fang', '燕': 'yan', '红': 'hong', '梅': 'mei',
        
        // 金店相关词汇
        '金': 'jin', '银': 'yin', '铂': 'bo', '钻': 'zuan', '玉': 'yu', '珠': 'zhu', '宝': 'bao', '石': 'shi',
        '料': 'liao', '足': 'zu', '硬': 'ying', '软': 'ruan', '新': 'xin', '旧': 'jiu', '大': 'da', '小': 'xiao',
        '重': 'zhong', '轻': 'qing', '厚': 'hou', '薄': 'bao', '长': 'chang', '短': 'duan', '圆': 'yuan', '方': 'fang',
        
        // 业务相关词汇
        '客': 'ke', '户': 'hu', '供': 'gong', '应': 'ying', '商': 'shang', '订': 'ding', '单': 'dan', '采': 'cai',
        '购': 'gou', '销': 'xiao', '售': 'shou', '库': 'ku', '存': 'cun', '出': 'chu', '入': 'ru', '退': 'tui',
        '换': 'huan', '修': 'xiu', '理': 'li', '加': 'jia', '工': 'gong', '费': 'fei', '价': 'jia', '格': 'ge',
        
        // 数字
        '一': 'yi', '二': 'er', '三': 'san', '四': 'si', '五': 'wu', '六': 'liu', '七': 'qi', '八': 'ba',
        '九': 'jiu', '十': 'shi', '零': 'ling', '百': 'bai', '千': 'qian', '万': 'wan',
        
        // 特殊字符（您系统中的特殊客户名）
        '茹': 'ru', '茵': 'yin', '莫': 'mo', '名': 'ming', '其': 'qi', '妙': 'miao',
        '忠': 'zhong', '东': 'dong', '象': 'xiang', '信': 'xin', '达': 'da', '祥': 'xiang',
        
        // 常用字符补充
        '中': 'zhong', '国': 'guo', '人': 'ren', '民': 'min', '公': 'gong', '司': 'si', '有': 'you', '限': 'xian',
        '责': 'ze', '任': 'ren', '个': 'ge', '体': 'ti', '经': 'jing', '营': 'ying', '部': 'bu', '店': 'dian'
    };
    
    // 创建全局对象
    window.pinyinPro = {
        // 主要的pinyin函数
        pinyin: function(text, options) {
            options = options || {};
            const pattern = options.pattern || 'pinyin';
            const toneType = options.toneType || 'symbol';
            const type = options.type || 'array';
            
            if (!text) return type === 'array' ? [] : '';
            
            const result = [];
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                let pinyin = '';
                
                if (/[a-zA-Z0-9]/.test(char)) {
                    // 英文和数字直接返回
                    pinyin = char.toLowerCase();
                } else if (pinyinMap[char]) {
                    // 从映射表获取拼音
                    pinyin = pinyinMap[char];
                    
                    // 根据pattern处理
                    if (pattern === 'first') {
                        pinyin = pinyin.charAt(0);
                    }
                } else {
                    // 未知字符的处理
                    const code = char.charCodeAt(0);
                    if (code >= 0x4E00 && code <= 0x9FFF) {
                        // 中文字符，使用简单的算法生成首字母
                        const letterIndex = Math.floor((code - 0x4E00) / 800) % 26;
                        pinyin = String.fromCharCode(97 + letterIndex);
                        
                        if (pattern !== 'first') {
                            // 如果不是只要首字母，生成一个简单的拼音
                            pinyin = pinyin + 'ing';
                        }
                    } else {
                        pinyin = char;
                    }
                }
                
                result.push(pinyin);
            }
            
            return type === 'array' ? result : result.join('');
        },
        
        // 版本信息
        version: '3.19.6-local'
    };
    
    console.log('✅ 本地pinyin-pro库加载成功，版本:', window.pinyinPro.version);

    // 触发库加载完成事件
    if (typeof window.PinyinConverter !== 'undefined' && window.PinyinConverter.onLibraryLoaded) {
        window.PinyinConverter.onLibraryLoaded();
    }
    
})();
