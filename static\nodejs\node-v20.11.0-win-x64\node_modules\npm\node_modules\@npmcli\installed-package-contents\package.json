{"name": "@npmcli/installed-package-contents", "version": "2.0.2", "description": "Get the list of files installed in a package in node_modules, including bundled dependencies", "author": "GitHub Inc.", "main": "lib/index.js", "bin": {"installed-package-contents": "lib/index.js"}, "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.4", "require-inject": "^1.4.4", "tap": "^16.3.0"}, "dependencies": {"npm-bundled": "^3.0.0", "npm-normalize-package-bin": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/npm/installed-package-contents.git"}, "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.4"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}