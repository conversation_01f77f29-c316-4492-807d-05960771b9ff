-- 订单模块优化数据库迁移脚本
-- 执行日期: 2025-07-27
-- 说明: 删除冗余字段，添加贵金属统计字段，优化索引

-- 1. 删除冗余字段（如果存在）
ALTER TABLE orders DROP COLUMN IF EXISTS discount;
ALTER TABLE orders DROP COLUMN IF EXISTS net_amount;

-- 2. 添加缺失的贵金属旧料统计字段
ALTER TABLE orders ADD COLUMN IF NOT EXISTS platinum_recycle FLOAT DEFAULT 0 COMMENT '铂金旧料克数';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS silver_recycle FLOAT DEFAULT 0 COMMENT '白银旧料克数';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS jade_recycle FLOAT DEFAULT 0 COMMENT '玉石旧料克数';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS diamond_recycle FLOAT DEFAULT 0 COMMENT '钻石旧料克数';
ALTER TABLE orders ADD COLUMN IF NOT EXISTS k18_recycle FLOAT DEFAULT 0 COMMENT '18K金旧料克数';

-- 3. 添加性能优化索引
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_order_date ON orders(order_date);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_is_deleted ON orders(is_deleted);

-- 4. 为订单项表添加索引
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_item_type ON order_items(item_type);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- 5. 为支付表添加索引
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);

-- 6. 更新统计信息
ANALYZE TABLE orders;
ANALYZE TABLE order_items;
ANALYZE TABLE payments;

-- 完成迁移
SELECT 'Order optimization migration completed successfully' as status;
