.TH "SCRIPTS" "7" "November 2023" "" ""
.SH "NAME"
\fBscripts\fR - How npm handles the "scripts" field
.SS "Description"
.P
The \fB"scripts"\fR property of your \fBpackage.json\fR file supports a number of built-in scripts and their preset life cycle events as well as arbitrary scripts. These all can be executed by running \fBnpm run-script <stage>\fR or \fBnpm run <stage>\fR for short. \fIPre\fR and \fIpost\fR commands with matching names will be run for those as well (e.g. \fBpremyscript\fR, \fBmyscript\fR, \fBpostmyscript\fR). Scripts from dependencies can be run with \fBnpm explore <pkg> -- npm run <stage>\fR.
.SS "Pre & Post Scripts"
.P
To create "pre" or "post" scripts for any scripts defined in the \fB"scripts"\fR section of the \fBpackage.json\fR, simply create another script \fIwith a matching name\fR and add "pre" or "post" to the beginning of them.
.P
.RS 2
.nf
{
  "scripts": {
    "precompress": "{{ executes BEFORE the `compress` script }}",
    "compress": "{{ run command to compress files }}",
    "postcompress": "{{ executes AFTER `compress` script }}"
  }
}
.fi
.RE
.P
In this example \fBnpm run compress\fR would execute these scripts as described.
.SS "Life Cycle Scripts"
.P
There are some special life cycle scripts that happen only in certain situations. These scripts happen in addition to the \fBpre<event>\fR, \fBpost<event>\fR, and \fB<event>\fR scripts.
.RS 0
.IP \(bu 4
\fBprepare\fR, \fBprepublish\fR, \fBprepublishOnly\fR, \fBprepack\fR, \fBpostpack\fR, \fBdependencies\fR
.RE 0

.P
\fBprepare\fR (since \fBnpm@4.0.0\fR)
.RS 0
.IP \(bu 4
Runs BEFORE the package is packed, i.e. during \fBnpm publish\fR and \fBnpm pack\fR
.IP \(bu 4
Runs on local \fBnpm install\fR without any arguments
.IP \(bu 4
Runs AFTER \fBprepublish\fR, but BEFORE \fBprepublishOnly\fR
.IP \(bu 4
NOTE: If a package being installed through git contains a \fBprepare\fR script, its \fBdependencies\fR and \fBdevDependencies\fR will be installed, and the prepare script will be run, before the package is packaged and installed.
.IP \(bu 4
As of \fBnpm@7\fR these scripts run in the background. To see the output, run with: \fB--foreground-scripts\fR.
.RE 0

.P
\fBprepublish\fR (DEPRECATED)
.RS 0
.IP \(bu 4
Does not run during \fBnpm publish\fR, but does run during \fBnpm ci\fR and \fBnpm install\fR. See below for more info.
.RE 0

.P
\fBprepublishOnly\fR
.RS 0
.IP \(bu 4
Runs BEFORE the package is prepared and packed, ONLY on \fBnpm publish\fR.
.RE 0

.P
\fBprepack\fR
.RS 0
.IP \(bu 4
Runs BEFORE a tarball is packed (on "\fBnpm pack\fR", "\fBnpm publish\fR", and when installing a git dependency).
.IP \(bu 4
NOTE: "\fBnpm run pack\fR" is NOT the same as "\fBnpm pack\fR". "\fBnpm run pack\fR" is an arbitrary user defined script name, where as, "\fBnpm pack\fR" is a CLI defined command.
.RE 0

.P
\fBpostpack\fR
.RS 0
.IP \(bu 4
Runs AFTER the tarball has been generated but before it is moved to its final destination (if at all, publish does not save the tarball locally)
.RE 0

.P
\fBdependencies\fR
.RS 0
.IP \(bu 4
Runs AFTER any operations that modify the \fBnode_modules\fR directory IF changes occurred.
.IP \(bu 4
Does NOT run in global mode
.RE 0

.SS "Prepare and Prepublish"
.P
\fBDeprecation Note: prepublish\fR
.P
Since \fBnpm@1.1.71\fR, the npm CLI has run the \fBprepublish\fR script for both \fBnpm publish\fR and \fBnpm install\fR, because it's a convenient way to prepare a package for use (some common use cases are described in the section below). It has also turned out to be, in practice, \fBvery confusing\fR \fI\(lahttps://github.com/npm/npm/issues/10074\(ra\fR. As of \fBnpm@4.0.0\fR, a new event has been introduced, \fBprepare\fR, that preserves this existing behavior. A \fInew\fR event, \fBprepublishOnly\fR has been added as a transitional strategy to allow users to avoid the confusing behavior of existing npm versions and only run on \fBnpm publish\fR (for instance, running the tests one last time to ensure they're in good shape).
.P
See \fI\(lahttps://github.com/npm/npm/issues/10074\(ra\fR for a much lengthier justification, with further reading, for this change.
.P
\fBUse Cases\fR
.P
If you need to perform operations on your package before it is used, in a way that is not dependent on the operating system or architecture of the target system, use a \fBprepublish\fR script. This includes tasks such as:
.RS 0
.IP \(bu 4
Compiling CoffeeScript source code into JavaScript.
.IP \(bu 4
Creating minified versions of JavaScript source code.
.IP \(bu 4
Fetching remote resources that your package will use.
.RE 0

.P
The advantage of doing these things at \fBprepublish\fR time is that they can be done once, in a single place, thus reducing complexity and variability. Additionally, this means that:
.RS 0
.IP \(bu 4
You can depend on \fBcoffee-script\fR as a \fBdevDependency\fR, and thus your users don't need to have it installed.
.IP \(bu 4
You don't need to include minifiers in your package, reducing the size for your users.
.IP \(bu 4
You don't need to rely on your users having \fBcurl\fR or \fBwget\fR or other system tools on the target machines.
.RE 0

.SS "Dependencies"
.P
The \fBdependencies\fR script is run any time an \fBnpm\fR command causes changes to the \fBnode_modules\fR directory. It is run AFTER the changes have been applied and the \fBpackage.json\fR and \fBpackage-lock.json\fR files have been updated.
.SS "Life Cycle Operation Order"
.SS "npm help \"cache add\""
.RS 0
.IP \(bu 4
\fBprepare\fR
.RE 0

.SS "npm help ci"
.RS 0
.IP \(bu 4
\fBpreinstall\fR
.IP \(bu 4
\fBinstall\fR
.IP \(bu 4
\fBpostinstall\fR
.IP \(bu 4
\fBprepublish\fR
.IP \(bu 4
\fBpreprepare\fR
.IP \(bu 4
\fBprepare\fR
.IP \(bu 4
\fBpostprepare\fR
.RE 0

.P
These all run after the actual installation of modules into \fBnode_modules\fR, in order, with no internal actions happening in between
.SS "npm help diff"
.RS 0
.IP \(bu 4
\fBprepare\fR
.RE 0

.SS "npm help install"
.P
These also run when you run \fBnpm install -g <pkg-name>\fR
.RS 0
.IP \(bu 4
\fBpreinstall\fR
.IP \(bu 4
\fBinstall\fR
.IP \(bu 4
\fBpostinstall\fR
.IP \(bu 4
\fBprepublish\fR
.IP \(bu 4
\fBpreprepare\fR
.IP \(bu 4
\fBprepare\fR
.IP \(bu 4
\fBpostprepare\fR
.RE 0

.P
If there is a \fBbinding.gyp\fR file in the root of your package and you haven't defined your own \fBinstall\fR or \fBpreinstall\fR scripts, npm will default the \fBinstall\fR command to compile using node-gyp via \fBnode-gyp
rebuild\fR
.P
These are run from the scripts of \fB<pkg-name>\fR
.SS "npm help pack"
.RS 0
.IP \(bu 4
\fBprepack\fR
.IP \(bu 4
\fBprepare\fR
.IP \(bu 4
\fBpostpack\fR
.RE 0

.SS "npm help publish"
.RS 0
.IP \(bu 4
\fBprepublishOnly\fR
.IP \(bu 4
\fBprepack\fR
.IP \(bu 4
\fBprepare\fR
.IP \(bu 4
\fBpostpack\fR
.IP \(bu 4
\fBpublish\fR
.IP \(bu 4
\fBpostpublish\fR
.RE 0

.SS "npm help rebuild"
.RS 0
.IP \(bu 4
\fBpreinstall\fR
.IP \(bu 4
\fBinstall\fR
.IP \(bu 4
\fBpostinstall\fR
.IP \(bu 4
\fBprepare\fR
.RE 0

.P
\fBprepare\fR is only run if the current directory is a symlink (e.g. with linked packages)
.SS "npm help restart"
.P
If there is a \fBrestart\fR script defined, these events are run, otherwise \fBstop\fR and \fBstart\fR are both run if present, including their \fBpre\fR and \fBpost\fR iterations)
.RS 0
.IP \(bu 4
\fBprerestart\fR
.IP \(bu 4
\fBrestart\fR
.IP \(bu 4
\fBpostrestart\fR
.RE 0

.SS "\fB\fBnpm run <user defined>\fR\fR \fI\(la/commands/npm-run-script\(ra\fR"
.RS 0
.IP \(bu 4
\fBpre<user-defined>\fR
.IP \(bu 4
\fB<user-defined>\fR
.IP \(bu 4
\fBpost<user-defined>\fR
.RE 0

.SS "npm help start"
.RS 0
.IP \(bu 4
\fBprestart\fR
.IP \(bu 4
\fBstart\fR
.IP \(bu 4
\fBpoststart\fR
.RE 0

.P
If there is a \fBserver.js\fR file in the root of your package, then npm will default the \fBstart\fR command to \fBnode server.js\fR. \fBprestart\fR and \fBpoststart\fR will still run in this case.
.SS "npm help stop"
.RS 0
.IP \(bu 4
\fBprestop\fR
.IP \(bu 4
\fBstop\fR
.IP \(bu 4
\fBpoststop\fR
.RE 0

.SS "npm help test"
.RS 0
.IP \(bu 4
\fBpretest\fR
.IP \(bu 4
\fBtest\fR
.IP \(bu 4
\fBposttest\fR
.RE 0

.SS "npm help version"
.RS 0
.IP \(bu 4
\fBpreversion\fR
.IP \(bu 4
\fBversion\fR
.IP \(bu 4
\fBpostversion\fR
.RE 0

.SS "A Note on a lack of npm help uninstall scripts"
.P
While npm v6 had \fBuninstall\fR lifecycle scripts, npm v7 does not. Removal of a package can happen for a wide variety of reasons, and there's no clear way to currently give the script enough context to be useful.
.P
Reasons for a package removal include:
.RS 0
.IP \(bu 4
a user directly uninstalled this package
.IP \(bu 4
a user uninstalled a dependant package and so this dependency is being uninstalled
.IP \(bu 4
a user uninstalled a dependant package but another package also depends on this version
.IP \(bu 4
this version has been merged as a duplicate with another version
.IP \(bu 4
etc.
.RE 0

.P
Due to the lack of necessary context, \fBuninstall\fR lifecycle scripts are not implemented and will not function.
.SS "User"
.P
When npm is run as root, scripts are always run with the effective uid and gid of the working directory owner.
.SS "Environment"
.P
Package scripts run in an environment where many pieces of information are made available regarding the setup of npm and the current state of the process.
.SS "path"
.P
If you depend on modules that define executable scripts, like test suites, then those executables will be added to the \fBPATH\fR for executing the scripts. So, if your package.json has this:
.P
.RS 2
.nf
{
  "name" : "foo",
  "dependencies" : {
    "bar" : "0.1.x"
  },
  "scripts": {
    "start" : "bar ./test"
  }
}
.fi
.RE
.P
then you could run \fBnpm start\fR to execute the \fBbar\fR script, which is exported into the \fBnode_modules/.bin\fR directory on \fBnpm install\fR.
.SS "package.json vars"
.P
The package.json fields are tacked onto the \fBnpm_package_\fR prefix. So, for instance, if you had \fB{"name":"foo", "version":"1.2.5"}\fR in your package.json file, then your package scripts would have the \fBnpm_package_name\fR environment variable set to "foo", and the \fBnpm_package_version\fR set to "1.2.5". You can access these variables in your code with \fBprocess.env.npm_package_name\fR and \fBprocess.env.npm_package_version\fR, and so on for other fields.
.P
See \fB\fBpackage.json\fR\fR \fI\(la/configuring-npm/package-json\(ra\fR for more on package configs.
.SS "current lifecycle event"
.P
Lastly, the \fBnpm_lifecycle_event\fR environment variable is set to whichever stage of the cycle is being executed. So, you could have a single script used for different parts of the process which switches based on what's currently happening.
.P
Objects are flattened following this format, so if you had \fB{"scripts":{"install":"foo.js"}}\fR in your package.json, then you'd see this in the script:
.P
.RS 2
.nf
process.env.npm_package_scripts_install === "foo.js"
.fi
.RE
.SS "Examples"
.P
For example, if your package.json contains this:
.P
.RS 2
.nf
{
  "scripts" : {
    "install" : "scripts/install.js",
    "postinstall" : "scripts/install.js",
    "uninstall" : "scripts/uninstall.js"
  }
}
.fi
.RE
.P
then \fBscripts/install.js\fR will be called for the install and post-install stages of the lifecycle, and \fBscripts/uninstall.js\fR will be called when the package is uninstalled. Since \fBscripts/install.js\fR is running for two different phases, it would be wise in this case to look at the \fBnpm_lifecycle_event\fR environment variable.
.P
If you want to run a make command, you can do so. This works just fine:
.P
.RS 2
.nf
{
  "scripts" : {
    "preinstall" : "./configure",
    "install" : "make && make install",
    "test" : "make test"
  }
}
.fi
.RE
.SS "Exiting"
.P
Scripts are run by passing the line as a script argument to \fBsh\fR.
.P
If the script exits with a code other than 0, then this will abort the process.
.P
Note that these script files don't have to be Node.js or even JavaScript programs. They just have to be some kind of executable file.
.SS "Best Practices"
.RS 0
.IP \(bu 4
Don't exit with a non-zero error code unless you \fIreally\fR mean it. Except for uninstall scripts, this will cause the npm action to fail, and potentially be rolled back. If the failure is minor or only will prevent some optional features, then it's better to just print a warning and exit successfully.
.IP \(bu 4
Try not to use scripts to do what npm can do for you. Read through \fB\fBpackage.json\fR\fR \fI\(la/configuring-npm/package-json\(ra\fR to see all the things that you can specify and enable by simply describing your package appropriately. In general, this will lead to a more robust and consistent state.
.IP \(bu 4
Inspect the env to determine where to put things. For instance, if the \fBnpm_config_binroot\fR environment variable is set to \fB/home/<USER>/bin\fR, then don't try to install executables into \fB/usr/local/bin\fR. The user probably set it up that way for a reason.
.IP \(bu 4
Don't prefix your script commands with "sudo". If root permissions are required for some reason, then it'll fail with that error, and the user will sudo the npm command in question.
.IP \(bu 4
Don't use \fBinstall\fR. Use a \fB.gyp\fR file for compilation, and \fBprepare\fR for anything else. You should almost never have to explicitly set a preinstall or install script. If you are doing this, please consider if there is another option. The only valid use of \fBinstall\fR or \fBpreinstall\fR scripts is for compilation which must be done on the target architecture.
.IP \(bu 4
Scripts are run from the root of the package folder, regardless of what the current working directory is when \fBnpm\fR is invoked. If you want your script to use different behavior based on what subdirectory you're in, you can use the \fBINIT_CWD\fR environment variable, which holds the full path you were in when you ran \fBnpm run\fR.
.RE 0

.SS "See Also"
.RS 0
.IP \(bu 4
npm help run-script
.IP \(bu 4
\fBpackage.json\fR \fI\(la/configuring-npm/package-json\(ra\fR
.IP \(bu 4
npm help developers
.IP \(bu 4
npm help install
.RE 0
