/**
 * 纯基于pinyin-pro库的中文转拼音首字母转换器
 * 完全不使用映射表，直接调用库函数获取准确拼音
 */

(function() {
    'use strict';

    // 创建独立的命名空间，避免全局污染
    window.PinyinConverter = {
        // 标记库是否已加载
        isLibraryLoaded: false,

        // 库加载完成回调
        onLibraryLoaded: function() {
            this.isLibraryLoaded = true;
            console.log('✅ PinyinConverter已就绪，使用本地pinyin-pro库');
        },

        // 检查pinyin-pro库是否可用
        checkLibrary: function() {
            try {
                const available = typeof window.pinyinPro !== 'undefined' &&
                       typeof window.pinyinPro.pinyin === 'function';
                if (available && !this.isLibraryLoaded) {
                    this.onLibraryLoaded();
                }
                return available;
            } catch (e) {
                console.warn('pinyin-pro库检查失败:', e);
                return false;
            }
        },

        // 使用pinyin-pro库获取单个字符的拼音首字母
        getCharInitial: function(char) {
            // 处理英文和数字
            if (/[a-zA-Z0-9]/.test(char)) {
                return char.toLowerCase();
            }

            // 处理中文字符
            const code = char.charCodeAt(0);
            if (code >= 0x4E00 && code <= 0x9FFF) {
                try {
                    if (this.checkLibrary()) {
                        // 使用pinyin-pro库获取拼音首字母
                        const result = window.pinyinPro.pinyin(char, {
                            pattern: 'first',
                            toneType: 'none',
                            type: 'array'
                        });

                        if (result && result.length > 0 && result[0]) {
                            return result[0].toLowerCase();
                        }
                    }
                } catch (e) {
                    console.warn('使用pinyin-pro库转换失败:', char, e);
                }

                // 如果库不可用或转换失败，返回空字符串
                return '';
            }

            return '';
        },
        

        
        // 获取文本的拼音首字母组合
        getInitials: function(text) {
            if (!text) return '';

            let initials = '';
            for (let i = 0; i < text.length; i++) {
                const initial = this.getCharInitial(text[i]);
                if (initial) {
                    initials += initial;
                }
            }

            return initials;
        },
        
        // 智能搜索匹配函数
        smartMatch: function(text, query) {
            if (!text || !query) return false;

            const lowerText = text.toLowerCase();
            const lowerQuery = query.toLowerCase();

            // 1. 中文名称直接匹配
            if (lowerText.includes(lowerQuery)) {
                return true;
            }

            // 2. 首字母组合匹配
            const textChars = Array.from(text);
            const charInitials = textChars.map(char => this.getCharInitial(char)).filter(initial => initial);

            if (charInitials.length === 0) {
                return false;
            }

            const fullInitials = charInitials.join('');

            // 3. 首字母开头匹配
            if (fullInitials.startsWith(lowerQuery)) {
                return true;
            }

            // 4. 首字母包含匹配
            if (fullInitials.includes(lowerQuery)) {
                return true;
            }

            // 5. 单字符首字母匹配
            if (lowerQuery.length === 1) {
                return charInitials.includes(lowerQuery);
            }

            return false;
        },
        
        // 初始化函数
        init: function() {
            // 检查库是否可用
            this.isLibraryLoaded = this.checkLibrary();

            if (this.isLibraryLoaded) {
                console.log('✅ PinyinConverter初始化成功，使用pinyin-pro库进行高精度拼音转换');
            } else {
                console.log('❌ pinyin-pro库不可用，拼音转换功能将不可用');
                return;
            }

            // 测试转换功能
            this.runTests();
        },
        
        // 运行测试
        runTests: function() {
            const testCases = [
                { char: '茹', expected: 'r' },
                { char: '茵', expected: 'y' },
                { char: '莫', expected: 'm' },
                { char: '名', expected: 'm' },
                { char: '其', expected: 'q' },
                { char: '妙', expected: 'm' },
                { char: '陈', expected: 'c' },
                { char: '新', expected: 'x' },
                { char: '大', expected: 'd' },
                { char: '象', expected: 'x' }
            ];

            console.log('🧪 拼音转换测试结果:');
            let correctCount = 0;
            testCases.forEach(test => {
                const result = this.getCharInitial(test.char);
                const status = result === test.expected ? '✅' : '❌';
                if (result === test.expected) correctCount++;
                console.log(`${test.char} -> ${result} (期望: ${test.expected}) ${status}`);
            });

            // 测试完整名称
            const nameTests = [
                { name: '茹茵', expected: 'ry' },
                { name: '莫名其妙', expected: 'mmqm' },
                { name: '陈忠东', expected: 'czd' },
                { name: '新大象', expected: 'xdx' },
                { name: '信达祥', expected: 'xdx' }
            ];

            console.log('🧪 完整名称测试结果:');
            let nameCorrect = 0;
            nameTests.forEach(test => {
                const result = this.getInitials(test.name);
                const status = result === test.expected ? '✅' : '❌';
                if (result === test.expected) nameCorrect++;
                console.log(`${test.name} -> ${result} (期望: ${test.expected}) ${status}`);
            });

            // 测试搜索功能
            const searchTests = [
                { name: '茹茵', query: 'ry', shouldMatch: true },
                { name: '茹茵', query: 'r', shouldMatch: true },
                { name: '莫名其妙', query: 'mmqm', shouldMatch: true },
                { name: '莫名其妙', query: 'mm', shouldMatch: true },
                { name: '陈忠东', query: 'cz', shouldMatch: true },
                { name: '陈忠东', query: 'czd', shouldMatch: true }
            ];

            console.log('🧪 搜索功能测试结果:');
            let searchCorrect = 0;
            searchTests.forEach(test => {
                const result = this.smartMatch(test.name, test.query);
                const status = result === test.shouldMatch ? '✅' : '❌';
                if (result === test.shouldMatch) searchCorrect++;
                console.log(`搜索 "${test.query}" 在 "${test.name}" 中: ${result} (期望: ${test.shouldMatch}) ${status}`);
            });

            // 总体评估
            const totalCorrect = correctCount + nameCorrect + searchCorrect;
            const totalTests = testCases.length + nameTests.length + searchTests.length;
            const accuracy = (totalCorrect / totalTests * 100).toFixed(1);

            console.log(`📊 总体准确率: ${totalCorrect}/${totalTests} (${accuracy}%)`);

            if (accuracy >= 95) {
                console.log('✅ 拼音转换功能测试通过！');
            } else {
                console.log('❌ 拼音转换功能需要进一步优化');
            }
        }
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            // 检查库是否已加载，如果已加载则立即初始化
            if (typeof window.pinyinPro !== 'undefined') {
                window.PinyinConverter.init();
            } else {
                // 库未加载时使用最小延迟
                setTimeout(function() {
                    window.PinyinConverter.init();
                }, 10);
            }
        });
    } else {
        // 页面已加载完成，检查库状态后初始化
        if (typeof window.pinyinPro !== 'undefined') {
            window.PinyinConverter.init();
        } else {
            setTimeout(function() {
                window.PinyinConverter.init();
            }, 10);
        }
    }
})();
