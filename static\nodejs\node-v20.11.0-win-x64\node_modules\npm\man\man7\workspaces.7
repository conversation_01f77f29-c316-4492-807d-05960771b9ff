.TH "WORKSPACES" "7" "November 2023" "" ""
.SH "NAME"
\fBworkspaces\fR - Working with workspaces
.SS "Description"
.P
\fBWorkspaces\fR is a generic term that refers to the set of features in the npm cli that provides support to managing multiple packages from your local file system from within a singular top-level, root package.
.P
This set of features makes up for a much more streamlined workflow handling linked packages from the local file system. Automating the linking process as part of \fBnpm install\fR and avoiding manually having to use \fBnpm link\fR in order to add references to packages that should be symlinked into the current \fBnode_modules\fR folder.
.P
We also refer to these packages being auto-symlinked during \fBnpm install\fR as a single \fBworkspace\fR, meaning it's a nested package within the current local file system that is explicitly defined in the \fB\fBpackage.json\fR\fR \fI\(la/configuring-npm/package-json#workspaces\(ra\fR \fBworkspaces\fR configuration.
.SS "Defining workspaces"
.P
Workspaces are usually defined via the \fBworkspaces\fR property of the \fB\fBpackage.json\fR\fR \fI\(la/configuring-npm/package-json#workspaces\(ra\fR file, e.g:
.P
.RS 2
.nf
{
  "name": "my-workspaces-powered-project",
  "workspaces": \[lB]
    "packages/a"
  \[rB]
}
.fi
.RE
.P
Given the above \fBpackage.json\fR example living at a current working directory \fB.\fR that contains a folder named \fBpackages/a\fR that itself contains a \fBpackage.json\fR inside it, defining a Node.js package, e.g:
.P
.RS 2
.nf
.
+-- package.json
`-- packages
   +-- a
   |   `-- package.json
.fi
.RE
.P
The expected result once running \fBnpm install\fR in this current working directory \fB.\fR is that the folder \fBpackages/a\fR will get symlinked to the \fBnode_modules\fR folder of the current working dir.
.P
Below is a post \fBnpm install\fR example, given that same previous example structure of files and folders:
.P
.RS 2
.nf
.
+-- node_modules
|  `-- a -> ../packages/a
+-- package-lock.json
+-- package.json
`-- packages
   +-- a
   |   `-- package.json
.fi
.RE
.SS "Getting started with workspaces"
.P
You may automate the required steps to define a new workspace using npm help init. For example in a project that already has a \fBpackage.json\fR defined you can run:
.P
.RS 2
.nf
npm init -w ./packages/a
.fi
.RE
.P
This command will create the missing folders and a new \fBpackage.json\fR file (if needed) while also making sure to properly configure the \fB"workspaces"\fR property of your root project \fBpackage.json\fR.
.SS "Adding dependencies to a workspace"
.P
It's possible to directly add/remove/update dependencies of your workspaces using the \fB\fBworkspace\fR config\fR \fI\(la/using-npm/config#workspace\(ra\fR.
.P
For example, assuming the following structure:
.P
.RS 2
.nf
.
+-- package.json
`-- packages
   +-- a
   |   `-- package.json
   `-- b
       `-- package.json
.fi
.RE
.P
If you want to add a dependency named \fBabbrev\fR from the registry as a dependency of your workspace \fBa\fR, you may use the workspace config to tell the npm installer that package should be added as a dependency of the provided workspace:
.P
.RS 2
.nf
npm install abbrev -w a
.fi
.RE
.P
Note: other installing commands such as \fBuninstall\fR, \fBci\fR, etc will also respect the provided \fBworkspace\fR configuration.
.SS "Using workspaces"
.P
Given the \fBspecifities of how Node.js handles module resolution\fR \fI\(lahttps://nodejs.org/dist/latest-v14.x/docs/api/modules.html#modules_all_together\(ra\fR it's possible to consume any defined workspace by its declared \fBpackage.json\fR \fBname\fR. Continuing from the example defined above, let's also create a Node.js script that will require the workspace \fBa\fR example module, e.g:
.P
.RS 2
.nf
// ./packages/a/index.js
module.exports = 'a'

// ./lib/index.js
const moduleA = require('a')
console.log(moduleA) // -> a
.fi
.RE
.P
When running it with:
.P
\fBnode lib/index.js\fR
.P
This demonstrates how the nature of \fBnode_modules\fR resolution allows for \fBworkspaces\fR to enable a portable workflow for requiring each \fBworkspace\fR in such a way that is also easy to npm help publish these nested workspaces to be consumed elsewhere.
.SS "Running commands in the context of workspaces"
.P
You can use the \fBworkspace\fR configuration option to run commands in the context of a configured workspace. Additionally, if your current directory is in a workspace, the \fBworkspace\fR configuration is implicitly set, and \fBprefix\fR is set to the root workspace.
.P
Following is a quick example on how to use the \fBnpm run\fR command in the context of nested workspaces. For a project containing multiple workspaces, e.g:
.P
.RS 2
.nf
.
+-- package.json
`-- packages
   +-- a
   |   `-- package.json
   `-- b
       `-- package.json
.fi
.RE
.P
By running a command using the \fBworkspace\fR option, it's possible to run the given command in the context of that specific workspace. e.g:
.P
.RS 2
.nf
npm run test --workspace=a
.fi
.RE
.P
You could also run the command within the workspace.
.P
.RS 2
.nf
cd packages/a && npm run test
.fi
.RE
.P
Either will run the \fBtest\fR script defined within the \fB./packages/a/package.json\fR file.
.P
Please note that you can also specify this argument multiple times in the command-line in order to target multiple workspaces, e.g:
.P
.RS 2
.nf
npm run test --workspace=a --workspace=b
.fi
.RE
.P
Or run the command for each workspace within the 'packages' folder:
.P
.RS 2
.nf
npm run test --workspace=packages
.fi
.RE
.P
It's also possible to use the \fBworkspaces\fR (plural) configuration option to enable the same behavior but running that command in the context of \fBall\fR configured workspaces. e.g:
.P
.RS 2
.nf
npm run test --workspaces
.fi
.RE
.P
Will run the \fBtest\fR script in both \fB./packages/a\fR and \fB./packages/b\fR.
.P
Commands will be run in each workspace in the order they appear in your \fBpackage.json\fR
.P
.RS 2
.nf
{
  "workspaces": \[lB] "packages/a", "packages/b" \[rB]
}
.fi
.RE
.P
Order of run is different with:
.P
.RS 2
.nf
{
  "workspaces": \[lB] "packages/b", "packages/a" \[rB]
}
.fi
.RE
.SS "Ignoring missing scripts"
.P
It is not required for all of the workspaces to implement scripts run with the \fBnpm run\fR command.
.P
By running the command with the \fB--if-present\fR flag, npm will ignore workspaces missing target script.
.P
.RS 2
.nf
npm run test --workspaces --if-present
.fi
.RE
.SS "See also"
.RS 0
.IP \(bu 4
npm help install
.IP \(bu 4
npm help publish
.IP \(bu 4
npm help run-script
.IP \(bu 4
npm help config
.RE 0
