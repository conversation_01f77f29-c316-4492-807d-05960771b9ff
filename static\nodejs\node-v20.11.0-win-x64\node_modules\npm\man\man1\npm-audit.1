.TH "NPM-AUDIT" "1" "November 2023" "" ""
.SH "NAME"
\fBnpm-audit\fR - Run a security audit
.SS "Synopsis"
.P
.RS 2
.nf
npm audit \[lB]fix|signatures\[rB]
.fi
.RE
.SS "Description"
.P
The audit command submits a description of the dependencies configured in your project to your default registry and asks for a report of known vulnerabilities. If any vulnerabilities are found, then the impact and appropriate remediation will be calculated. If the \fBfix\fR argument is provided, then remediations will be applied to the package tree.
.P
The command will exit with a 0 exit code if no vulnerabilities were found.
.P
Note that some vulnerabilities cannot be fixed automatically and will require manual intervention or review. Also note that since \fBnpm audit
fix\fR runs a full-fledged \fBnpm install\fR under the hood, all configs that apply to the installer will also apply to \fBnpm install\fR -- so things like \fBnpm audit fix --package-lock-only\fR will work as expected.
.P
By default, the audit command will exit with a non-zero code if any vulnerability is found. It may be useful in CI environments to include the \fB--audit-level\fR parameter to specify the minimum vulnerability level that will cause the command to fail. This option does not filter the report output, it simply changes the command's failure threshold.
.SS "Package lock"
.P
By default npm requires a package-lock or shrinkwrap in order to run the audit. You can bypass the package lock with \fB--no-package-lock\fR but be aware the results may be different with every run, since npm will re-build the dependency tree each time.
.SS "Audit Signatures"
.P
To ensure the integrity of packages you download from the public npm registry, or any registry that supports signatures, you can verify the registry signatures of downloaded packages using the npm CLI.
.P
Registry signatures can be verified using the following \fBaudit\fR command:
.P
.RS 2
.nf
$ npm audit signatures
.fi
.RE
.P
The npm CLI supports registry signatures and signing keys provided by any registry if the following conventions are followed:
.RS 0
.IP 1. 4
Signatures are provided in the package's \fBpackument\fR in each published version within the \fBdist\fR object:
.RE 0

.P
.RS 2
.nf
"dist":{
  "..omitted..": "..omitted..",
  "signatures": \[lB]{
    "keyid": "SHA256:{{SHA256_PUBLIC_KEY}}",
    "sig": "a312b9c3cb4a1b693e8ebac5ee1ca9cc01f2661c14391917dcb111517f72370809..."
  }\[rB]
}
.fi
.RE
.P
See this \fBexample\fR \fI\(lahttps://registry.npmjs.org/light-cycle/1.4.3\(ra\fR of a signed package from the public npm registry.
.P
The \fBsig\fR is generated using the following template: \fB${package.name}@${package.version}:${package.dist.integrity}\fR and the \fBkeyid\fR has to match one of the public signing keys below.
.RS 0
.IP 2. 4
Public signing keys are provided at \fBregistry-host.tld/-/npm/v1/keys\fR in the following format:
.RE 0

.P
.RS 2
.nf
{
  "keys": \[lB]{
    "expires": null,
    "keyid": "SHA256:{{SHA256_PUBLIC_KEY}}",
    "keytype": "ecdsa-sha2-nistp256",
    "scheme": "ecdsa-sha2-nistp256",
    "key": "{{B64_PUBLIC_KEY}}"
  }\[rB]
}
.fi
.RE
.P
Keys response:
.RS 0
.IP \(bu 4
\fBexpires\fR: null or a simplified extended \fBISO 8601 format\fR \fI\(lahttps://en.wikipedia.org/wiki/ISO_8601"\(ra\fR: \fBYYYY-MM-DDTHH:mm:ss.sssZ\fR
.IP \(bu 4
\fBkeydid\fR: sha256 fingerprint of the public key
.IP \(bu 4
\fBkeytype\fR: only \fBecdsa-sha2-nistp256\fR is currently supported by the npm CLI
.IP \(bu 4
\fBscheme\fR: only \fBecdsa-sha2-nistp256\fR is currently supported by the npm CLI
.IP \(bu 4
\fBkey\fR: base64 encoded public key
.RE 0

.P
See this \fBexample key's response from the public npm registry\fR \fI\(lahttps://registry.npmjs.org/-/npm/v1/keys"\(ra\fR.
.SS "Audit Endpoints"
.P
There are two audit endpoints that npm may use to fetch vulnerability information: the \fBBulk Advisory\fR endpoint and the \fBQuick Audit\fR endpoint.
.SS "Bulk Advisory Endpoint"
.P
As of version 7, npm uses the much faster \fBBulk Advisory\fR endpoint to optimize the speed of calculating audit results.
.P
npm will generate a JSON payload with the name and list of versions of each package in the tree, and POST it to the default configured registry at the path \fB/-/npm/v1/security/advisories/bulk\fR.
.P
Any packages in the tree that do not have a \fBversion\fR field in their package.json file will be ignored. If any \fB--omit\fR options are specified (either via the \fB\fB--omit\fR config\fR \fI\(la/using-npm/config#omit\(ra\fR, or one of the shorthands such as \fB--production\fR, \fB--only=dev\fR, and so on), then packages will be omitted from the submitted payload as appropriate.
.P
If the registry responds with an error, or with an invalid response, then npm will attempt to load advisory data from the \fBQuick Audit\fR endpoint.
.P
The expected result will contain a set of advisory objects for each dependency that matches the advisory range. Each advisory object contains a \fBname\fR, \fBurl\fR, \fBid\fR, \fBseverity\fR, \fBvulnerable_versions\fR, and \fBtitle\fR.
.P
npm then uses these advisory objects to calculate vulnerabilities and meta-vulnerabilities of the dependencies within the tree.
.SS "Quick Audit Endpoint"
.P
If the \fBBulk Advisory\fR endpoint returns an error, or invalid data, npm will attempt to load advisory data from the \fBQuick Audit\fR endpoint, which is considerably slower in most cases.
.P
The full package tree as found in \fBpackage-lock.json\fR is submitted, along with the following pieces of additional metadata:
.RS 0
.IP \(bu 4
\fBnpm_version\fR
.IP \(bu 4
\fBnode_version\fR
.IP \(bu 4
\fBplatform\fR
.IP \(bu 4
\fBarch\fR
.IP \(bu 4
\fBnode_env\fR
.RE 0

.P
All packages in the tree are submitted to the Quick Audit endpoint. Omitted dependency types are skipped when generating the report.
.SS "Scrubbing"
.P
Out of an abundance of caution, npm versions 5 and 6 would "scrub" any packages from the submitted report if their name contained a \fB/\fR character, so as to avoid leaking the names of potentially private packages or git URLs.
.P
However, in practice, this resulted in audits often failing to properly detect meta-vulnerabilities, because the tree would appear to be invalid due to missing dependencies, and prevented the detection of vulnerabilities in package trees that used git dependencies or private modules.
.P
This scrubbing has been removed from npm as of version 7.
.SS "Calculating Meta-Vulnerabilities and Remediations"
.P
npm uses the \fB\fB@npmcli/metavuln-calculator\fR\fR \fI\(lahttp://npm.im/@npmcli/metavuln-calculator\(ra\fR module to turn a set of security advisories into a set of "vulnerability" objects. A "meta-vulnerability" is a dependency that is vulnerable by virtue of dependence on vulnerable versions of a vulnerable package.
.P
For example, if the package \fBfoo\fR is vulnerable in the range \fB>=1.0.2
<2.0.0\fR, and the package \fBbar\fR depends on \fBfoo@^1.1.0\fR, then that version of \fBbar\fR can only be installed by installing a vulnerable version of \fBfoo\fR. In this case, \fBbar\fR is a "metavulnerability".
.P
Once metavulnerabilities for a given package are calculated, they are cached in the \fB~/.npm\fR folder and only re-evaluated if the advisory range changes, or a new version of the package is published (in which case, the new version is checked for metavulnerable status as well).
.P
If the chain of metavulnerabilities extends all the way to the root project, and it cannot be updated without changing its dependency ranges, then \fBnpm audit fix\fR will require the \fB--force\fR option to apply the remediation. If remediations do not require changes to the dependency ranges, then all vulnerable packages will be updated to a version that does not have an advisory or metavulnerability posted against it.
.SS "Exit Code"
.P
The \fBnpm audit\fR command will exit with a 0 exit code if no vulnerabilities were found. The \fBnpm audit fix\fR command will exit with 0 exit code if no vulnerabilities are found \fIor\fR if the remediation is able to successfully fix all vulnerabilities.
.P
If vulnerabilities were found the exit code will depend on the \fB\fBaudit-level\fR config\fR \fI\(la/using-npm/config#audit-level\(ra\fR.
.SS "Examples"
.P
Scan your project for vulnerabilities and automatically install any compatible updates to vulnerable dependencies:
.P
.RS 2
.nf
$ npm audit fix
.fi
.RE
.P
Run \fBaudit fix\fR without modifying \fBnode_modules\fR, but still updating the pkglock:
.P
.RS 2
.nf
$ npm audit fix --package-lock-only
.fi
.RE
.P
Skip updating \fBdevDependencies\fR:
.P
.RS 2
.nf
$ npm audit fix --only=prod
.fi
.RE
.P
Have \fBaudit fix\fR install SemVer-major updates to toplevel dependencies, not just SemVer-compatible ones:
.P
.RS 2
.nf
$ npm audit fix --force
.fi
.RE
.P
Do a dry run to get an idea of what \fBaudit fix\fR will do, and \fIalso\fR output install information in JSON format:
.P
.RS 2
.nf
$ npm audit fix --dry-run --json
.fi
.RE
.P
Scan your project for vulnerabilities and just show the details, without fixing anything:
.P
.RS 2
.nf
$ npm audit
.fi
.RE
.P
Get the detailed audit report in JSON format:
.P
.RS 2
.nf
$ npm audit --json
.fi
.RE
.P
Fail an audit only if the results include a vulnerability with a level of moderate or higher:
.P
.RS 2
.nf
$ npm audit --audit-level=moderate
.fi
.RE
.SS "Configuration"
.SS "\fBaudit-level\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null, "info", "low", "moderate", "high", "critical", or "none"
.RE 0

.P
The minimum level of vulnerability for \fBnpm audit\fR to exit with a non-zero exit code.
.SS "\fBdry-run\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Indicates that you don't want npm to make any changes and that it should only report what it would have done. This can be passed into any of the commands that modify your local installation, eg, \fBinstall\fR, \fBupdate\fR, \fBdedupe\fR, \fBuninstall\fR, as well as \fBpack\fR and \fBpublish\fR.
.P
Note: This is NOT honored by other network related commands, eg \fBdist-tags\fR, \fBowner\fR, etc.
.SS "\fBforce\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Removes various protections against unfortunate side effects, common mistakes, unnecessary performance degradation, and malicious input.
.RS 0
.IP \(bu 4
Allow clobbering non-npm files in global installs.
.IP \(bu 4
Allow the \fBnpm version\fR command to work on an unclean git repository.
.IP \(bu 4
Allow deleting the cache folder with \fBnpm cache clean\fR.
.IP \(bu 4
Allow installing packages that have an \fBengines\fR declaration requiring a different version of npm.
.IP \(bu 4
Allow installing packages that have an \fBengines\fR declaration requiring a different version of \fBnode\fR, even if \fB--engine-strict\fR is enabled.
.IP \(bu 4
Allow \fBnpm audit fix\fR to install modules outside your stated dependency range (including SemVer-major changes).
.IP \(bu 4
Allow unpublishing all versions of a published package.
.IP \(bu 4
Allow conflicting peerDependencies to be installed in the root project.
.IP \(bu 4
Implicitly set \fB--yes\fR during \fBnpm init\fR.
.IP \(bu 4
Allow clobbering existing values in \fBnpm pkg\fR
.IP \(bu 4
Allow unpublishing of entire packages (not just a single version).
.RE 0

.P
If you don't have a clear idea of what you want to do, it is strongly recommended that you do not use this option!
.SS "\fBjson\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Whether or not to output JSON data, rather than the normal output.
.RS 0
.IP \(bu 4
In \fBnpm pkg set\fR it enables parsing set values with JSON.parse() before saving them to your \fBpackage.json\fR.
.RE 0

.P
Not supported by all npm commands.
.SS "\fBpackage-lock-only\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to true, the current operation will only use the \fBpackage-lock.json\fR, ignoring \fBnode_modules\fR.
.P
For \fBupdate\fR this means only the \fBpackage-lock.json\fR will be updated, instead of checking \fBnode_modules\fR and downloading dependencies.
.P
For \fBlist\fR this means the output will be based on the tree described by the \fBpackage-lock.json\fR, rather than the contents of \fBnode_modules\fR.
.SS "\fBpackage-lock\fR"
.RS 0
.IP \(bu 4
Default: true
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to false, then ignore \fBpackage-lock.json\fR files when installing. This will also prevent \fIwriting\fR \fBpackage-lock.json\fR if \fBsave\fR is true.
.SS "\fBomit\fR"
.RS 0
.IP \(bu 4
Default: 'dev' if the \fBNODE_ENV\fR environment variable is set to 'production', otherwise empty.
.IP \(bu 4
Type: "dev", "optional", or "peer" (can be set multiple times)
.RE 0

.P
Dependency types to omit from the installation tree on disk.
.P
Note that these dependencies \fIare\fR still resolved and added to the \fBpackage-lock.json\fR or \fBnpm-shrinkwrap.json\fR file. They are just not physically installed on disk.
.P
If a package type appears in both the \fB--include\fR and \fB--omit\fR lists, then it will be included.
.P
If the resulting omit list includes \fB'dev'\fR, then the \fBNODE_ENV\fR environment variable will be set to \fB'production'\fR for all lifecycle scripts.
.SS "\fBinclude\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: "prod", "dev", "optional", or "peer" (can be set multiple times)
.RE 0

.P
Option that allows for defining which types of dependencies to install.
.P
This is the inverse of \fB--omit=<type>\fR.
.P
Dependency types specified in \fB--include\fR will not be omitted, regardless of the order in which omit/include are specified on the command-line.
.SS "\fBforeground-scripts\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Run all build scripts (ie, \fBpreinstall\fR, \fBinstall\fR, and \fBpostinstall\fR) scripts for installed packages in the foreground process, sharing standard input, output, and error with the main npm process.
.P
Note that this will generally make installs run slower, and be much noisier, but can be useful for debugging.
.SS "\fBignore-scripts\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, npm does not run scripts specified in package.json files.
.P
Note that commands explicitly intended to run a particular script, such as \fBnpm start\fR, \fBnpm stop\fR, \fBnpm restart\fR, \fBnpm test\fR, and \fBnpm run-script\fR will still run their intended script if \fBignore-scripts\fR is set, but they will \fInot\fR run any pre- or post-scripts.
.SS "\fBworkspace\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
Enable running a command in the context of the configured workspaces of the current project while filtering by running only the workspaces defined by this configuration option.
.P
Valid values for the \fBworkspace\fR config are either:
.RS 0
.IP \(bu 4
Workspace names
.IP \(bu 4
Path to a workspace directory
.IP \(bu 4
Path to a parent workspace directory (will result in selecting all workspaces within that folder)
.RE 0

.P
When set for the \fBnpm init\fR command, this may be set to the folder of a workspace which does not yet exist, to create the folder and set it up as a brand new workspace within the project.
.P
This value is not exported to the environment for child processes.
.SS "\fBworkspaces\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Set to true to run the command in the context of \fBall\fR configured workspaces.
.P
Explicitly setting this to false will cause commands like \fBinstall\fR to ignore workspaces altogether. When not set explicitly:
.RS 0
.IP \(bu 4
Commands that operate on the \fBnode_modules\fR tree (install, update, etc.) will link workspaces into the \fBnode_modules\fR folder. - Commands that do other things (test, exec, publish, etc.) will operate on the root project, \fIunless\fR one or more workspaces are specified in the \fBworkspace\fR config.
.RE 0

.P
This value is not exported to the environment for child processes.
.SS "\fBinclude-workspace-root\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Include the workspace root when workspaces are enabled for a command.
.P
When false, specifying individual workspaces via the \fBworkspace\fR config, or all workspaces via the \fBworkspaces\fR flag, will cause npm to operate only on the specified workspaces, and not on the root project.
.P
This value is not exported to the environment for child processes.
.SS "\fBinstall-links\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
When set file: protocol dependencies will be packed and installed as regular dependencies instead of creating a symlink. This option has no effect on workspaces.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help install
.IP \(bu 4
npm help config
.RE 0
