<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-sbom</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="npm-sbom">npm-sbom</h1>
<span class="description">Generate a Software Bill of Materials (SBOM)</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm sbom
</code></pre>
<h3 id="description">Description</h3>
<p>The <code>npm sbom</code> command generates a Software Bill of Materials (SBOM) listing the
dependencies for the current project. SBOMs can be generated in either
<a href="https://spdx.dev/">SPDX</a> or <a href="https://cyclonedx.org/">CycloneDX</a> format.</p>
<h3 id="example-cyclonedx-sbom">Example CycloneDX SBOM</h3>
<pre><code class="language-json">{
  "$schema": "http://cyclonedx.org/schema/bom-1.5.schema.json",
  "bomFormat": "CycloneDX",
  "specVersion": "1.5",
  "serialNumber": "urn:uuid:09f55116-97e1-49cf-b3b8-44d0207e7730",
  "version": 1,
  "metadata": {
    "timestamp": "2023-09-01T00:00:00.001Z",
    "lifecycles": [
      {
        "phase": "build"
      }
    ],
    "tools": [
      {
        "vendor": "npm",
        "name": "cli",
        "version": "10.1.0"
      }
    ],
    "component": {
      "bom-ref": "simple@1.0.0",
      "type": "library",
      "name": "simple",
      "version": "1.0.0",
      "scope": "required",
      "author": "John Doe",
      "description": "simple react app",
      "purl": "pkg:npm/simple@1.0.0",
      "properties": [
        {
          "name": "cdx:npm:package:path",
          "value": ""
        }
      ],
      "externalReferences": [],
      "licenses": [
        {
          "license": {
            "id": "MIT"
          }
        }
      ]
    }
  },
  "components": [
    {
      "bom-ref": "lodash@4.17.21",
      "type": "library",
      "name": "lodash",
      "version": "4.17.21",
      "scope": "required",
      "author": "John-David Dalton",
      "description": "Lodash modular utilities.",
      "purl": "pkg:npm/lodash@4.17.21",
      "properties": [
        {
          "name": "cdx:npm:package:path",
          "value": "node_modules/lodash"
        }
      ],
      "externalReferences": [
        {
          "type": "distribution",
          "url": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
        },
        {
          "type": "vcs",
          "url": "git+https://github.com/lodash/lodash.git"
        },
        {
          "type": "website",
          "url": "https://lodash.com/"
        },
        {
          "type": "issue-tracker",
          "url": "https://github.com/lodash/lodash/issues"
        }
      ],
      "hashes": [
        {
          "alg": "SHA-512",
          "content": "bf690311ee7b95e713ba568322e3533f2dd1cb880b189e99d4edef13592b81764daec43e2c54c61d5c558dc5cfb35ecb85b65519e74026ff17675b6f8f916f4a"
        }
      ],
      "licenses": [
        {
          "license": {
            "id": "MIT"
          }
        }
      ]
    }
  ],
  "dependencies": [
    {
      "ref": "simple@1.0.0",
      "dependsOn": [
        "lodash@4.17.21"
      ]
    },
    {
      "ref": "lodash@4.17.21",
      "dependsOn": []
    }
  ]
}
</code></pre>
<h3 id="example-spdx-sbom">Example SPDX SBOM</h3>
<pre><code class="language-json">{
  "spdxVersion": "SPDX-2.3",
  "dataLicense": "CC0-1.0",
  "SPDXID": "SPDXRef-DOCUMENT",
  "name": "simple@1.0.0",
  "documentNamespace": "http://spdx.org/spdxdocs/simple-1.0.0-bf81090e-8bbc-459d-bec9-abeb794e096a",
  "creationInfo": {
    "created": "2023-09-01T00:00:00.001Z",
    "creators": [
      "Tool: npm/cli-10.1.0"
    ]
  },
  "documentDescribes": [
    "SPDXRef-Package-simple-1.0.0"
  ],
  "packages": [
    {
      "name": "simple",
      "SPDXID": "SPDXRef-Package-simple-1.0.0",
      "versionInfo": "1.0.0",
      "packageFileName": "",
      "description": "simple react app",
      "primaryPackagePurpose": "LIBRARY",
      "downloadLocation": "NOASSERTION",
      "filesAnalyzed": false,
      "homepage": "NOASSERTION",
      "licenseDeclared": "MIT",
      "externalRefs": [
        {
          "referenceCategory": "PACKAGE-MANAGER",
          "referenceType": "purl",
          "referenceLocator": "pkg:npm/simple@1.0.0"
        }
      ]
    },
    {
      "name": "lodash",
      "SPDXID": "SPDXRef-Package-lodash-4.17.21",
      "versionInfo": "4.17.21",
      "packageFileName": "node_modules/lodash",
      "description": "Lodash modular utilities.",
      "downloadLocation": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz",
      "filesAnalyzed": false,
      "homepage": "https://lodash.com/",
      "licenseDeclared": "MIT",
      "externalRefs": [
        {
          "referenceCategory": "PACKAGE-MANAGER",
          "referenceType": "purl",
          "referenceLocator": "pkg:npm/lodash@4.17.21"
        }
      ],
      "checksums": [
        {
          "algorithm": "SHA512",
          "checksumValue": "bf690311ee7b95e713ba568322e3533f2dd1cb880b189e99d4edef13592b81764daec43e2c54c61d5c558dc5cfb35ecb85b65519e74026ff17675b6f8f916f4a"
        }
      ]
    }
  ],
  "relationships": [
    {
      "spdxElementId": "SPDXRef-DOCUMENT",
      "relatedSpdxElement": "SPDXRef-Package-simple-1.0.0",
      "relationshipType": "DESCRIBES"
    },
    {
      "spdxElementId": "SPDXRef-Package-simple-1.0.0",
      "relatedSpdxElement": "SPDXRef-Package-lodash-4.17.21",
      "relationshipType": "DEPENDS_ON"
    }
  ]
}
</code></pre>
<h3 id="package-lock-only-mode">Package lock only mode</h3>
<p>If package-lock-only is enabled, only the information in the package
lock (or shrinkwrap) is loaded.  This means that information from the
package.json files of your dependencies will not be included in the
result set (e.g. description, homepage, engines).</p>
<h3 id="configuration">Configuration</h3>
<h4 id="omit"><code>omit</code></h4>
<ul>
<li>Default: 'dev' if the <code>NODE_ENV</code> environment variable is set to
'production', otherwise empty.</li>
<li>Type: "dev", "optional", or "peer" (can be set multiple times)</li>
</ul>
<p>Dependency types to omit from the installation tree on disk.</p>
<p>Note that these dependencies <em>are</em> still resolved and added to the
<code>package-lock.json</code> or <code>npm-shrinkwrap.json</code> file. They are just not
physically installed on disk.</p>
<p>If a package type appears in both the <code>--include</code> and <code>--omit</code> lists, then
it will be included.</p>
<p>If the resulting omit list includes <code>'dev'</code>, then the <code>NODE_ENV</code> environment
variable will be set to <code>'production'</code> for all lifecycle scripts.</p>
<h4 id="package-lock-only"><code>package-lock-only</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>If set to true, the current operation will only use the <code>package-lock.json</code>,
ignoring <code>node_modules</code>.</p>
<p>For <code>update</code> this means only the <code>package-lock.json</code> will be updated,
instead of checking <code>node_modules</code> and downloading dependencies.</p>
<p>For <code>list</code> this means the output will be based on the tree described by the
<code>package-lock.json</code>, rather than the contents of <code>node_modules</code>.</p>
<h4 id="sbom-format"><code>sbom-format</code></h4>
<ul>
<li>Default: null</li>
<li>Type: "cyclonedx" or "spdx"</li>
</ul>
<p>SBOM format to use when generating SBOMs.</p>
<h4 id="sbom-type"><code>sbom-type</code></h4>
<ul>
<li>Default: "library"</li>
<li>Type: "library", "application", or "framework"</li>
</ul>
<p>The type of package described by the generated SBOM. For SPDX, this is the
value for the <code>primaryPackagePurpose</code> fieled. For CycloneDX, this is the
value for the <code>type</code> field.</p>
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<h2 id="see-also">See Also</h2>
<ul>
<li><a href="../using-npm/package-spec.html">package spec</a></li>
<li><a href="../using-npm/dependency-selectors.html">dependency selectors</a></li>
<li><a href="../configuring-npm/package-json.html">package.json</a></li>
<li><a href="../using-npm/workspaces.html">workspaces</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-sbom.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>