/**
 * 金色ERP系统 - 主JavaScript文件
 * 包含全局交互和动画效果
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有工具提示
    initTooltips();
    
    // 初始化侧边栏交互
    initSidebar();
    

    
    // 初始化表单验证
    initFormValidation();
    
    // 初始化数据筛选面板交互
    initFilterPanels();
    
    // 初始化模态框效果
    initModals();
});

/**
 * 初始化所有工具提示组件
 */
function initTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            delay: { show: 300, hide: 100 }
        });
    });
}

/**
 * 侧边栏交互效果
 */
function initSidebar() {
    // 移动端侧边栏切换
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('show');
        });
    }
    
    // 侧边栏菜单项悬停效果
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.paddingLeft = '25px';
        });
        
        link.addEventListener('mouseleave', function() {
            this.style.paddingLeft = '';
        });
    });

    // 添加当前页面高亮
    highlightCurrentPage();
}

/**
 * 高亮当前页面对应的侧边栏菜单
 */
function highlightCurrentPage() {
    // 获取当前路径
    const currentPath = window.location.pathname;
    
    // 寻找匹配的导航链接
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href) && href !== '/') {
            link.classList.add('active');
            
            // 如果在折叠菜单中，展开父菜单
            const parentCollapse = link.closest('.collapse');
            if (parentCollapse) {
                const parentToggle = document.querySelector(`[data-bs-target="#${parentCollapse.id}"]`);
                if (parentToggle) {
                    parentToggle.setAttribute('aria-expanded', 'true');
                    parentToggle.classList.remove('collapsed');
                    parentCollapse.classList.add('show');
                }
            }
        }
    });
}





/**
 * 表单验证初始化
 */
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                // 显示第一个无效字段的错误消息
                const invalidField = form.querySelector(':invalid');
                if (invalidField) {
                    invalidField.focus();
                    // 滚动到可见区域
                    invalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * 数据筛选面板交互
 */
function initFilterPanels() {
    const toggleButtons = document.querySelectorAll('.filter-toggle-btn');
    toggleButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const filterPanel = document.querySelector(targetId);
            
            if (filterPanel) {
                filterPanel.classList.toggle('show');
                
                // 切换按钮图标
                const icon = this.querySelector('i');
                if (icon) {
                    if (icon.classList.contains('bi-funnel')) {
                        icon.classList.remove('bi-funnel');
                        icon.classList.add('bi-funnel-fill');
                    } else {
                        icon.classList.remove('bi-funnel-fill');
                        icon.classList.add('bi-funnel');
                    }
                }
            }
        });
    });
    
    // 重置筛选按钮
    const resetButtons = document.querySelectorAll('.filter-reset-btn');
    resetButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const form = this.closest('form');
            if (form) {
                const inputs = form.querySelectorAll('input, select');
                inputs.forEach(input => {
                    if (input.type === 'text' || input.type === 'search' || input.tagName === 'SELECT') {
                        input.value = '';
                    } else if (input.type === 'checkbox' || input.type === 'radio') {
                        input.checked = false;
                    }
                });
            }
        });
    });
}

/**
 * 模态框效果初始化
 */
function initModals() {
    // 模态框打开时添加动画效果
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function() {
            // 使用requestAnimationFrame替代setTimeout，更流畅的动画
            requestAnimationFrame(() => {
                const dialog = this.querySelector('.modal-dialog');
                if (dialog) {
                    dialog.style.transform = 'scale(1)';
                    dialog.style.opacity = '1';
                }
            });
        });

        modal.addEventListener('hide.bs.modal', function() {
            const dialog = this.querySelector('.modal-dialog');
            if (dialog) {
                dialog.style.transform = 'scale(0.9)';
                dialog.style.opacity = '0';
            }
        });
    });
}

/**
 * 数据表格搜索功能
 */
function initTableSearch() {
    const searchInputs = document.querySelectorAll('.table-search-input');
    searchInputs.forEach(input => {
        input.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const tableId = this.getAttribute('data-table');
            const table = document.getElementById(tableId);
            
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        });
    });
}

/**
 * 自动提示搜索功能
 * @param {string} inputId - 输入框ID
 * @param {Array} data - 搜索数据源
 */
function setupAutocomplete(inputId, data) {
    const input = document.getElementById(inputId);
    if (!input) return;
    
    let currentFocus;
    
    input.addEventListener("input", function() {
        let val = this.value;
        
        // 关闭已经打开的列表
        closeAllLists();
        
        if (!val) { return false; }
        currentFocus = -1;
        
        // 创建建议项容器
        const suggestionContainer = document.createElement("div");
        suggestionContainer.setAttribute("id", this.id + "autocomplete-list");
        suggestionContainer.setAttribute("class", "autocomplete-items");
        this.parentNode.appendChild(suggestionContainer);
        
        // 为匹配项创建元素
        for (let i = 0; i < data.length; i++) {
            if (data[i].toLowerCase().includes(val.toLowerCase())) {
                const item = document.createElement("div");
                
                // 匹配部分加粗显示
                const matchIndex = data[i].toLowerCase().indexOf(val.toLowerCase());
                item.innerHTML = data[i].substr(0, matchIndex);
                item.innerHTML += "<strong>" + data[i].substr(matchIndex, val.length) + "</strong>";
                item.innerHTML += data[i].substr(matchIndex + val.length);
                
                item.innerHTML += "<input type='hidden' value='" + data[i] + "'>";
                item.addEventListener("click", function() {
                    input.value = this.getElementsByTagName("input")[0].value;
                    closeAllLists();
                });
                suggestionContainer.appendChild(item);
            }
        }
    });
    
    // 处理键盘导航
    input.addEventListener("keydown", function(e) {
        let x = document.getElementById(this.id + "autocomplete-list");
        if (x) x = x.getElementsByTagName("div");
        if (e.keyCode == 40) { // 下箭头
            currentFocus++;
            addActive(x);
        } else if (e.keyCode == 38) { // 上箭头
            currentFocus--;
            addActive(x);
        } else if (e.keyCode == 13) { // 回车
            e.preventDefault();
            if (currentFocus > -1) {
                if (x) x[currentFocus].click();
            }
        }
    });
    
    function addActive(x) {
        if (!x) return false;
        removeActive(x);
        if (currentFocus >= x.length) currentFocus = 0;
        if (currentFocus < 0) currentFocus = (x.length - 1);
        x[currentFocus].classList.add("autocomplete-active");
    }
    
    function removeActive(x) {
        for (let i = 0; i < x.length; i++) {
            x[i].classList.remove("autocomplete-active");
        }
    }
    
    function closeAllLists(elmnt) {
        const x = document.getElementsByClassName("autocomplete-items");
        for (let i = 0; i < x.length; i++) {
            if (elmnt != x[i] && elmnt != input) {
                x[i].parentNode.removeChild(x[i]);
            }
        }
    }
    
    document.addEventListener("click", function(e) {
        closeAllLists(e.target);
    });
}

/**
 * 生成占位图表
 * @param {string} chartId - 图表容器ID
 * @param {string} type - 图表类型
 */
function createPlaceholderChart(chartId, type) {
    const chartContainer = document.getElementById(chartId);
    if (!chartContainer) return;
    
    // 创建随机数据
    const createRandomData = (count, max) => {
        return Array.from({length: count}, () => Math.floor(Math.random() * max));
    };
    
    // 创建随机标签
    const createLabels = (count) => {
        return Array.from({length: count}, (_, i) => `项目 ${i+1}`);
    };
    
    const ctx = document.createElement('canvas');
    chartContainer.appendChild(ctx);
    
    const data = {
        labels: createLabels(6),
        datasets: [{
            label: '数据集',
            data: createRandomData(6, 100),
            backgroundColor: [
                'rgba(212, 175, 55, 0.7)',
                'rgba(54, 162, 235, 0.7)',
                'rgba(255, 99, 132, 0.7)',
                'rgba(75, 192, 192, 0.7)',
                'rgba(153, 102, 255, 0.7)',
                'rgba(255, 159, 64, 0.7)'
            ],
            borderColor: [
                'rgba(212, 175, 55, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 99, 132, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)'
            ],
            borderWidth: 1
        }]
    };
    
    const options = {
        responsive: true,
        maintainAspectRatio: false,
        animation: {
            duration: 1000,
            easing: 'easeOutQuart'
        }
    };

    // 如果是柱状图，设置柱子宽度
    if (type === 'bar') {
        data.datasets.forEach(dataset => {
            dataset.barThickness = 30; // 设置柱子宽度为30px，让柱子更细
        });
    }

    // 使用Chart.js创建图表
    if (typeof Chart !== 'undefined') {
        new Chart(ctx, {
            type: type,
            data: data,
            options: options
        });
    }
}

/**
 * 显示加载中指示器
 */
function showLoadingIndicator(targetElement) {
    const loader = document.createElement('div');
    loader.className = 'loading-spinner';
    loader.innerHTML = '<div class="spinner-border text-warning" role="status"><span class="visually-hidden">加载中...</span></div><p class="mt-2">正在加载...</p>';
    
    if (typeof targetElement === 'string') {
        targetElement = document.querySelector(targetElement);
    }
    
    if (targetElement) {
        targetElement.appendChild(loader);
    }
    
    return loader;
}

/**
 * 隐藏加载中指示器
 */
function hideLoadingIndicator(loaderElement) {
    if (loaderElement && loaderElement.parentNode) {
        loaderElement.parentNode.removeChild(loaderElement);
    }
}

