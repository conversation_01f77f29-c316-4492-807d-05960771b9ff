.TH "PACKAGE-LOCK.JSON" "5" "November 2023" "" ""
.SH "NAME"
\fBpackage-lock.json\fR - A manifestation of the manifest
.SS "Description"
.P
\fBpackage-lock.json\fR is automatically generated for any operations where npm modifies either the \fBnode_modules\fR tree, or \fBpackage.json\fR. It describes the exact tree that was generated, such that subsequent installs are able to generate identical trees, regardless of intermediate dependency updates.
.P
This file is intended to be committed into source repositories, and serves various purposes:
.RS 0
.IP \(bu 4
Describe a single representation of a dependency tree such that teammates, deployments, and continuous integration are guaranteed to install exactly the same dependencies.
.IP \(bu 4
Provide a facility for users to "time-travel" to previous states of \fBnode_modules\fR without having to commit the directory itself.
.IP \(bu 4
Facilitate greater visibility of tree changes through readable source control diffs.
.IP \(bu 4
Optimize the installation process by allowing npm to skip repeated metadata resolutions for previously-installed packages.
.IP \(bu 4
As of npm v7, lockfiles include enough information to gain a complete picture of the package tree, reducing the need to read \fBpackage.json\fR files, and allowing for significant performance improvements.
.RE 0

.SS "\fBpackage-lock.json\fR vs \fBnpm-shrinkwrap.json\fR"
.P
Both of these files have the same format, and perform similar functions in the root of a project.
.P
The difference is that \fBpackage-lock.json\fR cannot be published, and it will be ignored if found in any place other than the root project.
.P
In contrast, \fBnpm-shrinkwrap.json\fR \fI\(la/configuring-npm/npm-shrinkwrap-json\(ra\fR allows publication, and defines the dependency tree from the point encountered. This is not recommended unless deploying a CLI tool or otherwise using the publication process for producing production packages.
.P
If both \fBpackage-lock.json\fR and \fBnpm-shrinkwrap.json\fR are present in the root of a project, \fBnpm-shrinkwrap.json\fR will take precedence and \fBpackage-lock.json\fR will be ignored.
.SS "Hidden Lockfiles"
.P
In order to avoid processing the \fBnode_modules\fR folder repeatedly, npm as of v7 uses a "hidden" lockfile present in \fBnode_modules/.package-lock.json\fR. This contains information about the tree, and is used in lieu of reading the entire \fBnode_modules\fR hierarchy provided that the following conditions are met:
.RS 0
.IP \(bu 4
All package folders it references exist in the \fBnode_modules\fR hierarchy.
.IP \(bu 4
No package folders exist in the \fBnode_modules\fR hierarchy that are not listed in the lockfile.
.IP \(bu 4
The modified time of the file is at least as recent as all of the package folders it references.
.RE 0

.P
That is, the hidden lockfile will only be relevant if it was created as part of the most recent update to the package tree. If another CLI mutates the tree in any way, this will be detected, and the hidden lockfile will be ignored.
.P
Note that it \fIis\fR possible to manually change the \fIcontents\fR of a package in such a way that the modified time of the package folder is unaffected. For example, if you add a file to \fBnode_modules/foo/lib/bar.js\fR, then the modified time on \fBnode_modules/foo\fR will not reflect this change. If you are manually editing files in \fBnode_modules\fR, it is generally best to delete the file at \fBnode_modules/.package-lock.json\fR.
.P
As the hidden lockfile is ignored by older npm versions, it does not contain the backwards compatibility affordances present in "normal" lockfiles. That is, it is \fBlockfileVersion: 3\fR, rather than \fBlockfileVersion: 2\fR.
.SS "Handling Old Lockfiles"
.P
When npm detects a lockfile from npm v6 or before during the package installation process, it is automatically updated to fetch missing information from either the \fBnode_modules\fR tree or (in the case of empty \fBnode_modules\fR trees or very old lockfile formats) the npm registry.
.SS "File Format"
.SS "\fBname\fR"
.P
The name of the package this is a package-lock for. This will match what's in \fBpackage.json\fR.
.SS "\fBversion\fR"
.P
The version of the package this is a package-lock for. This will match what's in \fBpackage.json\fR.
.SS "\fBlockfileVersion\fR"
.P
An integer version, starting at \fB1\fR with the version number of this document whose semantics were used when generating this \fBpackage-lock.json\fR.
.P
Note that the file format changed significantly in npm v7 to track information that would have otherwise required looking in \fBnode_modules\fR or the npm registry. Lockfiles generated by npm v7 will contain \fBlockfileVersion: 2\fR.
.RS 0
.IP \(bu 4
No version provided: an "ancient" shrinkwrap file from a version of npm prior to npm v5.
.IP \(bu 4
\fB1\fR: The lockfile version used by npm v5 and v6.
.IP \(bu 4
\fB2\fR: The lockfile version used by npm v7 and v8. Backwards compatible to v1 lockfiles.
.IP \(bu 4
\fB3\fR: The lockfile version used by npm v9 and above. Backwards compatible to npm v7.
.RE 0

.P
npm will always attempt to get whatever data it can out of a lockfile, even if it is not a version that it was designed to support.
.SS "\fBpackages\fR"
.P
This is an object that maps package locations to an object containing the information about that package.
.P
The root project is typically listed with a key of \fB""\fR, and all other packages are listed with their relative paths from the root project folder.
.P
Package descriptors have the following fields:
.RS 0
.IP \(bu 4
version: The version found in \fBpackage.json\fR
.IP \(bu 4
resolved: The place where the package was actually resolved from. In the case of packages fetched from the registry, this will be a url to a tarball. In the case of git dependencies, this will be the full git url with commit sha. In the case of link dependencies, this will be the location of the link target. \fBregistry.npmjs.org\fR is a magic value meaning "the currently configured registry".
.IP \(bu 4
integrity: A \fBsha512\fR or \fBsha1\fR \fBStandard Subresource Integrity\fR \fI\(lahttps://w3c.github.io/webappsec/specs/subresourceintegrity/\(ra\fR string for the artifact that was unpacked in this location.
.IP \(bu 4
link: A flag to indicate that this is a symbolic link. If this is present, no other fields are specified, since the link target will also be included in the lockfile.
.IP \(bu 4
dev, optional, devOptional: If the package is strictly part of the \fBdevDependencies\fR tree, then \fBdev\fR will be true. If it is strictly part of the \fBoptionalDependencies\fR tree, then \fBoptional\fR will be set. If it is both a \fBdev\fR dependency \fIand\fR an \fBoptional\fR dependency of a non-dev dependency, then \fBdevOptional\fR will be set. (An \fBoptional\fR dependency of a \fBdev\fR dependency will have both \fBdev\fR and \fBoptional\fR set.)
.IP \(bu 4
inBundle: A flag to indicate that the package is a bundled dependency.
.IP \(bu 4
hasInstallScript: A flag to indicate that the package has a \fBpreinstall\fR, \fBinstall\fR, or \fBpostinstall\fR script.
.IP \(bu 4
hasShrinkwrap: A flag to indicate that the package has an \fBnpm-shrinkwrap.json\fR file.
.IP \(bu 4
bin, license, engines, dependencies, optionalDependencies: fields from \fBpackage.json\fR
.RE 0

.SS "dependencies"
.P
Legacy data for supporting versions of npm that use \fBlockfileVersion: 1\fR. This is a mapping of package names to dependency objects. Because the object structure is strictly hierarchical, symbolic link dependencies are somewhat challenging to represent in some cases.
.P
npm v7 ignores this section entirely if a \fBpackages\fR section is present, but does keep it up to date in order to support switching between npm v6 and npm v7.
.P
Dependency objects have the following fields:
.RS 0
.IP \(bu 4
version: a specifier that varies depending on the nature of the package, and is usable in fetching a new copy of it.
.RS 4
.IP \(bu 4
bundled dependencies: Regardless of source, this is a version number that is purely for informational purposes.
.IP \(bu 4
registry sources: This is a version number. (eg, \fB1.2.3\fR)
.IP \(bu 4
git sources: This is a git specifier with resolved committish. (eg, \fBgit+https://example.com/foo/bar#115311855adb0789a0466714ed48a1499ffea97e\fR)
.IP \(bu 4
http tarball sources: This is the URL of the tarball. (eg, \fBhttps://example.com/example-1.3.0.tgz\fR)
.IP \(bu 4
local tarball sources: This is the file URL of the tarball. (eg \fBfile:///opt/storage/example-1.3.0.tgz\fR)
.IP \(bu 4
local link sources: This is the file URL of the link. (eg \fBfile:libs/our-module\fR)
.RE 0

.IP \(bu 4
integrity: A \fBsha512\fR or \fBsha1\fR \fBStandard Subresource Integrity\fR \fI\(lahttps://w3c.github.io/webappsec/specs/subresourceintegrity/\(ra\fR string for the artifact that was unpacked in this location. For git dependencies, this is the commit sha.
.IP \(bu 4
resolved: For registry sources this is path of the tarball relative to the registry URL. If the tarball URL isn't on the same server as the registry URL then this is a complete URL. \fBregistry.npmjs.org\fR is a magic value meaning "the currently configured registry".
.IP \(bu 4
bundled: If true, this is the bundled dependency and will be installed by the parent module. When installing, this module will be extracted from the parent module during the extract phase, not installed as a separate dependency.
.IP \(bu 4
dev: If true then this dependency is either a development dependency ONLY of the top level module or a transitive dependency of one. This is false for dependencies that are both a development dependency of the top level and a transitive dependency of a non-development dependency of the top level.
.IP \(bu 4
optional: If true then this dependency is either an optional dependency ONLY of the top level module or a transitive dependency of one. This is false for dependencies that are both an optional dependency of the top level and a transitive dependency of a non-optional dependency of the top level.
.IP \(bu 4
requires: This is a mapping of module name to version. This is a list of everything this module requires, regardless of where it will be installed. The version should match via normal matching rules a dependency either in our \fBdependencies\fR or in a level higher than us.
.IP \(bu 4
dependencies: The dependencies of this dependency, exactly as at the top level.
.RE 0

.SS "See also"
.RS 0
.IP \(bu 4
npm help shrinkwrap
.IP \(bu 4
\fBnpm-shrinkwrap.json\fR \fI\(la/configuring-npm/npm-shrinkwrap-json\(ra\fR
.IP \(bu 4
\fBpackage.json\fR \fI\(la/configuring-npm/package-json\(ra\fR
.IP \(bu 4
npm help install
.RE 0
