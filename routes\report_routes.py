from flask import request, jsonify
from datetime import datetime, time, timedelta
from flask_login import login_required as flask_login_required
import logging
from sqlalchemy import func, or_, and_

# 获取当前模块的logger
logger = logging.getLogger(__name__)

# 延迟导入app和db，避免循环导入
def get_app_db():
    from app import app, db
    return app, db

def api_report_daily():
    """获取日报表数据"""
    app, db = get_app_db()
    try:
        date_str = request.args.get('date', None)
        if date_str:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            report_date = datetime.now().date()

        # 获取指定日期的统计数据
        stats = get_daily_stats(report_date)
        
        # 获取产品库存变动
        product_inventory = get_product_inventory_changes(report_date)
        
        # 获取旧料库存变动
        old_material = get_old_material_changes(report_date)
        
        # 获取账户余额变动
        account_balance = get_account_balance_changes(report_date)
        
        return jsonify({
            'status': 'success',
            'data': {
                'stats': stats,
                'product_inventory': product_inventory,
                'old_material': old_material,
                'account_balance': account_balance
            }
        })
    except Exception as e:
        app.logger.error(f"获取日报表数据失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取日报表数据失败: {str(e)}"
        })

def get_daily_stats(report_date):
    """获取指定日期的统计数据"""
    app, db = get_app_db()
    from models import Order, OrderItem, Purchase, Product
    
    # 设置日期范围
    start_datetime = datetime.combine(report_date, time.min)
    end_datetime = datetime.combine(report_date, time.max)
    
    # 查询当日订单 - 使用业务时间order_date字段进行过滤
    orders = Order.query.filter(
        Order.order_date >= start_datetime,
        Order.order_date <= end_datetime,
        Order.is_deleted == False
    ).all()

    # 计算销售总额
    total_sales = sum(float(order.total_amount or 0) for order in orders)

    # 获取订单项，计算黄金出入库量 - 使用业务时间order_date字段进行过滤
    order_items = OrderItem.query.join(Order).filter(
        Order.order_date >= start_datetime,
        Order.order_date <= end_datetime,
        Order.is_deleted == False
    ).all()
    
    # 修改：只计算分类为黄金的产品和旧料名称为旧料足金的旧料
    # 黄金出库(新货) - 只统计分类为黄金的产品
    gold_out = 0
    for item in order_items:
        if item.item_type == 'new':
            # 多种方式判断是否为黄金产品
            is_gold_product = False
            if item.product and hasattr(item.product, 'category') and item.product.category == '黄金':
                is_gold_product = True
            elif item.product_name and any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']):
                is_gold_product = True
            
            if is_gold_product:
                gold_out += float(item.weight or 0)
    
    # 黄金退货量 - 只统计分类为黄金的产品
    gold_return = 0
    for item in order_items:
        if item.item_type == 'return':
            # 多种方式判断是否为黄金产品
            is_gold_product = False
            if item.product and hasattr(item.product, 'category') and item.product.category == '黄金':
                is_gold_product = True
            elif item.product_name and any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']):
                is_gold_product = True
            
            if is_gold_product:
                gold_return += float(item.weight or 0)
    
    # 今日料部收料(黄金) - 只统计旧料名称为旧料足金，结算方式为还料、结料、存料的旧料
    gold_in = 0
    # 开单录入单据中结料和欠料为旧料足金的旧料
    order_settle_owe_gold = 0
    for item in order_items:
        if item.item_type == 'old' and item.material_name == '旧料足金':
            # 今日料部收料(黄金) = 还料、结料、存料的旧料足金的和
            if item.settlement_type in ['return', 'settle', 'store']:
                gold_in += float(item.net_weight or 0)
            # 开单录入单据中结料和欠料为旧料足金的旧料
            if item.settlement_type in ['settle', 'owe']:
                order_settle_owe_gold += float(item.net_weight or 0)
    
    # 查询当日采购单 - 使用func.date确保只比较日期部分
    purchases = Purchase.query.filter(
        func.date(Purchase.business_time) == report_date,
        Purchase.is_deleted == False
    ).all()
    
    app.logger.info(f"get_daily_stats: 查询日期 {report_date} 的采购单，找到 {len(purchases)} 条记录")
    
    # 采购黄金 - 只统计分类为黄金的产品
    gold_purchase = 0
    from models import PurchaseItem
    purchase_items = PurchaseItem.query.join(Purchase).filter(
        func.date(Purchase.business_time) == report_date,
        Purchase.is_deleted == False,
        PurchaseItem.is_deleted == False
    ).all()
    
    for item in purchase_items:
        # 多种方式判断是否为黄金产品
        is_gold_product = False
        if item.product and hasattr(item.product, 'category') and item.product.category == '黄金':
            is_gold_product = True
        elif item.product_name and any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']):
            is_gold_product = True
        
        if is_gold_product:
            gold_purchase += float(item.weight or 0)
    
    # 修改：净出货量 = 今日出货(黄金) - 今日退货(黄金) - 单据中结算方式为欠料和结料的旧料足金的和
    gold_net = gold_out - gold_return - order_settle_owe_gold
    
    return {
        'order_count': len(orders),
        'total_sales': total_sales,
        'gold_out': gold_out,
        'gold_return': gold_return,
        'gold_in': gold_in,
        'gold_purchase': gold_purchase,
        'gold_net': gold_net
    }

def calculate_product_inventory_at_date(product, target_date):
    """
    计算指定产品在指定日期结束时的库存
    使用正向计算方法：初始库存 + 所有截止到目标日期的库存变化
    """
    from models import Purchase, PurchaseItem, Order, OrderItem, MaterialAdjustment, MaterialAdjustmentItem
    from sqlalchemy import func, and_, or_

    # 获取初始库存
    initial_stock = float(getattr(product, 'initial_stock', 0) or 0)

    # 计算截止到目标日期的所有采购入库
    purchase_items = PurchaseItem.query.join(Purchase).filter(
        func.date(Purchase.business_time) <= target_date,
        or_(
            PurchaseItem.product_id == product.id,
            PurchaseItem.product_name == product.name,
            and_(PurchaseItem.product_name.isnot(None), PurchaseItem.product_name == product.name)
        ),
        Purchase.is_deleted == False,
        PurchaseItem.is_deleted == False
    ).all()

    total_purchase = sum(float(item.weight or 0) for item in purchase_items
                        if getattr(item, 'item_type', 'new') in ['new', None])

    # 计算截止到目标日期的所有销售出库
    order_items = OrderItem.query.join(Order).filter(
        func.date(Order.order_date) <= target_date,
        or_(
            OrderItem.product_id == product.id,
            OrderItem.product_name == product.name,
            and_(OrderItem.product_code.isnot(None), OrderItem.product_code == product.code)
        ),
        Order.is_deleted == False
    ).all()

    total_out = sum(float(item.weight or 0) for item in order_items if item.item_type == 'new')
    total_returned = sum(float(item.weight or 0) for item in order_items if item.item_type == 'return')

    # 计算截止到目标日期的所有货料调整
    conversion_adjustments = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
        func.date(MaterialAdjustment.business_date) <= target_date,
        MaterialAdjustmentItem.item_type == 'product',
        MaterialAdjustmentItem.item_name == product.name
    ).all()

    total_adjustments = sum(float(item.weight_change or 0) for item in conversion_adjustments)

    # 计算最终库存
    final_stock = initial_stock + total_purchase + total_returned - total_out + total_adjustments

    return final_stock

def get_product_inventory_changes(report_date):
    """获取产品库存变动"""
    app, db = get_app_db()
    from models import Product, Purchase, PurchaseItem, Order, OrderItem
    from sqlalchemy import func, or_, and_

    # 获取所有产品
    products = Product.query.all()
    result = []

    # 设置日期范围
    start_datetime = datetime.combine(report_date, time.min)
    end_datetime = datetime.combine(report_date, time.max)
    yesterday = report_date - timedelta(days=1)

    for product in products:
        # 使用正向计算获取昨日库存
        previous_inventory = calculate_product_inventory_at_date(product, yesterday)

        # 当前库存使用正向计算获取
        current_stock = calculate_product_inventory_at_date(product, report_date)
        
        # 查询当日采购入库 - 多种匹配方式
        purchase_items = PurchaseItem.query.join(Purchase).filter(
            func.date(Purchase.business_time) == report_date,
            or_(
            PurchaseItem.product_id == product.id,
                PurchaseItem.product_name == product.name,
                and_(PurchaseItem.product_name.isnot(None), PurchaseItem.product_name == product.name)
            ),
            Purchase.is_deleted == False,
            PurchaseItem.is_deleted == False
        ).all()

        # 采购量计算 - 只统计新品采购，不包括退货等
        purchase_quantity = sum(float(item.weight or 0) for item in purchase_items
                               if getattr(item, 'item_type', 'new') in ['new', None])

        # 查询当日销售出库和退货 - 多种匹配方式
        order_items = OrderItem.query.join(Order).filter(
            Order.order_date >= start_datetime,
            Order.order_date <= end_datetime,
            or_(
            OrderItem.product_id == product.id,
                OrderItem.product_name == product.name,
                and_(OrderItem.product_code.isnot(None), OrderItem.product_code == product.code)
            ),
            Order.is_deleted == False
        ).all()

        # 批发客户出货 - 只统计新品出货且客户类型为批发的订单
        wholesale_out = sum(float(item.weight or 0) for item in order_items
                       if item.item_type == 'new' and item.order.customer and
                       getattr(item.order.customer, 'customer_type', '') == 'wholesale')

        # 零售客户出货 - 只统计新品出货且客户类型为零售或无客户的订单（默认零售）
        retail_out = sum(float(item.weight or 0) for item in order_items
                       if item.item_type == 'new' and (not item.order.customer or
                       getattr(item.order.customer, 'customer_type', '') != 'wholesale'))

        # 退货 - 只统计return类型
        returned = sum(float(item.weight or 0) for item in order_items
                      if item.item_type == 'return')

        # 总出货量
        total_out = wholesale_out + retail_out
        
        # 查询当日货料调整 - 产品相关的调整
        from models import MaterialAdjustment, MaterialAdjustmentItem

        # 查询当日货料调整（包括货料转换和纯产品调整）
        material_conversion = 0  # 货料转换
        inventory_adjustment = 0  # 纯产品调整（今日盘点）

        # 查询所有相关的调整项目
        adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
            func.date(MaterialAdjustment.business_date) == report_date,
            or_(
                # 新字段名匹配
                and_(
                    MaterialAdjustmentItem.target_type == 'product',
                    MaterialAdjustmentItem.target_name == product.name
                ),
                # 兼容旧字段名
                and_(
                    MaterialAdjustmentItem.item_type == 'product',
                    MaterialAdjustmentItem.item_name == product.name
                )
            )
        ).all()

        # 分别计算货料转换和纯调整
        for item in adjustment_items:
            weight_change = float(item.weight_change or 0)
            if item.is_conversion:
                # 货料转换：旧料转产品时，产品增加（正值）
                material_conversion += weight_change
            else:
                # 纯产品调整（今日盘点）
                inventory_adjustment += weight_change

        # 计算净变化（包含调整）
        net_change = purchase_quantity + returned - total_out + material_conversion + inventory_adjustment

        # 验证今日库存计算
        calculated_today_inventory = previous_inventory + net_change

        # 如果计算出的今日库存与数据库库存差异较大，记录警告并使用计算值
        if abs(calculated_today_inventory - current_stock) > 0.01:
            # 对于历史数据查询，使用计算值而不是数据库当前值
            if report_date != datetime.now().date():
                current_stock = calculated_today_inventory

        result.append({
            'product_name': product.name,
            'previous_inventory': previous_inventory,
            'purchase': purchase_quantity,
            'returned': returned,
            'wholesale_out': wholesale_out,
            'retail_out': retail_out,
            'total_out': total_out,
            'material_conversion': material_conversion,  # 货料转换（原旧料转新货）
            'inventory_adjustment': inventory_adjustment,  # 今日盘点
            'current_inventory': current_stock,
            'net_change': net_change
        })
    
    return result

def calculate_old_material_inventory_at_date(material, target_date):
    """
    计算指定旧料在指定日期结束时的库存
    使用正向计算方法：初始库存 + 所有截止到目标日期的库存变化
    """
    from models import Order, OrderItem, SupplierTransaction, MaterialTransaction, MoneyTransaction, MaterialAdjustment, MaterialAdjustmentItem
    from sqlalchemy import func

    # 获取初始库存
    initial_stock = float(getattr(material, 'initial_stock', 0) or 0)

    # 计算截止到目标日期的所有旧料交易变化
    old_items = OrderItem.query.join(Order).filter(
        func.date(Order.order_date) <= target_date,
        OrderItem.item_type == 'old',
        OrderItem.material_name == material.name,
        Order.status != 'deleted'
    ).all()

    # 统计各类型旧料变化 - 使用净重而不是克重
    total_collected = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'collect')
    total_settled = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'settle')
    total_returned = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'return')
    total_customer_deposit = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'store')

    # 计算截止到目标日期的供应商往来买料
    buy_material_trans = MoneyTransaction.query.join(SupplierTransaction).filter(
        func.date(SupplierTransaction.business_date) <= target_date,
        MoneyTransaction.return_purpose == '买料',
        MoneyTransaction.return_material_type == material.name
    ).all()

    total_buy_material = sum(float(mt.return_weight or 0) for mt in buy_material_trans)

    # 计算截止到目标日期的供应商往来物料交易
    supplier_material_trans = MaterialTransaction.query.join(SupplierTransaction).filter(
        func.date(SupplierTransaction.business_date) <= target_date
    ).all()

    total_supplier_store = 0  # 供应商往来存料
    total_daily_deposit = 0   # 寄料

    for mt in supplier_material_trans:
        # 存料操作增加旧料库存
        if mt.store_material_type == material.name and mt.store_weight and mt.store_weight > 0:
            total_supplier_store += float(mt.store_weight)

        # 寄料操作减少旧料库存
        if mt.deposit_material_type == material.name and mt.deposit_weight and mt.deposit_weight > 0:
            total_daily_deposit += float(mt.deposit_weight)

    # 计算截止到目标日期的所有旧料调整
    adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
        func.date(MaterialAdjustment.business_date) <= target_date,
        MaterialAdjustmentItem.item_type == 'old_material',
        MaterialAdjustmentItem.item_name == material.name
    ).all()

    total_adjustments = sum(float(item.weight_change or 0) for item in adjustment_items)

    # 计算最终库存
    final_stock = (initial_stock + total_buy_material + total_collected + total_settled +
                  total_returned + total_customer_deposit + total_supplier_store +
                  total_adjustments - total_daily_deposit)

    return final_stock

def get_old_material_changes(report_date):
    """获取旧料库存变动"""
    from models import OldMaterial, Order, OrderItem
    from sqlalchemy import func, or_, and_

    # 获取所有旧料
    old_materials = OldMaterial.query.all()
    result = []

    # 设置日期范围
    start_datetime = datetime.combine(report_date, time.min)
    end_datetime = datetime.combine(report_date, time.max)
    yesterday = report_date - timedelta(days=1)

    for material in old_materials:
        # 使用正向计算获取昨日库存
        previous_inventory = calculate_old_material_inventory_at_date(material, yesterday)

        # 当前库存从数据库获取
        current_stock = float(getattr(material, 'department_stock', 0) or 0)
        
        # 查询当日旧料交易 - 来自订单的旧料
        old_items = OrderItem.query.join(Order).filter(
            Order.order_date >= start_datetime,
            Order.order_date <= end_datetime,
            OrderItem.item_type == 'old',
            OrderItem.material_name == material.name,
            Order.status != 'deleted'
        ).all()

        # 统计各类型旧料变化 - 使用净重而不是克重
        collected = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'collect')
        settled = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'settle')
        returned = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'return')
        customer_deposit = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'store')
        deduct_deposit = sum(float(item.net_weight or 0) for item in old_items if item.settlement_type == 'deduct')

        # 查询供应商往来中的买料数据（从MoneyTransaction表）
        from models import SupplierTransaction, MaterialTransaction, MoneyTransaction
        buy_material_trans = MoneyTransaction.query.join(SupplierTransaction).filter(
            func.date(SupplierTransaction.business_date) == report_date,
            MoneyTransaction.return_purpose == '买料',
            MoneyTransaction.return_material_type == material.name
        ).all()

        # 今日买料（从供应商往来的买料记录）
        buy_material = sum(float(mt.return_weight or 0) for mt in buy_material_trans)

        # 查询供应商往来中的物料交易情况
        supplier_material_trans = MaterialTransaction.query.join(SupplierTransaction).filter(
            func.date(SupplierTransaction.business_date) == report_date
        ).all()

        # 从供应商往来中筛选该旧料的相关交易
        supplier_return_material = 0  # 供应商往来还料
        supplier_store_material = 0   # 供应商往来存料
        daily_deposit = 0            # 今日寄料（寄料克重）
        deposit_loss = 0             # 寄料损耗
        for mt in supplier_material_trans:
            # 还料操作减少旧料库存
            if mt.return_material_type == material.name and mt.return_weight and mt.return_weight > 0:
                supplier_return_material += float(mt.return_weight)

            # 存料操作增加旧料库存
            if mt.store_material_type == material.name and mt.store_weight and mt.store_weight > 0:
                supplier_store_material += float(mt.store_weight)

            # 寄料操作减少旧料库存
            if mt.deposit_material_type == material.name and mt.deposit_weight and mt.deposit_weight > 0:
                daily_deposit += float(mt.deposit_weight)

            # 寄料损耗记录
            if mt.deposit_material_type == material.name and mt.deposit_loss and mt.deposit_loss > 0:
                deposit_loss += float(mt.deposit_loss)

        # 查询当日旧料调整
        from models import MaterialAdjustment, MaterialAdjustmentItem

        # 查询当日货料调整（包括货料转换和纯旧料调整）
        material_conversion = 0  # 货料转换
        material_adjustment = 0  # 纯旧料调整

        # 查询所有相关的调整项目
        adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
            func.date(MaterialAdjustment.business_date) == report_date,
            or_(
                # 新字段名匹配
                and_(
                    MaterialAdjustmentItem.target_type == 'old_material',
                    MaterialAdjustmentItem.target_name == material.name
                ),
                # 兼容旧字段名
                and_(
                    MaterialAdjustmentItem.item_type == 'old_material',
                    MaterialAdjustmentItem.item_name == material.name
                )
            )
        ).all()

        # 分别计算货料转换和纯调整
        for item in adjustment_items:
            weight_change = float(item.weight_change or 0)
            if item.is_conversion:
                # 货料转换：旧料转产品时，旧料减少（负值）
                material_conversion += weight_change
            else:
                # 纯旧料调整
                material_adjustment += weight_change

        # 计算净变化量（包含货料转换）
        net_change = (buy_material + collected + settled + returned + customer_deposit +
                     supplier_store_material + material_conversion + material_adjustment - daily_deposit)

        # 验证今日库存计算
        calculated_today_inventory = previous_inventory + net_change

        # 如果计算出的今日库存与数据库库存差异较大，记录警告并使用计算值
        if abs(calculated_today_inventory - current_stock) > 0.01:
            # 对于历史数据查询，使用计算值而不是数据库当前值
            if report_date != datetime.now().date():
                current_stock = calculated_today_inventory

        result.append({
            'material_name': material.name,
            'previous_inventory': previous_inventory,
            'buy_material': buy_material,  # 今日买料
            'collected': collected,
            'settled': settled,
            'returned': returned,
            'customer_deposit': customer_deposit,
            'deduct_deposit': deduct_deposit,
            'daily_deposit': daily_deposit,  # 今日寄料
            'deposit_loss': deposit_loss,  # 寄料损耗，不参与库存计算
            'material_conversion': material_conversion,  # 货料转换
            'material_adjustment': material_adjustment,  # 旧料调整
            'current_inventory': current_stock
        })
    
    return result

def calculate_account_balance_at_date(account, target_date):
    """
    计算指定账户在指定日期结束时的余额
    使用正向计算方法：初始余额 + 所有截止到目标日期的交易变化
    """
    from models import AccountTransaction, Payment
    from sqlalchemy import func
    from datetime import datetime

    # 获取初始余额
    initial_balance = float(account.initial_balance or 0)

    # 计算截止到目标日期的所有AccountTransaction变化
    account_transactions = AccountTransaction.query.filter(
        AccountTransaction.account_id == account.id,
        func.date(AccountTransaction.transaction_date) <= target_date
    ).all()

    account_change = 0
    for trans in account_transactions:
        amount = float(trans.amount or 0)
        trans_type = getattr(trans, 'transaction_type', '')
        purpose = getattr(trans, 'purpose', '')

        # 排除销售相关的AccountTransaction，避免与Payment重复计算
        if purpose in ['销售收入', '销售收款']:
            continue

        if trans_type == 'income':
            account_change += amount
        elif trans_type == 'expense':
            account_change -= amount

    # 计算截止到目标日期的所有Payment变化
    payments = Payment.query.filter(
        Payment.account_id == account.id,
        func.date(Payment.payment_date) <= target_date
    ).all()

    payment_change = sum(float(payment.amount or 0) for payment in payments)

    # 计算截止到目标日期的所有Transaction变化（其他项目往来）
    from models import Transaction
    other_transactions = Transaction.query.filter(
        func.date(Transaction.business_time) <= target_date,
        Transaction.account_id == account.id
    ).all()

    other_change = 0
    for trans in other_transactions:
        amount = float(trans.amount or 0)
        if trans.is_income:
            other_change += amount
        else:
            other_change -= amount

    # 计算最终余额
    final_balance = initial_balance + account_change + payment_change + other_change

    return final_balance

def get_account_balance_changes(report_date):
    """
    获取每日报表的账户余额变化数据
    """
    from models import Account, AccountTransaction, Payment, Order, Transaction, MoneyTransaction, SupplierTransaction
    from sqlalchemy import func, and_
    from datetime import timedelta
    
    balance_changes = []
    
    accounts = Account.query.filter_by(is_active=True).all()
    
    for account in accounts:
        # 统计当日收入和支出
        deposit_income = 0  # 今日柜台收入
        customer_repayment = 0  # 今日客户还款
        supplier_deductions = 0  # 供应商往来扣款
        other_income = 0  # 其他收入
        other_expenses = 0  # 其他支出
        
        # 查询当日的客户还款：payment_date为当日且Order不是当日创建的支付记录，且订单未删除
        customer_payments = Payment.query.join(Order).filter(
            Payment.account_id == account.id,
            func.date(Payment.payment_date) == report_date,
            func.date(Order.created_at) != report_date,  # 订单不是当日创建
            Order.is_deleted == False  # 排除已删除的订单
        ).all()

        customer_repayment = sum(float(payment.amount or 0) for payment in customer_payments)

        # 查询当日结算的当日订单的支付记录作为今日柜台收入，且订单未删除
        today_order_payments = Payment.query.join(Order).filter(
            Payment.account_id == account.id,
            func.date(Payment.payment_date) == report_date,
            func.date(Order.created_at) == report_date,  # 订单是当日创建
            Order.is_deleted == False  # 排除已删除的订单
        ).all()

        deposit_income = sum(float(payment.amount or 0) for payment in today_order_payments)
        
        # 查询当日的MoneyTransaction记录（供应商往来扣款）
        app, db = get_app_db()
        money_transactions = db.session.query(MoneyTransaction).join(
            SupplierTransaction, MoneyTransaction.transaction_id == SupplierTransaction.id
        ).filter(
            func.date(SupplierTransaction.business_date) == report_date
        ).all()
        
        for money_trans in money_transactions:
            store_source = getattr(money_trans, 'store_source', '')
            return_source = getattr(money_trans, 'return_source', '')
            
            if store_source == account.name:
                store_amount = float(getattr(money_trans, 'store_amount', 0) or 0)
                if store_amount > 0:
                    supplier_deductions += store_amount
            
            if return_source == account.name:
                return_amount = float(getattr(money_trans, 'return_amount', 0) or 0)
                if return_amount > 0:
                    supplier_deductions += return_amount
        
        # 查询当日的AccountTransaction记录
        account_transactions = AccountTransaction.query.filter(
            AccountTransaction.account_id == account.id,
            func.date(AccountTransaction.transaction_date) == report_date
        ).all()
        
        for trans in account_transactions:
            amount = float(trans.amount or 0)
            trans_type = getattr(trans, 'transaction_type', '')
            purpose = getattr(trans, 'purpose', '')

            if trans_type == 'income':
                if purpose in ['销售收入', '销售收款']:
                    # 销售相关收入不在这里计算，避免与Payment记录重复计算
                    # 销售收入通过today_order_payments计算（柜台收入）
                    # 销售收款通过customer_payments计算（客户还款）
                    pass
                else:
                    # 其他收入（排除销售相关的收入，避免重复计算）
                    other_income += amount
            elif trans_type == 'expense':
                # 排除销售相关和供应商相关的支出，这些应该在对应列中体现
                if purpose not in ['供应商存料', '供应商还料', '供应商买料', '供应商存款', '供应商还款', '销售扣款']:
                    other_expenses += amount  # 其他支出
        
        # 查询当日的Transaction记录（其他项目往来）
        other_transactions = Transaction.query.filter(
            func.date(Transaction.business_time) == report_date,
            Transaction.account_id == account.id
        ).all()
        
        for trans in other_transactions:
            amount = float(trans.amount or 0)
            if trans.is_income:
                other_income += amount
            else:
                other_expenses += amount
        
        # 今日余额从数据库获取
        current_balance = float(account.balance or 0)

        # 计算净变化量
        net_change = (deposit_income + customer_repayment + other_income -
                     other_expenses - supplier_deductions)

        # 计算昨日余额：使用正向计算方法
        previous_balance = calculate_account_balance_at_date(account, report_date - timedelta(days=1))

        # 验证今日余额计算
        calculated_today_balance = previous_balance + net_change

        # 如果计算出的今日余额与数据库余额差异较大，记录警告
        if abs(calculated_today_balance - current_balance) > 0.01:
            app.logger.warning(f"账户 {account.name} 计算余额不一致: 计算值={calculated_today_balance:.2f}, 数据库值={current_balance:.2f}, 差异={calculated_today_balance - current_balance:.2f}")

            # 对于历史数据查询，使用计算值而不是数据库当前值
            if report_date != datetime.now().date():
                current_balance = calculated_today_balance
        
        balance_changes.append({
            'account_name': account.name,
            'previous_balance': previous_balance,
            'deposit_income': deposit_income,
            'customer_repayment': customer_repayment,
            'supplier_deductions': supplier_deductions,
            'other_income': other_income,
            'other_expenses': other_expenses,
            'current_balance': current_balance,
            'net_change': net_change
        })
    
    return balance_changes

def api_orders_today():
    """获取当日销售订单"""
    app, db = get_app_db()
    from models import Order
    
    try:
        date_str = request.args.get('date', None)
        if date_str:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            report_date = datetime.now().date()
        
        # 设置日期范围
        start_datetime = datetime.combine(report_date, time.min)
        end_datetime = datetime.combine(report_date, time.max)
        
        # 查询当日订单 - 使用业务时间order_date字段进行过滤
        orders = Order.query.filter(
            Order.order_date >= start_datetime,
            Order.order_date <= end_datetime,
            Order.is_deleted == False
        ).all()
        
        orders_data = []
        for order in orders:
            # 计算各类项目的黄金重量 - 只计算黄金相关产品
            new_items = [item for item in order.items if item.item_type == 'new']
            return_items = [item for item in order.items if item.item_type == 'return']
            old_items = [item for item in order.items if item.item_type == 'old']
            
            # 出库重量（黄金）- 只计算产品分类为黄金的产品
            gold_weight_out = 0
            for item in new_items:
                # 多种方式判断是否为黄金产品
                is_gold_product = False
                if item.product and hasattr(item.product, 'category') and item.product.category == '黄金':
                    is_gold_product = True
                elif item.product_name and any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']):
                    is_gold_product = True
                
                if is_gold_product:
                    gold_weight_out += float(item.weight or 0)
            
            # 退货重量（黄金） 只计算产品分类为黄金的产品
            gold_weight_in = 0
            for item in return_items:
                # 多种方式判断是否为黄金产品
                is_gold_product = False
                if item.product and hasattr(item.product, 'category') and item.product.category == '黄金':
                    is_gold_product = True
                elif item.product_name and any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']):
                    is_gold_product = True
                
                if is_gold_product:
                    gold_weight_in += float(item.weight or 0)
            
            # 旧料重量（黄金）- 只计算材料名称为"旧料足金"且结算方式为存料、还料、结料的重量
            old_material_weight = 0
            for item in old_items:
                if (item.material_name == '旧料足金' and 
                    item.settlement_type in ['store', 'return', 'settle'] and 
                    item.net_weight):
                    old_material_weight += float(item.net_weight)
            
            # 获取客户名称
            customer_name = order.customer.name if order.customer else '散客'
            
            orders_data.append({
                'id': order.id,
                'order_no': order.order_no,
                'order_date': order.order_date.strftime('%Y-%m-%d %H:%M') if order.order_date else '',
                'customer_name': customer_name,
                'gold_weight_out': gold_weight_out,
                'gold_weight_in': gold_weight_in,
                'old_material_weight': old_material_weight,
                'total_amount': float(order.total_amount or 0),
                'is_owed_material': bool(order.current_owed_gold and float(order.current_owed_gold) > 0),
                'is_paid': bool(getattr(order, 'is_paid', False))
            })
        
        return jsonify({
            'status': 'success',
            'data': orders_data
        })
    except Exception as e:
        app.logger.error(f"获取今日订单失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取今日订单失败: {str(e)}"
        })

def api_purchases_today():
    """获取当日采购订单"""
    app, db = get_app_db()
    from models import Purchase, PurchaseItem, Supplier
    
    try:
        date_str = request.args.get('date', None)
        if date_str:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            report_date = datetime.now().date()
        
        app.logger.info(f"开始查询日期 {report_date} 的采购单")
        
        # 查询当日采购单 - 使用DATE函数确保只比较日期部分
        purchases = Purchase.query.filter(
            func.date(Purchase.business_time) == report_date,
            Purchase.is_deleted == False
        ).all()
        
        app.logger.info(f"查询日期 {report_date} 的采购单，找到 {len(purchases)} 条记录")
        if len(purchases) > 0:
            app.logger.info(f"第一条采购单ID: {purchases[0].id}, 采购单号: {getattr(purchases[0], 'purchase_no', 'n/a')}")
            app.logger.info(f"第一条采购单业务日期: {purchases[0].business_time}")
        
        purchases_data = []
        for purchase in purchases:
            # 获取供应商信息 - 现在是从采购项中提取
            supplier_names = []
            supplier_ids = set()
            supplier_count = 0
            
            # 从采购项中获取供应商信息（只统计有效项目）
            for item in purchase.items:
                # 跳过已删除的项目
                if item.is_deleted:
                    continue
                    
                if item.supplier_id and item.supplier_id not in supplier_ids:
                    supplier_ids.add(item.supplier_id)
                    supplier_count += 1
                    if hasattr(item, 'supplier') and item.supplier:
                        supplier_names.append(item.supplier.name)
            
            supplier_name = ', '.join(supplier_names) if supplier_names else '未知供应商'
            
            business_time_str = ''
            if purchase.business_time:
                if isinstance(purchase.business_time, datetime):
                    business_time_str = purchase.business_time.strftime('%Y-%m-%d')
                else:
                    business_time_str = str(purchase.business_time)
            
            # 计算结价金额（从采购项中汇总）
            final_amount = 0
            if hasattr(purchase, 'total_final_amount') and purchase.total_final_amount:
                final_amount = purchase.total_final_amount
            else:
                # 从采购项中汇总计算结价金额（只统计有效项目）
                for item in purchase.items:
                    # 跳过已删除的项目
                    if item.is_deleted:
                        continue
                        
                    if hasattr(item, 'final_amount') and item.final_amount:
                        final_amount += float(item.final_amount)
                # 更新采购单的total_final_amount字段
                purchase.total_final_amount = final_amount
            
            # 计算黄金重量（只统计产品分类为黄金的有效采购项）
            gold_weight = 0
            for item in purchase.items:
                # 跳过已删除的项目
                if item.is_deleted:
                    continue
                    
                # 多种方式判断是否为黄金产品
                is_gold_product = False
                if item.product and hasattr(item.product, 'category') and item.product.category == '黄金':
                    is_gold_product = True
                elif item.product_name and any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']):
                    is_gold_product = True
                
                if is_gold_product:
                    gold_weight += float(item.weight or 0)
            
            purchase_data = {
                'id': purchase.id,
                'purchase_no': getattr(purchase, 'purchase_no', f'P{purchase.id:06d}'),
                'supplier_name': supplier_name,
                'supplier_count': supplier_count,  # 添加供应商数量
                'weight': float(gold_weight),  # 改为黄金重量
                'total_weight': float(purchase.total_weight or 0),  # 保留总重量供其他用
                'amount': float(purchase.total_amount or 0),
                'final_amount': float(final_amount),  # 添加结价金额
                'business_time': business_time_str
            }
            
            purchases_data.append(purchase_data)
            app.logger.debug(f"处理采购单 ID={purchase.id}, 日期={business_time_str}, 供应商数量={supplier_count}")
        
        return jsonify({
            'status': 'success',
            'data': purchases_data
        })
    except Exception as e:
        app.logger.error(f"获取今日采购订单失败: {str(e)}")
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f"获取今日采购订单失败: {str(e)}"
        })

def api_supplier_transactions_today():
    """获取当日供应商往来"""
    app, db = get_app_db()
    from models import SupplierTransaction, MoneyTransaction, MaterialTransaction
    
    try:
        date_str = request.args.get('date', None)
        if date_str:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            report_date = datetime.now().date()
        
        app.logger.info(f"开始查询日期 {report_date} 的供应商往来记录")
        
        # 使用func.date确保只比较日期部分
        transactions = SupplierTransaction.query.filter(
            func.date(SupplierTransaction.business_date) == report_date
        ).all()
        
        app.logger.info(f"查询日期 {report_date} 的供应商往来，找到 {len(transactions)} 条记录")
        if len(transactions) > 0:
            app.logger.info(f"第一条供应商往来ID: {transactions[0].id}, 业务日期: {transactions[0].business_date}")
        
        transactions_data = []
        for transaction in transactions:
            # 获取供应商信息
            supplier_name = transaction.supplier.name if hasattr(transaction, 'supplier') and transaction.supplier else '未知供应商'
            supplier_type_text = '产品供应'
            if hasattr(transaction, 'supplier') and transaction.supplier:
                supplier_type = transaction.supplier.supplier_type
                if supplier_type == 'product':
                    supplier_type_text = '产品供应'
                elif supplier_type in ['gold', 'gold_material', 'material']:
                    supplier_type_text = '金料供应'
                else:
                    supplier_type_text = supplier_type or '产品供应'
            
            # 获取物料交易信息
            material_transactions = MaterialTransaction.query.filter_by(transaction_id=transaction.id).all()
            total_return_weight = sum(float(mt.return_weight or 0) for mt in material_transactions)
            total_store_weight = sum(float(mt.store_weight or 0) for mt in material_transactions)
            # 计算本期寄料重量（旧料足金的实际到料值）
            total_deposit_weight = sum(float(mt.actual_deposit_weight or 0) for mt in material_transactions 
                                      if mt.deposit_material_type == '旧料足金')
            
            # 获取款项交易信息
            money_transactions = MoneyTransaction.query.filter_by(transaction_id=transaction.id).all()
            total_return_amount = sum(float(mt.return_amount or 0) for mt in money_transactions)
            total_store_amount = sum(float(mt.store_amount or 0) for mt in money_transactions)
            # 计算本期买料重量（旧料足金的买料克重值）
            total_buy_weight = sum(float(mt.return_weight or 0) for mt in money_transactions 
                                  if mt.return_material_type == '旧料足金')
            # 计算存款抵扣金额（买料表格中旧料名称为旧料足金的总计金额）
            total_buy_amount = sum(float(mt.return_amount or 0) for mt in money_transactions 
                                  if mt.return_purpose == '买料' and mt.return_material_type == '旧料足金')
            
            transactions_data.append({
                'id': transaction.id,
                'transaction_no': getattr(transaction, 'transaction_no', f"TR{transaction.id:06d}"),
                'supplier_name': supplier_name,
                'supplier_type': supplier_type_text,
                'business_date': transaction.business_date.strftime('%Y-%m-%d'),
                'business_date': transaction.business_date.strftime('%Y-%m-%d') if transaction.business_date else '',
                'total_deposit_weight': float(total_deposit_weight),
                'total_buy_weight': float(total_buy_weight),
                'total_buy_amount': float(total_buy_amount),
                'total_return_weight': float(total_return_weight),
                'total_store_weight': float(total_store_weight),
                'total_return_amount': float(total_return_amount),
                'total_store_amount': float(total_store_amount),
                'notes': transaction.notes if hasattr(transaction, 'notes') else ''
            })
        
        return jsonify({
            'status': 'success',
            'data': transactions_data
        })
    except Exception as e:
        app.logger.error(f"获取今日供应商往来失败: {str(e)}")
        # 打印完整堆栈跟踪
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f"获取今日供应商往来失败: {str(e)}"
        })

def api_other_transactions_today():
    """获取当日其他项目录入"""
    app, db = get_app_db()
    from models import Transaction, Account, AccountTransaction
    
    try:
        date_str = request.args.get('date', None)
        if date_str:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            report_date = datetime.now().date()
        
        app.logger.info(f"开始查询日期 {report_date} 的其他项目录入记录")
        
        # 只查询Transaction表中的当日数据（其他项目往来的专用表）
        transactions = Transaction.query.filter(
            func.date(Transaction.business_time) == report_date
        ).all()
        
        app.logger.info(f"查询日期 {report_date} 的其他项目往来，从Transaction表找到 {len(transactions)} 条记录")
        
        # 只查询AccountTransaction表中明确标记为其他项目往来的数据
        # 并且确保对应的Transaction记录存在（避免孤立记录）
        account_transactions = AccountTransaction.query.filter(
            func.date(AccountTransaction.transaction_date) == report_date,
            # 只包含非订单、非采购、非供应商相关的交易
            ~AccountTransaction.purpose.in_(['销售收入', '供应商存料', '供应商还料', '供应商买料']),
            # 排除关联订单和采购的交易
            AccountTransaction.order_id.is_(None),
            AccountTransaction.purchase_id.is_(None),
            AccountTransaction.supplier_transaction_id.is_(None)
            ).all()

        # 过滤掉孤立的AccountTransaction记录（没有对应Transaction记录的）
        valid_account_transactions = []
        for at in account_transactions:
            if at.related_document:
                # 检查是否有对应的Transaction记录
                transaction = Transaction.query.filter_by(transaction_no=at.related_document).first()
                if transaction:
                    valid_account_transactions.append(at)
                else:
                    app.logger.warning(f"发现孤立的AccountTransaction记录: ID={at.id}, 单据={at.related_document}")
            else:
                # 没有related_document的记录也保留（可能是其他类型的交易）
                valid_account_transactions.append(at)

        account_transactions = valid_account_transactions
        
        app.logger.info(f"查询日期 {report_date} 的其他项目录入，从AccountTransaction表找到 {len(account_transactions)} 条记录")
        
        transactions_data = []
        
        # 处理Transaction表中的数据
        for trans in transactions:
            # 获取账户信息
            account = Account.query.get(trans.account_id) if hasattr(trans, 'account_id') and trans.account_id else None
            account_name = account.name if account else '默认账户'
            
            # 构建交易数据
            transaction_data = {
                'id': trans.id,
                'transaction_no': getattr(trans, 'transaction_no', f'TR{trans.id:06d}'),
                'customer_name': trans.customer.name if hasattr(trans, 'customer') and trans.customer else getattr(trans, 'customer_name', '未知'),
                'amount': float(trans.amount or 0),
                'business_time': trans.business_time.strftime('%Y-%m-%d %H:%M') if hasattr(trans, 'business_time') and trans.business_time else trans.created_at.strftime('%Y-%m-%d %H:%M'),
                'account_name': account_name,
                'is_income': getattr(trans, 'is_income', True),
                'notes': getattr(trans, 'notes', '')
            }
            
            transactions_data.append(transaction_data)
        
        # 处理AccountTransaction表中的其他项目往来数据
        for trans in account_transactions:
            # 获取账户信息
            account = Account.query.get(trans.account_id) if trans.account_id else None
            account_name = account.name if account else '默认账户'
            
            # 构建交易数据
            transaction_data = {
                'id': trans.id,
                'transaction_no': getattr(trans, 'related_document', f'TR{trans.id:06d}'),
                'customer_name': getattr(trans, 'related_party', '未知'),
                'amount': float(trans.amount or 0),
                'created_at': trans.transaction_date.strftime('%Y-%m-%d %H:%M'),
                'account_name': account_name,
                'is_income': getattr(trans, 'transaction_type', '') == 'income',
                'notes': getattr(trans, 'notes', '')
            }
            
            transactions_data.append(transaction_data)
        
        # 详细记录找到的数据
        app.logger.info(f"最终找到 {len(transactions_data)} 条其他项目往来记录")
        
        return jsonify({
            'status': 'success',
            'data': transactions_data
        })
    except Exception as e:
        app.logger.error(f"获取今日其他项目录入失败: {str(e)}")
        # 记录完整堆栈跟踪便于调试
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f"获取今日其他项目录入失败: {str(e)}"
        })

def api_products_all():
    """获取所有产品"""
    app, db = get_app_db()
    from models import Product
    
    try:
        # 按照产品编号排序，与产品信息表保持一致
        products = Product.query.order_by(Product.code).all()
        products_data = []
        
        for product in products:
            products_data.append({
                'id': product.id,
                'name': product.name,
                'code': getattr(product, 'code', ''),
                'stock': float(getattr(product, 'stock', 0) or 0),
                'metal_type': getattr(product, 'metal_type', '')
            })
        
        return jsonify({
            'status': 'success',
            'data': products_data
        })
    except Exception as e:
        app.logger.error(f"获取所有产品失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取所有产品失败: {str(e)}"
        })



def api_accounts_all():
    """获取所有账户"""
    app, db = get_app_db()
    from models import Account
    
    try:
        accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
        accounts_data = []
        
        # 使用账户余额概览的计算方法获取当前余额
        from datetime import datetime
        today = datetime.now().strftime('%Y-%m-%d')
        account_balances = calculate_period_account_balances(today, today, accounts)

        # 创建账户ID到期末余额的映射
        balance_map = {acc_data['id']: acc_data['final_balance'] for acc_data in account_balances}

        for account in accounts:
            accounts_data.append({
                'id': account.id,
                'name': account.name,
                'balance': float(balance_map.get(account.id, 0))
            })
        
        return jsonify({
            'status': 'success',
            'data': accounts_data
        })
    except Exception as e:
        app.logger.error(f"获取所有账户失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取所有账户失败: {str(e)}"
        })

def api_material_adjustments_today():
    """获取当日货料调整记录"""
    app, db = get_app_db()
    from models import MaterialAdjustment, MaterialAdjustmentItem, User

    try:
        date_str = request.args.get('date', None)
        if date_str:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            report_date = datetime.now().date()

        app.logger.info(f"开始查询日期 {report_date} 的货料调整记录")

        # 查询当日货料调整记录
        adjustments = MaterialAdjustment.query.filter(
            func.date(MaterialAdjustment.business_date) == report_date
        ).all()

        app.logger.info(f"查询日期 {report_date} 的货料调整，找到 {len(adjustments)} 条记录")

        adjustments_data = []
        for adjustment in adjustments:
            # 获取调整项目
            adjustment_items = MaterialAdjustmentItem.query.filter_by(
                adjustment_id=adjustment.id
            ).all()

            # 获取创建人名称
            creator_name = adjustment.creator.username if adjustment.creator else '未知'

            # 调整类型显示名称
            type_names = {
                'old_material_adjustment': '旧料调整',
                'material_conversion': '货料转换',
                'daily_inventory': '今日盘点',
                # 兼容旧的类型名称
                'old_material': '旧料调整',
                'product': '产品调整',
                'old_to_product': '货料转换'
            }
            type_display = type_names.get(adjustment.adjustment_type, adjustment.adjustment_type)

            # 为每个调整项目创建一行数据
            for item in adjustment_items:
                item_type_display = '旧料' if item.item_type == 'old_material' else '产品'

                adjustments_data.append({
                    'id': adjustment.id,
                    'adjustment_no': adjustment.adjustment_no,
                    'adjustment_type': type_display,
                    'item_name': item.item_name,
                    'item_type': item_type_display,
                    'weight_change': float(item.weight_change),
                    'business_date': adjustment.business_date.strftime('%Y-%m-%d') if adjustment.business_date else '',
                    'notes': item.notes or adjustment.notes or '',
                    'creator_name': creator_name
                })

        return jsonify({
            'status': 'success',
            'data': adjustments_data
        })
    except Exception as e:
        app.logger.error(f"获取今日货料调整失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取今日货料调整失败: {str(e)}"
        })

def api_customers_search():
    """客户搜索API - 与开单录入搜索方式一致"""
    app, db = get_app_db()
    try:
        from models import Customer

        query = request.args.get('q', '').strip().lower()
        if not query:
            return jsonify({
                'success': False,
                'message': '搜索关键词不能为空'
            })

        # 获取所有客户数据，包括拼音信息
        customers = Customer.query.all()

        # 如果查询参数为'all'，返回所有客户数据
        if query == 'all':
            customers_data = []
            for customer in customers:
                customers_data.append({
                    'id': customer.id,
                    'name': customer.name or '',
                    'phone': customer.phone or '',
                    'customer_type': customer.customer_type or 'retail',
                    'pinyin': customer.pinyin or '',
                    'pinyin_initials': customer.pinyin_initials or ''
                })

            return jsonify({
                'success': True,
                'customers': customers_data
            })

        filtered_customers = []
        for customer in customers:
            name = customer.name.lower() if customer.name else ''
            phone = customer.phone.lower() if customer.phone else ''
            pinyin = customer.pinyin.lower() if customer.pinyin else ''
            pinyin_initials = customer.pinyin_initials.lower() if customer.pinyin_initials else ''

            # 使用与开单录入相同的搜索逻辑
            match = False

            # 1. 电话号码匹配
            if phone and query in phone:
                match = True

            # 2. 姓名直接匹配
            elif name and query in name:
                match = True

            # 3. 首字母精确开头匹配
            elif pinyin_initials and pinyin_initials.startswith(query):
                match = True

            # 4. 拼音开头匹配
            elif pinyin and pinyin.startswith(query):
                match = True

            # 5. 拼音部分匹配 - 支持拼音中任意音节开头匹配
            elif pinyin:
                import re
                pinyin_parts = re.findall(r'[a-z]+', pinyin)
                for part in pinyin_parts:
                    if part.startswith(query):
                        match = True
                        break

            # 6. 组合字符序列匹配
            elif pinyin_initials and query in pinyin_initials:
                match = True

            if match:
                filtered_customers.append({
                    'id': customer.id,
                    'name': customer.name,
                    'phone': customer.phone,
                    'customer_type': customer.customer_type,
                    'pinyin': customer.pinyin,
                    'pinyin_initials': customer.pinyin_initials
                })

        # 限制返回结果数量
        filtered_customers = filtered_customers[:10]

        return jsonify({
            'success': True,
            'customers': filtered_customers
        })
    except Exception as e:
        app.logger.error(f"搜索客户失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"搜索客户失败: {str(e)}"
        })

def api_customer_details(customer_id):
    """获取客户款料明细API"""
    app, db = get_app_db()
    try:
        from models import Customer, Order, Transaction, OrderItem, OldMaterial, CustomerMaterialBalance, Payment

        # 验证客户是否存在
        customer = Customer.query.get(customer_id)
        if not customer:
            return jsonify({
                'success': False,
                'message': '客户不存在'
            })

        details = []

        # 获取所有旧料类型（按编号排序）
        all_materials = OldMaterial.query.order_by(OldMaterial.code).all()

        # 获取客户的旧料初始值
        material_balances = {}
        for balance in customer.material_balances:
            material_balances[balance.material_name] = {
                'owed_weight': float(balance.owed_weight or 0),
                'stored_weight': float(balance.stored_weight or 0)
            }

        # 为没有设置初始值的旧料设置默认值0
        for material in all_materials:
            if material.name not in material_balances:
                material_balances[material.name] = {
                    'owed_weight': 0,
                    'stored_weight': 0
                }

        # 获取客户的款项初始值
        # 由于Customer.balance和deposit_amount字段会被订单和交易更新，包含累计值
        # 我们需要计算真正的初始值：当前累计值 - 所有业务变动

        # 计算所有订单产生的净欠款
        orders = Order.query.filter_by(customer_id=customer_id, is_deleted=False).all()
        total_order_debt = 0
        for order in orders:
            # 计算订单净欠款（使用total_amount代替net_amount）
            order_debt = float(order.total_amount or 0)
            payments = Payment.query.filter_by(order_id=order.id).all()
            payment_amount = sum(float(p.amount or 0) for p in payments)
            net_debt = order_debt - payment_amount
            total_order_debt += net_debt

        # 计算所有Transaction产生的净变动
        transactions = Transaction.query.filter_by(customer_id=customer_id).all()
        total_transaction_debt = 0
        total_transaction_deposit = 0
        for transaction in transactions:
            if transaction.notes == '存款':
                total_transaction_deposit += float(transaction.amount or 0)
            elif transaction.notes == '存款抵扣':
                total_transaction_deposit -= float(transaction.amount or 0)
            elif '还款' in (transaction.notes or ''):
                total_transaction_debt -= float(transaction.amount or 0)

        # 计算真正的初始值 = 当前累计值 - 所有业务变动
        current_balance = float(customer.balance or 0)
        current_deposit = float(customer.deposit_amount or 0)

        initial_debt = current_balance - total_order_debt - total_transaction_debt
        initial_deposit = current_deposit - total_transaction_deposit

        # 1. 添加初始值记录
        initial_record = {
            'business_time': '初始值',
            'document_type': '期初余额',
            'document_id': None,
            'remark': '系统初始值',
            'debt_amount': initial_debt,
            'deposit_amount': initial_deposit
        }

        # 动态添加所有旧料的初始值
        for material in all_materials:
            balance = material_balances[material.name]
            # 使用材料名称作为键
            initial_record[f'{material.name}_debt'] = balance['owed_weight']
            initial_record[f'{material.name}_storage'] = balance['stored_weight']

        details.append(initial_record)

        # 2. 获取订单相关的款料变动（排除已删除的订单）
        orders = Order.query.filter_by(customer_id=customer_id, is_deleted=False).order_by(Order.order_date).all()
        for order in orders:
            # 通过OrderItem获取旧料信息
            old_material_items = OrderItem.query.filter_by(
                order_id=order.id,
                item_type='old'
            ).all()

            # 构建订单记录
            # 欠款+，还款-，存款只来自Transaction表
            order_debt_amount = float(order.total_amount) if order.total_amount else 0

            # 计算支付金额（还款）- 这应该减少欠款，而不是影响存款
            payments = Payment.query.filter_by(order_id=order.id).all()
            payment_amount = sum(float(p.amount or 0) for p in payments)

            # 订单的净欠款 = 应付金额 - 实付金额
            net_debt_amount = order_debt_amount - payment_amount

            order_record = {
                'business_time': order.order_date.strftime('%Y-%m-%d %H:%M') if order.order_date else '',
                'document_type': '订单',
                'document_id': order.order_no,  # 使用订单编号而不是ID
                'document_link': f'/orders/{order.id}',  # 添加超链接
                'remark': order.notes or '',  # 直接使用备注内容
                'debt_amount': net_debt_amount,  # 净欠款（应付-实付）
                'deposit_amount': 0  # 订单不涉及存款，存款只来自Transaction表
            }

            # 初始化所有旧料字段为0
            for material in all_materials:
                order_record[f'{material.name}_debt'] = 0
                order_record[f'{material.name}_storage'] = 0

            # 计算各种旧料的变动，按照业务规则设置正负号
            # 欠料+，还料-，存料+，存料抵扣-
            for item in old_material_items:
                if item.material_name and item.settlement_type:
                    net_weight = float(item.net_weight or 0)
                    if net_weight > 0:
                        if item.settlement_type == 'owe':  # 欠料
                            order_record[f'{item.material_name}_debt'] += net_weight
                        elif item.settlement_type == 'return':  # 还料
                            order_record[f'{item.material_name}_debt'] -= net_weight
                        elif item.settlement_type == 'store':  # 存料
                            order_record[f'{item.material_name}_storage'] += net_weight
                        elif item.settlement_type == 'deduct':  # 存料抵扣
                            order_record[f'{item.material_name}_storage'] -= net_weight

            # 检查订单是否有实际的款料变动，如果没有变动就不显示
            has_changes = False

            # 检查款项变动
            if order_record['debt_amount'] != 0 or order_record['deposit_amount'] != 0:
                has_changes = True

            # 检查旧料变动
            if not has_changes:
                for material in all_materials:
                    if order_record[f'{material.name}_debt'] != 0 or order_record[f'{material.name}_storage'] != 0:
                        has_changes = True
                        break

            # 只有有变动的订单才添加到明细中
            if has_changes:
                details.append(order_record)

        # 3. 获取其他项目往来单据中的款项变动
        # 欠款+，还款-，存款+，存款抵扣-
        # 注意：Transaction表没有is_deleted字段，所有记录都参与计算
        other_transactions = Transaction.query.filter_by(customer_id=customer_id).order_by(Transaction.business_time).all()
        for transaction in other_transactions:
            debt_amount = 0
            deposit_amount = 0

            if transaction.notes:
                if transaction.notes == '存款抵扣':
                    # 存款抵扣，减少存款（负值）
                    deposit_amount = -float(transaction.amount) if transaction.amount else 0
                elif transaction.notes == '存款':
                    # 存款，增加存款（正值）
                    deposit_amount = float(transaction.amount) if transaction.amount else 0
                elif '还款' in transaction.notes:
                    # 还款，减少欠款（负值）
                    debt_amount = -float(transaction.amount) if transaction.amount else 0
                else:
                    # 其他情况，只有非存款相关的才根据is_income判断欠款
                    # 存款相关的已经在上面处理，不应该再计算为欠款
                    if transaction.is_income:
                        # 收入增加欠款（客户欠我们的钱）
                        debt_amount = float(transaction.amount) if transaction.amount else 0
                    else:
                        # 支出减少欠款（我们欠客户的钱）
                        debt_amount = -float(transaction.amount) if transaction.amount else 0
            else:
                # 没有备注的情况，其他项目往来不应该自动计算为欠款
                # 只有明确标注的业务才影响欠款，避免误判
                debt_amount = 0
                deposit_amount = 0

            if debt_amount != 0 or deposit_amount != 0:  # 只显示有款项变动的记录
                transaction_record = {
                    'business_time': transaction.business_time.strftime('%Y-%m-%d %H:%M') if transaction.business_time else '',
                    'document_type': '其他项目',
                    'document_id': transaction.transaction_no,  # 使用交易编号而不是ID
                    'document_link': f'/other_transactions/{transaction.id}',  # 添加超链接
                    'remark': transaction.notes or '',  # 直接使用备注内容
                    'debt_amount': debt_amount,
                    'deposit_amount': deposit_amount
                }

                # 初始化所有旧料字段为0
                for material in all_materials:
                    transaction_record[f'{material.name}_debt'] = 0
                    transaction_record[f'{material.name}_storage'] = 0

                details.append(transaction_record)

        # 按时间排序
        details.sort(key=lambda x: x['business_time'] if x['business_time'] != '初始值' else '0000-00-00 00:00')

        # 构建旧料列表信息
        materials_info = []
        for material in all_materials:
            materials_info.append({
                'name': material.name,
                'code': material.code
            })

        return jsonify({
            'success': True,
            'details': details,
            'materials': materials_info  # 返回旧料列表信息
        })
    except Exception as e:
        app.logger.error(f"获取客户款料明细失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"获取客户款料明细失败: {str(e)}"
        })

def api_customer_sales_statistics():
    """客户销售统计API"""
    app, db = get_app_db()
    try:
        from sqlalchemy import text
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        customer_type = request.args.get('customer_type', '')
        customer_id = request.args.get('customer_id', '')

        # 构建查询条件
        query_conditions = []
        params = {}

        # 日期条件（必须有）- 使用业务时间order_date字段
        if start_date:
            query_conditions.append("DATE(o.order_date) >= :start_date")
            params['start_date'] = start_date

        if end_date:
            query_conditions.append("DATE(o.order_date) <= :end_date")
            params['end_date'] = end_date

        # 客户类型条件（可选）
        if customer_type:
            query_conditions.append("c.customer_type = :customer_type")
            params['customer_type'] = customer_type

        # 特定客户条件（可选）
        if customer_id:
            query_conditions.append("o.customer_id = :customer_id")
            params['customer_id'] = customer_id

        # 如果没有任何条件，至少要有基本的WHERE子句
        where_clause = "WHERE " + " AND ".join(query_conditions) if query_conditions else "WHERE 1=1"

        # 查询采购克重统计（新货出库）
        purchase_weight_sql = f"""
            SELECT
                COALESCE(p.code, oi.product_code) as product_code,
                COALESCE(p.name, oi.product_name) as product_name,
                COALESCE(p.category, '其他') as product_category,
                SUM(oi.weight) as total_weight
            FROM orders o
            JOIN customers c ON o.customer_id = c.id
            JOIN order_items oi ON o.id = oi.order_id
            LEFT JOIN products p ON oi.product_id = p.id
            {where_clause}
            AND o.is_deleted = 0
            AND oi.item_type = 'new'
            GROUP BY COALESCE(p.code, oi.product_code), COALESCE(p.name, oi.product_name), COALESCE(p.category, '其他')
            ORDER BY COALESCE(p.category, '其他'), COALESCE(p.code, oi.product_code)
        """

        app.logger.info(f"采购克重查询SQL: {purchase_weight_sql}")
        app.logger.info(f"查询参数: {params}")

        purchase_weight_result = db.session.execute(text(purchase_weight_sql), params).fetchall()

        app.logger.info(f"采购克重查询结果: {len(purchase_weight_result)} 条记录")
        for row in purchase_weight_result:
            app.logger.info(f"产品编号: {row.product_code}, 产品名称: {row.product_name}, 分类: {row.product_category}, 克重: {row.total_weight}")

        # 构建采购克重数据
        purchase_weight = {}
        for row in purchase_weight_result:
            purchase_weight[row.product_code] = float(row.total_weight or 0)

        # 查询退货克重统计（退货录入）
        return_weight_sql = f"""
            SELECT
                COALESCE(p.code, oi.product_code) as product_code,
                COALESCE(p.name, oi.product_name) as product_name,
                COALESCE(p.category, '其他') as product_category,
                SUM(oi.weight) as total_weight
            FROM orders o
            JOIN customers c ON o.customer_id = c.id
            JOIN order_items oi ON o.id = oi.order_id
            LEFT JOIN products p ON oi.product_id = p.id
            {where_clause}
            AND o.is_deleted = 0
            AND oi.item_type = 'return'
            GROUP BY COALESCE(p.code, oi.product_code), COALESCE(p.name, oi.product_name), COALESCE(p.category, '其他')
            ORDER BY COALESCE(p.category, '其他'), COALESCE(p.code, oi.product_code)
        """

        return_weight_result = db.session.execute(text(return_weight_sql), params).fetchall()

        # 构建退货克重数据
        return_weight = {}
        for row in return_weight_result:
            return_weight[row.product_code] = float(row.total_weight or 0)

        # 查询销售明细统计（旧料相关数据）
        from models import OldMaterial

        # 获取所有旧料
        old_materials = OldMaterial.query.filter_by(is_active=True).all()
        app.logger.info(f"找到 {len(old_materials)} 种旧料")
        for material in old_materials:
            app.logger.info(f"旧料: {material.name}")

        # 构建销售明细数据
        sales_detail = {}

        # 为每种旧料和每种统计类型初始化数据
        for material in old_materials:
            for stat_type in ['settle', 'debt', 'return', 'storage', 'deduct']:
                key = f"{material.name}_{stat_type}"
                sales_detail[key] = 0

        # 查询实际的旧料统计数据（使用净重）
        sales_detail_sql = f"""
            SELECT
                oi.material_name,
                oi.settlement_type,
                SUM(oi.net_weight) as total_net_weight
            FROM orders o
            JOIN customers c ON o.customer_id = c.id
            JOIN order_items oi ON o.id = oi.order_id
            {where_clause}
            AND o.is_deleted = 0
            AND oi.item_type = 'old'
            AND oi.material_name IS NOT NULL
            GROUP BY oi.material_name, oi.settlement_type
        """

        app.logger.info(f"旧料统计查询SQL: {sales_detail_sql}")
        app.logger.info(f"查询参数: {params}")

        sales_detail_results = db.session.execute(text(sales_detail_sql), params).fetchall()

        app.logger.info(f"旧料统计查询结果: {len(sales_detail_results)} 条记录")

        # 处理查询结果
        for row in sales_detail_results:
            app.logger.info(f"旧料: {row.material_name}, 结算方式: {row.settlement_type}, 净重: {row.total_net_weight}")
            material_name = row.material_name
            settlement_type = row.settlement_type
            net_weight = float(row.total_net_weight or 0)

            # 映射settlement_type到统计类型（支持中英文）
            stat_type_mapping = {
                # 英文代码
                'settle': 'settle',    # 结料
                'owe': 'debt',         # 欠料
                'return': 'return',    # 还料
                'store': 'storage',    # 存料
                'deduct': 'deduct',    # 存料抵扣
                # 中文名称
                '结料': 'settle',
                '欠料': 'debt',
                '还料': 'return',
                '存料': 'storage',
                '存料抵扣': 'deduct',
                # 其他可能的值
                'debt': 'debt',
                'storage': 'storage'
            }

            stat_type = stat_type_mapping.get(settlement_type)
            if stat_type:
                key = f"{material_name}_{stat_type}"
                sales_detail[key] = net_weight
                app.logger.info(f"设置旧料统计: {key} = {net_weight}")
            else:
                app.logger.warning(f"未识别的结算方式: {settlement_type}")

        # 计算精确的统计指标
        # 查询订单数据以计算总采购金额、采购频次和平均工费
        orders_sql = f"""
            SELECT
                o.id as order_id,
                o.total_amount,
                oi.amount as settlement_amount,
                oi.labor_cost,
                oi.weight,
                oi.item_type
            FROM orders o
            JOIN customers c ON o.customer_id = c.id
            JOIN order_items oi ON o.id = oi.order_id
            {where_clause}
            AND o.is_deleted = 0
            AND oi.item_type = 'new'
        """

        app.logger.info(f"订单统计查询SQL: {orders_sql}")
        app.logger.info(f"查询参数: {params}")

        orders_results = db.session.execute(text(orders_sql), params).fetchall()

        # 计算精确指标
        total_amount = 0  # 总采购金额
        total_labor_fee = 0  # 总工费
        total_weight = 0  # 总重量
        order_ids = set()  # 订单ID集合，用于计算采购频次

        for row in orders_results:
            order_id = row.order_id
            settlement_amount = float(row.settlement_amount or 0)
            labor_cost = float(row.labor_cost or 0)
            weight = float(row.weight or 0)

            # 累计总采购金额（新货出库的结算金额）
            total_amount += settlement_amount

            # 累计总工费和总重量
            total_labor_fee += labor_cost * weight  # 工费 * 重量
            total_weight += weight

            # 记录订单ID
            order_ids.add(order_id)

        # 计算采购频次（不重复的订单数量）
        purchase_frequency = len(order_ids)

        # 计算平均工费/克
        avg_labor_fee = total_labor_fee / total_weight if total_weight > 0 else 0

        app.logger.info(f"精确计算结果: 总金额={total_amount}, 采购频次={purchase_frequency}, 平均工费={avg_labor_fee}")

        return jsonify({
            'success': True,
            'purchase_weight': purchase_weight,
            'return_weight': return_weight,
            'sales_detail': sales_detail,
            # 新增精确计算的指标
            'total_amount': total_amount,
            'purchase_frequency': purchase_frequency,
            'avg_labor_fee': avg_labor_fee,
            'total_weight': total_weight,
            'total_labor_fee': total_labor_fee
        })

    except Exception as e:
        app.logger.error(f"获取客户销售统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"获取客户销售统计失败: {str(e)}"
        }), 500

def api_customer_material_balances_get(customer_id):
    """获取客户旧料余额API"""
    app, db = get_app_db()
    try:
        from models import Customer, CustomerMaterialBalance

        # 验证客户是否存在
        customer = Customer.query.get(customer_id)
        if not customer:
            return jsonify({
                'success': False,
                'message': '客户不存在'
            })

        # 获取客户的旧料余额
        balances = CustomerMaterialBalance.query.filter_by(customer_id=customer_id).all()

        balances_data = []
        for balance in balances:
            balances_data.append({
                'material_name': balance.material_name,
                'owed_weight': float(balance.owed_weight or 0),
                'stored_weight': float(balance.stored_weight or 0)
            })

        return jsonify({
            'success': True,
            'balances': balances_data
        })

    except Exception as e:
        app.logger.error(f"获取客户旧料余额失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"获取客户旧料余额失败: {str(e)}"
        })

def api_supplier_details(supplier_id):
    """获取供应商款料明细API"""
    app, db = get_app_db()
    try:
        app.logger.info(f"开始获取供应商款料明细，supplier_id: {supplier_id}")
        from models import (Supplier, SupplierTransaction, MaterialTransaction, MoneyTransaction,
                           OldMaterial, SupplierMaterialBalance, Purchase, PurchaseItem, Product)

        # 验证供应商是否存在
        supplier = Supplier.query.get(supplier_id)
        if not supplier:
            return jsonify({
                'success': False,
                'message': '供应商不存在'
            })

        details = []

        # 获取所有旧料类型（按编号排序）
        all_materials = OldMaterial.query.order_by(OldMaterial.code).all()

        # 获取供应商的旧料初始值
        material_balances = {}
        for balance in supplier.material_balances:
            material_balances[balance.material_name] = {
                'owed_weight': float(balance.owed_weight or 0),
                'stored_weight': float(balance.stored_weight or 0)
            }

        # 添加初始值记录 - 使用SupplierMaterialBalance表中的实际初始值
        initial_record = {
            'business_time': '初始值',
            'related_document': '初始值',
            'notes': '',  # 删除初始值行的单据备注
            'debt_amount': float(supplier.initial_owed_amount or 0),  # 使用供应商的初始欠款金额
            'deposit_amount': float(supplier.initial_deposit_amount or 0)  # 使用供应商的初始存款金额
        }

        # 为每种旧料添加初始值 - 使用SupplierMaterialBalance表中的实际值
        for material in all_materials:
            balance = material_balances.get(material.name, {'owed_weight': 0, 'stored_weight': 0})
            initial_record[f'{material.name}_debt'] = float(balance['owed_weight'])
            initial_record[f'{material.name}_storage'] = float(balance['stored_weight'])

        details.append(initial_record)

        # 判断交易类型的辅助函数


        # 初始化累计值（从SupplierMaterialBalance表中的实际初始值开始）
        current_balances = {
            'debt_amount': float(supplier.initial_owed_amount or 0),  # 使用供应商的初始欠款金额
            'deposit_amount': float(supplier.initial_deposit_amount or 0)  # 使用供应商的初始存款金额
        }

        # 初始化旧料累计值 - 从SupplierMaterialBalance表中的实际初始值开始
        for material in all_materials:
            balance = material_balances.get(material.name, {'owed_weight': 0, 'stored_weight': 0})
            current_balances[f'{material.name}_debt'] = float(balance['owed_weight'])
            current_balances[f'{material.name}_storage'] = float(balance['stored_weight'])

        # 获取所有相关单据并按时间排序
        all_documents = []

        # 1. 获取供应商往来记录
        supplier_transactions = SupplierTransaction.query.filter_by(
            supplier_id=supplier_id
        ).order_by(SupplierTransaction.business_date.asc(), SupplierTransaction.created_at.asc()).all()

        for transaction in supplier_transactions:
            all_documents.append({
                'type': 'supplier_transaction',
                'data': transaction,
                'business_time': transaction.business_date,
                'created_at': transaction.created_at
            })

        # 2. 获取采购单据记录
        purchases = Purchase.query.filter_by(
            is_deleted=False
        ).join(PurchaseItem, Purchase.id == PurchaseItem.purchase_id).filter(
            PurchaseItem.supplier_id == supplier_id,
            PurchaseItem.is_deleted == False
        ).order_by(Purchase.business_time.asc(), Purchase.created_at.asc()).all()

        for purchase in purchases:
            # 确保business_time是date类型用于排序
            if hasattr(purchase.business_time, 'date'):
                business_date = purchase.business_time.date()
            elif hasattr(purchase.business_time, 'year'):
                business_date = purchase.business_time
            else:
                # 如果是字符串，尝试解析
                try:
                    from datetime import datetime
                    business_date = datetime.strptime(str(purchase.business_time), '%Y-%m-%d').date()
                except:
                    business_date = purchase.business_time

            all_documents.append({
                'type': 'purchase',
                'data': purchase,
                'business_time': business_date,
                'created_at': purchase.created_at
            })

        # 3. 获取其他供应商往来单据中以该供应商为来源的记录
        app.logger.info(f"查找以供应商{supplier.name}为还料/存料来源的其他供应商往来记录")

        # 查找还料来源为该供应商的记录
        return_source_transactions = db.session.query(SupplierTransaction).join(
            MaterialTransaction, SupplierTransaction.id == MaterialTransaction.transaction_id
        ).filter(
            MaterialTransaction.return_source == supplier.name,
            MaterialTransaction.return_weight > 0
        ).order_by(SupplierTransaction.business_date.asc(), SupplierTransaction.created_at.asc()).all()

        # 查找存料来源为该供应商的记录
        store_source_transactions = db.session.query(SupplierTransaction).join(
            MaterialTransaction, SupplierTransaction.id == MaterialTransaction.transaction_id
        ).filter(
            MaterialTransaction.store_source == supplier.name,
            MaterialTransaction.store_weight > 0
        ).order_by(SupplierTransaction.business_date.asc(), SupplierTransaction.created_at.asc()).all()

        # 合并并去重
        source_transactions = list(set(return_source_transactions + store_source_transactions))

        app.logger.info(f"找到{len(source_transactions)}个其他供应商往来单据涉及该供应商作为来源")

        for transaction in source_transactions:
            all_documents.append({
                'type': 'source_transaction',
                'data': transaction,
                'business_time': transaction.business_date,
                'created_at': transaction.created_at
            })

        # 按时间排序所有单据（统一转换为字符串进行比较）
        def sort_key(x):
            business_time = x['business_time']
            created_at = x['created_at']

            # 统一转换为字符串进行比较，避免类型不匹配
            if hasattr(business_time, 'strftime'):
                # datetime或date对象
                business_time_str = business_time.strftime('%Y-%m-%d')
            else:
                # 字符串或其他类型
                business_time_str = str(business_time)

            if hasattr(created_at, 'strftime'):
                created_at_str = created_at.strftime('%Y-%m-%d %H:%M:%S')
            else:
                created_at_str = str(created_at)

            return (business_time_str, created_at_str)

        all_documents.sort(key=sort_key)

        for doc in all_documents:
            if doc['type'] == 'supplier_transaction':
                transaction = doc['data']

                # 计算本次交易的变化
                material_changes = {}
                for material in all_materials:
                    material_changes[f'{material.name}_debt'] = 0.0
                    material_changes[f'{material.name}_storage'] = 0.0

                # 处理物料交易（使用原有正确的统计逻辑）
                for mt in transaction.material_transactions:
                    # 1. 寄料：减少欠料（使用实际到料重量）
                    if mt.actual_deposit_weight and mt.actual_deposit_weight > 0:
                        if mt.deposit_material_type:
                            material_changes[f'{mt.deposit_material_type}_debt'] -= float(mt.actual_deposit_weight)

                    # 2. 还料：减少欠料（使用还料重量）
                    if mt.return_weight and mt.return_weight > 0:
                        if mt.return_material_type:
                            # 还料减少欠料
                            material_changes[f'{mt.return_material_type}_debt'] -= float(mt.return_weight)

                    # 3. 存料：增加存料
                    if mt.store_weight and mt.store_weight > 0:
                        if mt.store_material_type:
                            material_changes[f'{mt.store_material_type}_storage'] += float(mt.store_weight)

                # 处理款项交易（使用原有正确的统计逻辑）
                for money_trans in transaction.money_transactions:
                    # 处理还款金额
                    if money_trans.return_amount and money_trans.return_amount > 0:
                        # 判断是买料表格还是还款表格
                        if money_trans.return_material_type and money_trans.return_weight and money_trans.return_weight > 0:
                            # 买料表格：有旧料类型和买料克重
                            # 减少欠料克重
                            material_changes[f'{money_trans.return_material_type}_debt'] -= float(money_trans.return_weight)
                            # 减少存款（存款抵扣）
                            current_balances['deposit_amount'] -= float(money_trans.return_amount)
                        else:
                            # 还款表格：没有买料克重，还款用途只是备注
                            # 减少欠款
                            current_balances['debt_amount'] -= float(money_trans.return_amount)

                    # 存款：增加存款
                    if money_trans.store_amount and money_trans.store_amount > 0:
                        current_balances['deposit_amount'] += float(money_trans.store_amount)

                # 应用物料变化到累计值
                for material in all_materials:
                    current_balances[f'{material.name}_debt'] += material_changes[f'{material.name}_debt']
                    current_balances[f'{material.name}_storage'] += material_changes[f'{material.name}_storage']

                # 检查是否有实际变化（物料变化或款项变化）
                has_material_changes = any(abs(change) > 0.001 for change in material_changes.values())
                has_money_changes = (
                    len(transaction.money_transactions) > 0 and
                    any(
                        (mt.return_amount and abs(mt.return_amount) > 0.001) or
                        (mt.store_amount and abs(mt.store_amount) > 0.001)
                        for mt in transaction.money_transactions
                    )
                )
                has_changes = has_material_changes or has_money_changes

                # 只有有变化的记录才显示
                if has_changes:
                    # 生成描述性的文档类型
                    doc_types = []
                    for mt in transaction.material_transactions:
                        if mt.actual_deposit_weight and mt.actual_deposit_weight > 0:
                            doc_types.append('寄料')
                        if mt.return_weight and mt.return_weight > 0:
                            doc_types.append('还料')
                        if mt.store_weight and mt.store_weight > 0:
                            doc_types.append('存料')

                    for money_trans in transaction.money_transactions:
                        if money_trans.return_purpose == '买料' and money_trans.return_amount and money_trans.return_amount > 0:
                            doc_types.append('买料')
                        elif money_trans.return_amount and money_trans.return_amount > 0:
                            doc_types.append('还款')
                        if money_trans.store_amount and money_trans.store_amount > 0:
                            doc_types.append('存款')

                    document_type = '/'.join(doc_types) if doc_types else '往来'

                    # 格式化业务时间，显示到分钟（与供应商往来单据页面一致）
                    # 供应商往来单据页面显示：created_at（录入时间）到分钟
                    if hasattr(transaction, 'created_at') and transaction.created_at:
                        business_time_str = transaction.created_at.strftime('%Y-%m-%d %H:%M')
                    elif hasattr(transaction.business_date, 'strftime'):
                        business_time_str = transaction.business_date.strftime('%Y-%m-%d')
                    else:
                        business_time_str = str(transaction.business_date)

                    # 计算本次变化（款项变化）
                    debt_amount_change = 0.0
                    deposit_amount_change = 0.0

                    for money_trans in transaction.money_transactions:
                        if money_trans.return_amount and money_trans.return_amount > 0:
                            if money_trans.return_material_type and money_trans.return_weight and money_trans.return_weight > 0:
                                # 买料表格：减少存款
                                deposit_amount_change -= float(money_trans.return_amount)
                            else:
                                # 还款表格：减少欠款
                                debt_amount_change -= float(money_trans.return_amount)

                        if money_trans.store_amount and money_trans.store_amount > 0:
                            deposit_amount_change += float(money_trans.store_amount)

                    record = {
                        'business_time': business_time_str,
                        'related_document': transaction.transaction_no,
                        'document_link': f'/supplier_transaction_edit/{transaction.id}',  # 添加超链接
                        'document_id': transaction.id,  # 添加文档ID
                        'notes': transaction.notes or '',
                        'debt_amount': debt_amount_change,  # 显示变化值
                        'deposit_amount': deposit_amount_change  # 显示变化值
                    }

                    # 添加旧料变化值
                    for material in all_materials:
                        record[f'{material.name}_debt'] = material_changes[f'{material.name}_debt']
                        record[f'{material.name}_storage'] = material_changes[f'{material.name}_storage']

                    details.append(record)

            elif doc['type'] == 'purchase':
                purchase = doc['data']

                # 计算本次采购的变化
                material_changes = {}
                for material in all_materials:
                    material_changes[f'{material.name}_debt'] = 0.0
                    material_changes[f'{material.name}_storage'] = 0.0

                # 获取该供应商的采购项目
                purchase_items = PurchaseItem.query.filter_by(
                    purchase_id=purchase.id,
                    supplier_id=supplier_id,
                    is_deleted=False
                ).all()

                total_owed_amount_change = 0.0
                total_owed_gold_change = 0.0

                for item in purchase_items:
                    # 检查产品类别是否为黄金
                    is_gold_product = False
                    if item.product_id:
                        product = Product.query.get(item.product_id)
                        if product and product.category in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']:
                            is_gold_product = True
                    elif item.product_type in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']:
                        is_gold_product = True

                    # 如果是黄金产品，增加旧料足金欠料
                    if is_gold_product and item.weight:
                        material_changes['旧料足金_debt'] += float(item.weight)
                        total_owed_gold_change += float(item.weight)

                    # 增加欠款金额（工费+结价金额）
                    labor_fee = float(item.labor_fee or 0)
                    final_amount = float(item.final_amount or 0)
                    total_amount = labor_fee + final_amount
                    if total_amount > 0:
                        total_owed_amount_change += total_amount

                # 应用变化到累计值
                for material in all_materials:
                    current_balances[f'{material.name}_debt'] += material_changes[f'{material.name}_debt']
                    current_balances[f'{material.name}_storage'] += material_changes[f'{material.name}_storage']

                current_balances['debt_amount'] += total_owed_amount_change

                # 检查是否有实际变化
                has_changes = total_owed_amount_change > 0.001 or total_owed_gold_change > 0.001

                if has_changes:
                    # 格式化业务时间
                    if hasattr(purchase.business_time, 'strftime'):
                        business_time_str = purchase.business_time.strftime('%Y-%m-%d %H:%M')
                    else:
                        business_time_str = str(purchase.business_time)

                    record = {
                        'business_time': business_time_str,
                        'related_document': purchase.purchase_no,
                        'document_link': f'/purchases/edit/{purchase.id}',  # 添加超链接
                        'document_id': purchase.id,  # 添加文档ID
                        'notes': purchase.notes or '',
                        'debt_amount': total_owed_amount_change,  # 显示变化值
                        'deposit_amount': 0.0  # 采购单据不涉及存款变化
                    }

                    # 添加旧料变化值
                    for material in all_materials:
                        record[f'{material.name}_debt'] = material_changes[f'{material.name}_debt']
                        record[f'{material.name}_storage'] = material_changes[f'{material.name}_storage']

                    details.append(record)

            elif doc['type'] == 'source_transaction':
                # 处理其他供应商往来单据中以该供应商为来源的记录
                transaction = doc['data']

                # 计算该供应商作为来源的旧料变化
                material_changes = {}
                for material in all_materials:
                    material_changes[f'{material.name}_debt'] = 0.0
                    material_changes[f'{material.name}_storage'] = 0.0

                # 查找涉及该供应商作为来源的物料交易
                for mt in transaction.material_transactions:
                    # 还料来源为该供应商：该供应商欠料增加
                    if (mt.return_source == supplier.name and
                        mt.return_weight and mt.return_weight > 0 and
                        mt.return_material_type):
                        material_changes[f'{mt.return_material_type}_debt'] += float(mt.return_weight)

                    # 存料来源为该供应商：该供应商欠料增加
                    if (mt.store_source == supplier.name and
                        mt.store_weight and mt.store_weight > 0 and
                        mt.store_material_type):
                        material_changes[f'{mt.store_material_type}_debt'] += float(mt.store_weight)

                # 应用物料变化到累计值
                for material in all_materials:
                    current_balances[f'{material.name}_debt'] += material_changes[f'{material.name}_debt']
                    current_balances[f'{material.name}_storage'] += material_changes[f'{material.name}_storage']

                # 检查是否有实际变化
                has_changes = any(abs(change) > 0.001 for change in material_changes.values())

                if has_changes:
                    # 生成描述性的文档类型
                    doc_types = []
                    for mt in transaction.material_transactions:
                        if mt.return_source == supplier.name and mt.return_weight and mt.return_weight > 0:
                            doc_types.append(f'还料来源({mt.return_material_type})')
                        if mt.store_source == supplier.name and mt.store_weight and mt.store_weight > 0:
                            doc_types.append(f'存料来源({mt.store_material_type})')

                    document_type = '/'.join(doc_types) if doc_types else '来源'

                    # 格式化业务时间
                    if hasattr(transaction, 'created_at') and transaction.created_at:
                        business_time_str = transaction.created_at.strftime('%Y-%m-%d %H:%M')
                    elif hasattr(transaction.business_date, 'strftime'):
                        business_time_str = transaction.business_date.strftime('%Y-%m-%d')
                    else:
                        business_time_str = str(transaction.business_date)

                    # 获取发起往来的供应商名称
                    source_supplier = Supplier.query.get(transaction.supplier_id)
                    source_supplier_name = source_supplier.name if source_supplier else '未知供应商'

                    record = {
                        'business_time': business_time_str,
                        'related_document': transaction.transaction_no,
                        'document_link': f'/supplier_transaction_edit/{transaction.id}',
                        'document_id': transaction.id,
                        'notes': '借料',
                        'debt_amount': 0.0,  # 来源记录不涉及款项变化
                        'deposit_amount': 0.0
                    }

                    # 添加旧料变化值
                    for material in all_materials:
                        record[f'{material.name}_debt'] = material_changes[f'{material.name}_debt']
                        record[f'{material.name}_storage'] = material_changes[f'{material.name}_storage']

                    details.append(record)

        # 按时间排序
        details.sort(key=lambda x: x['business_time'] if x['business_time'] != '初始值' else '0000-00-00')

        # 构建旧料列表信息
        materials_info = []
        for material in all_materials:
            materials_info.append({
                'name': material.name,
                'code': material.code
            })

        app.logger.info(f"供应商款料明细获取成功，返回{len(details)}条记录")
        return jsonify({
            'success': True,
            'details': details,
            'materials': materials_info
        })
    except Exception as e:
        app.logger.error(f"获取供应商款料明细失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"获取供应商款料明细失败: {str(e)}"
        })

def api_supplier_previous_balance(supplier_id):
    """获取供应商在指定时间点的上期余额API"""
    app, db = get_app_db()
    try:
        from models import Supplier
        from datetime import datetime

        # 获取查询参数
        business_date = request.args.get('business_date')
        exclude_transaction_id = request.args.get('exclude_transaction_id')

        if not business_date:
            return jsonify({
                'success': False,
                'message': '缺少业务日期参数'
            })

        try:
            # 支持两种日期格式：YYYY-MM-DD 和 YYYY-MM-DDTHH:MM
            if 'T' in business_date:
                # datetime-local格式，保留完整的datetime信息
                query_datetime = datetime.strptime(business_date, '%Y-%m-%dT%H:%M')
                business_date_obj = query_datetime.date()
            else:
                # date格式，转换为当天的开始时间
                query_datetime = datetime.strptime(business_date, '%Y-%m-%d')
                business_date_obj = query_datetime.date()
        except ValueError:
            return jsonify({
                'success': False,
                'message': '业务日期格式错误，支持格式：YYYY-MM-DD 或 YYYY-MM-DDTHH:MM'
            })

        # 验证供应商是否存在
        supplier = Supplier.query.get(supplier_id)
        if not supplier:
            return jsonify({
                'success': False,
                'message': '供应商不存在'
            })

        app.logger.info(f"API调用：计算供应商{supplier.name}的上期余额，业务日期：{business_date_obj}，排除交易ID：{exclude_transaction_id}")

        # 直接调用修复后的计算函数，确保包含借料记录
        result = calculate_supplier_previous_balance(
            supplier_id,
            business_date_obj,
            exclude_transaction_id
        )

        if result:
            app.logger.info(f"API返回：上期数据计算成功，包含{len([k for k in result.keys() if k.startswith('previous_')])}个字段")
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'message': '计算上期余额失败'
            })

    except Exception as e:
        app.logger.error(f"获取供应商上期余额失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"获取供应商上期余额失败: {str(e)}"
        })

    except Exception as e:
        app.logger.error(f"获取供应商上期余额失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"获取供应商上期余额失败: {str(e)}"
        })


def api_supplier_stats():
    """获取供应商统计数据API"""
    app, db = get_app_db()
    try:
        from models import Purchase, PurchaseItem, Product, Supplier, SupplierTransaction, MaterialTransaction, OldMaterial
        from datetime import datetime, time

        # 获取参数
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        supplier_type = request.args.get('supplier_type', '')
        supplier_id = request.args.get('supplier_id', '')

        if not start_date_str or not end_date_str:
            return jsonify({
                'success': False,
                'message': '请提供开始日期和结束日期'
            })

        # 解析日期
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

        # 设置日期时间范围
        start_datetime = datetime.combine(start_date, time.min)
        end_datetime = datetime.combine(end_date, time.max)

        app.logger.info(f"获取供应商统计数据: {start_date} 到 {end_date}, 供应商类型: {supplier_type}, 供应商ID: {supplier_id}")

        # 调试：查看数据库中实际的供应商类型分布
        supplier_type_stats = db.session.query(Supplier.supplier_type, func.count(Supplier.id)).group_by(Supplier.supplier_type).all()
        app.logger.info(f"📊 数据库中的供应商类型分布: {dict(supplier_type_stats)}")

        # 构建供应商往来查询条件
        query = db.session.query(SupplierTransaction).filter(
            SupplierTransaction.business_date >= start_date,
            SupplierTransaction.business_date <= end_date
        )

        # 按供应商类型筛选
        if supplier_type:
            app.logger.info(f"🏭 按供应商类型筛选: {supplier_type}")
            if supplier_type == 'gold':
                query = query.join(Supplier).filter(Supplier.supplier_type == 'gold')
                app.logger.info("筛选金料供应商: supplier_type = 'gold'")
            elif supplier_type == 'product':
                query = query.join(Supplier).filter(Supplier.supplier_type == 'product')
                app.logger.info("筛选产品供应商: supplier_type = 'product'")

        # 按特定供应商筛选
        if supplier_id:
            try:
                supplier_id_int = int(supplier_id)
                query = query.filter(SupplierTransaction.supplier_id == supplier_id_int)
                app.logger.info(f"👤 按特定供应商筛选: ID={supplier_id_int}")

                # 自动根据选择的供应商类型设置筛选条件
                selected_supplier = Supplier.query.get(supplier_id_int)
                if selected_supplier:
                    supplier_type = selected_supplier.supplier_type  # 覆盖原有的supplier_type参数
                    app.logger.info(f"🔄 自动设置供应商类型为: {supplier_type} (根据选择的供应商: {selected_supplier.name})")

            except ValueError:
                app.logger.warning(f"❌ 无效的供应商ID: {supplier_id}")

        # 执行查询
        supplier_transactions = query.all()
        app.logger.info(f"📋 找到 {len(supplier_transactions)} 条供应商往来记录")

        if len(supplier_transactions) > 0:
            app.logger.info(f"📝 第一条记录: ID={supplier_transactions[0].id}, 供应商={supplier_transactions[0].supplier.name}, 日期={supplier_transactions[0].business_date}")
        else:
            app.logger.warning("⚠️ 没有找到符合条件的供应商往来记录")

        # 统计数据初始化
        purchase_weight = {}  # 采购统计（从采购单据中统计）
        return_weight = {}    # 退货统计（从退货单据中统计）
        total_amount = 0      # 总往来金额
        total_weight = 0      # 总往来黄金克重
        transaction_count = len(supplier_transactions)
        material_detail = {}  # 料项统计详情

        # 查询采购单据数据
        app.logger.info("📦 开始查询采购单据数据...")
        purchase_query = db.session.query(Purchase).filter(
            Purchase.business_time >= start_datetime,
            Purchase.business_time <= end_datetime,
            Purchase.is_deleted == False
        )

        # 按供应商类型筛选采购单据
        if supplier_type:
            app.logger.info(f"🏭 按供应商类型筛选采购单据: {supplier_type}")
            if supplier_type == 'gold':
                # 通过PurchaseItem表关联Supplier表进行筛选
                purchase_query = purchase_query.join(PurchaseItem, Purchase.id == PurchaseItem.purchase_id)\
                                             .join(Supplier, PurchaseItem.supplier_id == Supplier.id)\
                                             .filter(Supplier.supplier_type == 'gold')
                app.logger.info("筛选金料供应商的采购单据: supplier_type = 'gold'")
            elif supplier_type == 'product':
                # 通过PurchaseItem表关联Supplier表进行筛选
                purchase_query = purchase_query.join(PurchaseItem, Purchase.id == PurchaseItem.purchase_id)\
                                             .join(Supplier, PurchaseItem.supplier_id == Supplier.id)\
                                             .filter(Supplier.supplier_type == 'product')
                app.logger.info("筛选产品供应商的采购单据: supplier_type = 'product'")

        # 按特定供应商筛选采购单据
        if supplier_id:
            try:
                supplier_id_int = int(supplier_id)
                # 如果之前没有JOIN PurchaseItem，需要先JOIN
                if supplier_type:
                    # 已经JOIN了，直接添加筛选条件
                    purchase_query = purchase_query.filter(PurchaseItem.supplier_id == supplier_id_int)
                else:
                    # 没有JOIN，需要先JOIN PurchaseItem
                    purchase_query = purchase_query.join(PurchaseItem, Purchase.id == PurchaseItem.purchase_id)\
                                                 .filter(PurchaseItem.supplier_id == supplier_id_int)
                app.logger.info(f"👤 按特定供应商筛选采购单据: ID={supplier_id_int}")
            except ValueError:
                app.logger.warning(f"❌ 无效的供应商ID: {supplier_id}")

        # 使用distinct()去重，避免JOIN产生重复记录
        purchases = purchase_query.distinct().all()
        app.logger.info(f"📦 找到 {len(purchases)} 条采购单据")

        # 获取所有产品用于分类
        products = Product.query.all()
        product_dict = {p.id: p for p in products}

        # 统计采购数据
        total_purchase_amount = 0
        total_labor_fee = 0
        total_purchase_weight = 0

        for purchase in purchases:
            total_purchase_amount += float(purchase.total_amount or 0)

            # 统计采购项
            for item in purchase.items:
                if item.is_deleted:
                    continue

                product = product_dict.get(item.product_id)
                if not product:
                    continue

                weight = float(item.weight or 0)
                labor_fee = float(item.labor_fee or 0)

                # 按产品代码统计采购重量
                product_code = product.code or f"product_{product.id}"
                if product_code not in purchase_weight:
                    purchase_weight[product_code] = 0
                purchase_weight[product_code] += weight

                app.logger.debug(f"📊 采购统计: 产品[{product.name}] 代码[{product_code}] 重量[{weight}g] 累计[{purchase_weight[product_code]}g]")

                total_purchase_weight += weight
                # 工费直接累加，不乘以重量
                total_labor_fee += labor_fee

        app.logger.info(f"📊 采购统计完成: {len(purchase_weight)}个产品类别, 总重量{total_purchase_weight}克, 总金额{total_purchase_amount}元")
        app.logger.info(f"📊 采购重量明细: {purchase_weight}")

        # 获取所有旧料类型
        old_materials = OldMaterial.query.all()

        # 处理供应商往来数据
        for transaction in supplier_transactions:
            # 统计总金额（存款 + 还款）
            total_amount += float(transaction.total_store_amount or 0)  # 存款
            total_amount += float(transaction.total_return_amount or 0)  # 还款

            # 统计总黄金克重（寄料 + 买料 + 还料 + 存料，只计算旧料足金）
            total_weight += float(transaction.total_deposit_weight or 0)  # 寄料（旧料足金）
            total_weight += float(transaction.total_buy_weight or 0)      # 买料（旧料足金）
            total_weight += float(transaction.total_return_weight or 0)   # 还料（旧料足金）
            total_weight += float(transaction.total_store_weight or 0)    # 存料（旧料足金）

            # 处理料项统计数据
            for material_transaction in transaction.material_transactions:
                # 1. 到料克重：寄料表格的实际寄料重量（actual_deposit_weight）
                if material_transaction.actual_deposit_weight and material_transaction.deposit_material_type:
                    key = f"{material_transaction.deposit_material_type}_arrival"
                    material_detail[key] = material_detail.get(key, 0) + float(material_transaction.actual_deposit_weight)

                # 3. 安排料克重：还料来源为特定供应商的还料克重 + 存料来源为特定供应商的存料克重
                # 注意：这里只统计当前遍历的单据中的安排料数据，不包括以其他供应商为来源的数据
                if material_transaction.return_weight and material_transaction.return_material_type:
                    # 检查还料来源是否为特定供应商（如果没有筛选特定供应商，则统计所有非库存来源）
                    if material_transaction.return_source and material_transaction.return_source != '库存':
                        # 如果筛选了特定供应商，只统计来源为当前供应商的数据
                        if not supplier_id or material_transaction.return_source == transaction.supplier.name:
                            key = f"{material_transaction.return_material_type}_arrange"
                            material_detail[key] = material_detail.get(key, 0) + float(material_transaction.return_weight)
                            app.logger.debug(f"📊 统计安排料(还料): {material_transaction.return_material_type} {material_transaction.return_weight}克, 来源: {material_transaction.return_source}")

                if material_transaction.store_weight and material_transaction.store_material_type:
                    # 检查存料来源是否为特定供应商（如果没有筛选特定供应商，则统计所有非库存来源）
                    if material_transaction.store_source and material_transaction.store_source != '库存':
                        # 如果筛选了特定供应商，只统计来源为当前供应商的数据
                        if not supplier_id or material_transaction.store_source == transaction.supplier.name:
                            key = f"{material_transaction.store_material_type}_arrange"
                            material_detail[key] = material_detail.get(key, 0) + float(material_transaction.store_weight)
                            app.logger.debug(f"📊 统计安排料(存料): {material_transaction.store_material_type} {material_transaction.store_weight}克, 来源: {material_transaction.store_source}")

                # 4. 还料克重：还料表格中的还料克重之和
                if material_transaction.return_weight and material_transaction.return_material_type:
                    key = f"{material_transaction.return_material_type}_return"
                    material_detail[key] = material_detail.get(key, 0) + float(material_transaction.return_weight)

                # 5. 存料克重：存料表格中的存料克重之和
                if material_transaction.store_weight and material_transaction.store_material_type:
                    key = f"{material_transaction.store_material_type}_storage"
                    material_detail[key] = material_detail.get(key, 0) + float(material_transaction.store_weight)

            # 2. 买料克重：从money_transactions中获取买料数据
            for money_transaction in transaction.money_transactions:
                if (money_transaction.return_purpose == '买料' and
                    money_transaction.return_material_type and
                    money_transaction.return_weight):
                    key = f"{money_transaction.return_material_type}_purchase"
                    material_detail[key] = material_detail.get(key, 0) + float(money_transaction.return_weight)

        # 如果筛选了特定供应商，还需要查询所有单据中以该供应商为来源的料项数据
        if supplier_id:
            try:
                supplier_id_int = int(supplier_id)
                target_supplier = Supplier.query.get(supplier_id_int)
                if target_supplier:
                    supplier_name = target_supplier.name
                    app.logger.info(f"🔍 查询以'{supplier_name}'为来源的料项数据...")

                    # 查询所有在日期范围内的供应商往来单据中，以该供应商为来源的料项数据
                    all_transactions_in_range = db.session.query(SupplierTransaction).filter(
                        SupplierTransaction.business_date >= start_date,
                        SupplierTransaction.business_date <= end_date
                    ).all()

                    source_material_count = 0
                    for transaction in all_transactions_in_range:
                        # 查找以目标供应商为来源的还料和存料
                        for material_transaction in transaction.material_transactions:
                            # 还料来源为目标供应商的数据
                            if (material_transaction.return_weight and
                                material_transaction.return_material_type and
                                material_transaction.return_source == supplier_name):
                                key = f"{material_transaction.return_material_type}_arrange"
                                material_detail[key] = material_detail.get(key, 0) + float(material_transaction.return_weight)
                                source_material_count += 1
                                app.logger.info(f"📋 找到还料来源数据: {material_transaction.return_material_type} {material_transaction.return_weight}克")

                            # 存料来源为目标供应商的数据
                            if (material_transaction.store_weight and
                                material_transaction.store_material_type and
                                material_transaction.store_source == supplier_name):
                                key = f"{material_transaction.store_material_type}_arrange"
                                material_detail[key] = material_detail.get(key, 0) + float(material_transaction.store_weight)
                                source_material_count += 1
                                app.logger.info(f"📋 找到存料来源数据: {material_transaction.store_material_type} {material_transaction.store_weight}克")

                    app.logger.info(f"✅ 以'{supplier_name}'为来源的料项数据统计完成，共找到 {source_material_count} 条记录")
            except ValueError:
                app.logger.warning(f"❌ 无效的供应商ID: {supplier_id}")

        # 计算平均工费
        avg_labor_fee = total_labor_fee / total_purchase_weight if total_purchase_weight > 0 else 0

        # 计算净黄金重量（采购的黄金类产品重量）
        gold_categories = ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']
        net_gold_weight = 0
        for product in products:
            if product.category in gold_categories:
                product_code = product.code or f"product_{product.id}"
                net_gold_weight += purchase_weight.get(product_code, 0)

        # 分别计算产品供应商和金料供应商的数据
        from models import Supplier

        # 产品供应商统计数据
        product_supplier_owed_material = 0
        product_supplier_owed_amount = 0

        # 金料供应商统计数据
        gold_supplier_owed_material = 0
        gold_supplier_owed_amount = 0  # 对于金料供应商，这将改为存款
        arrange_weight = 0  # 安排料克重
        buy_weight = 0      # 买料克重
        deposit_weight = 0  # 寄到料克重（到料克重）

        # 从供应商款料明细表格的最后一行计算当前欠料和欠款/存款
        # 获取所有符合筛选条件的供应商
        supplier_ids = set()
        for transaction in supplier_transactions:
            supplier_ids.add(transaction.supplier_id)

        # 如果选择了特定供应商，只计算该供应商
        if supplier_id:
            try:
                supplier_id_int = int(supplier_id)
                supplier_ids = {supplier_id_int}
                app.logger.info(f"🎯 只计算特定供应商: ID={supplier_id_int}")
            except ValueError:
                app.logger.warning(f"❌ 无效的供应商ID: {supplier_id}")
        # 如果没有往来记录且没有选择特定供应商，则根据筛选条件获取所有供应商
        elif not supplier_ids:
            if supplier_type == 'product':
                suppliers = Supplier.query.filter_by(supplier_type='product').all()
            elif supplier_type == 'gold':
                suppliers = Supplier.query.filter_by(supplier_type='gold').all()
            else:
                suppliers = Supplier.query.all()
            supplier_ids = {s.id for s in suppliers}
            app.logger.info(f"📊 计算所有{supplier_type or '全部'}供应商: {len(supplier_ids)}个")

        # 对每个供应商计算其款料明细的最终余额
        for supplier_id_int in supplier_ids:
            supplier = Supplier.query.get(supplier_id_int)
            if not supplier:
                continue

            # 直接计算该供应商的款料明细最终余额（避免循环调用）
            try:
                # 暂时使用原有函数，避免复杂计算导致错误
                final_balances = calculate_supplier_final_balances(supplier_id_int)

                if final_balances:
                    # 获取旧料类型信息
                    from models import OldMaterial
                    all_materials = OldMaterial.query.order_by(OldMaterial.code).all()

                    # 根据供应商类型累加到对应的统计中
                    if supplier.supplier_type == 'product':
                        # 产品供应商：累加旧料足金欠料和欠款
                        product_supplier_owed_material += final_balances.get('旧料足金_debt', 0)
                        product_supplier_owed_amount += final_balances.get('debt_amount', 0)
                    elif supplier.supplier_type == 'gold':
                        # 金料供应商：累加旧料足金欠料和存款
                        gold_supplier_owed_material += final_balances.get('旧料足金_debt', 0)
                        gold_supplier_owed_amount += final_balances.get('deposit_amount', 0)

            except Exception as e:
                app.logger.error(f"计算供应商{supplier_id_int}的款料明细余额失败: {str(e)}")
                # 如果计算失败，回退到供应商表的字段
                if supplier.supplier_type == 'product':
                    product_supplier_owed_material += float(supplier.owed_gold or 0)
                    product_supplier_owed_amount += float(supplier.owed_amount or 0)
                elif supplier.supplier_type == 'gold':
                    gold_supplier_owed_material += float(supplier.owed_gold or 0)
                    gold_supplier_owed_amount += float(supplier.deposit_amount or 0)

        # 从供应商往来数据中统计料项数据（主要针对金料供应商）
        for transaction in supplier_transactions:
            # 只统计金料供应商的往来数据
            if transaction.supplier.supplier_type == 'gold':
                buy_weight += float(transaction.total_buy_weight or 0)

        # 从material_detail中获取各种料项克重（这里已经在前面的循环中统计好了）
        arrange_weight = material_detail.get('旧料足金_arrange', 0)    # 安排料克重
        deposit_weight = material_detail.get('旧料足金_arrival', 0)    # 寄到料克重（到料克重）

        # 确保采购克重总计只计算黄金分类的重量
        gold_purchase_weight = net_gold_weight  # 使用之前计算的净黄金重量

        # 根据实际筛选的供应商类型决定统计卡片数据
        # 如果筛选了金料供应商，产品供应商统计应该为0
        if supplier_type == 'gold':
            # 只显示金料供应商数据，产品供应商数据清零
            final_product_owed_material = 0
            final_product_owed_amount = 0
            final_purchase_weight_total = 0
            final_labor_fee_total = 0
            final_purchase_amount_total = 0
        elif supplier_type == 'product':
            # 只显示产品供应商数据，金料供应商数据清零
            final_product_owed_material = product_supplier_owed_material
            final_product_owed_amount = product_supplier_owed_amount
            final_purchase_weight_total = gold_purchase_weight
            final_labor_fee_total = total_labor_fee
            final_purchase_amount_total = total_purchase_amount
            # 金料供应商数据清零
            gold_supplier_owed_material = 0
            gold_supplier_owed_amount = 0
            arrange_weight = 0
            buy_weight = 0
            deposit_weight = 0
        else:
            # 显示所有数据
            final_product_owed_material = product_supplier_owed_material
            final_product_owed_amount = product_supplier_owed_amount
            final_purchase_weight_total = gold_purchase_weight
            final_labor_fee_total = total_labor_fee
            final_purchase_amount_total = total_purchase_amount

        return jsonify({
            'success': True,
            'purchase_weight': purchase_weight,
            'return_weight': return_weight,
            'material_detail': material_detail,
            # 产品供应商统计卡片数据
            'purchase_weight_total': final_purchase_weight_total,       # 采购克重总计（黄金分类的采购总计克重）
            'labor_fee_total': final_labor_fee_total,                  # 采购工费总计
            'final_amount_total': final_purchase_amount_total,         # 采购结价金额总计
            'product_owed_material': final_product_owed_material,  # 产品供应商当前欠料
            'product_owed_amount': final_product_owed_amount,      # 产品供应商当前欠款
            # 金料供应商统计卡片数据
            'arrange_weight': arrange_weight,                    # 安排料克重（从料项统计表格获取）
            'buy_weight': buy_weight,                           # 买料克重
            'deposit_weight': deposit_weight,                   # 寄到料克重（到料克重）
            'gold_owed_material': gold_supplier_owed_material,  # 金料供应商当前欠料
            'gold_owed_amount': gold_supplier_owed_amount,      # 金料供应商当前存款
            # 保留原有字段以兼容现有代码
            'owed_material': final_product_owed_material,    # 兼容字段
            'owed_amount': final_product_owed_amount,        # 兼容字段
            'material_owed': gold_supplier_owed_material,       # 兼容字段
            'amount_owed': gold_supplier_owed_amount,           # 兼容字段
            'total_amount': final_purchase_amount_total,
            'transaction_count': len(purchases),
            'total_labor_fee': total_labor_fee,
            'total_weight': total_purchase_weight,
            'avg_labor_fee': avg_labor_fee,
            'net_gold_weight': net_gold_weight,
            'supplier_transaction_count': transaction_count,
            'supplier_total_amount': total_amount,
            'supplier_total_weight': total_weight
        })

    except Exception as e:
        app.logger.error(f"获取供应商统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"获取供应商统计数据失败: {str(e)}"
        })

def get_supplier_material_details_at_date(supplier_id, business_date, exclude_transaction_id=None):
    """
    获取供应商在指定时间点的旧料明细数据（完全照搬前端计算逻辑）

    Args:
        supplier_id: 供应商ID
        business_date: 业务日期
        exclude_transaction_id: 排除的交易ID（用于编辑模式）

    Returns:
        dict: 包含旧料明细的字典，格式为 {material_name: {owed_weight: float, stored_weight: float}}
    """
    app, db = get_app_db()
    try:
        from models import OldMaterial
        from datetime import datetime, date
        import json

        # 获取所有旧料类型
        all_materials = OldMaterial.query.order_by(OldMaterial.code).all()

        # 初始化累计值（照搬前端逻辑）
        material_totals = {}
        for material in all_materials:
            material_totals[material.name] = {
                'owed': 0.0,
                'stored': 0.0
            }

        # 调用供应商款料明细API获取数据（照搬前端逻辑）
        with app.test_request_context():
            api_result = api_supplier_details(supplier_id)
            data = json.loads(api_result.data)

            if not data.get('success'):
                app.logger.error(f"获取供应商款料明细API失败: {data.get('message')}")
                return None

            details = data.get('details', [])

        # 按时间排序所有记录（照搬前端逻辑）
        def sort_key(detail):
            if detail.get('business_time') == '初始值':
                return '0000-00-00'
            return detail.get('business_time', '')

        sorted_details = sorted(details, key=sort_key)

        # 解析业务日期
        if isinstance(business_date, str):
            if 'T' in business_date:
                current_datetime = datetime.strptime(business_date, '%Y-%m-%dT%H:%M')
            else:
                current_datetime = datetime.strptime(business_date, '%Y-%m-%d')
        elif isinstance(business_date, date):
            current_datetime = datetime.combine(business_date, datetime.min.time())
        else:
            current_datetime = business_date

        # 累计所有之前单据的旧料数据变化（完全照搬前端逻辑）
        for detail in sorted_details:
            # 处理初始值记录（照搬前端逻辑）
            if detail.get('business_time') == '初始值':
                for material in all_materials:
                    material_name = material.name
                    owed_key = f'{material_name}_debt'
                    stored_key = f'{material_name}_storage'

                    if owed_key in detail:
                        owed_value = float(detail[owed_key] or 0)
                        material_totals[material_name]['owed'] = owed_value  # 初始值直接设置

                    if stored_key in detail:
                        stored_value = float(detail[stored_key] or 0)
                        material_totals[material_name]['stored'] = stored_value  # 初始值直接设置
                continue

            # 跳过当前单据（编辑模式下）（照搬前端逻辑）
            if exclude_transaction_id and detail.get('document_id'):
                if str(detail['document_id']) == str(exclude_transaction_id):
                    continue

            # 检查业务时间是否在当前单据之前（照搬前端逻辑）
            if detail.get('business_time'):
                try:
                    detail_datetime = datetime.strptime(detail['business_time'], '%Y-%m-%d %H:%M')
                    if detail_datetime >= current_datetime:
                        break  # 到达当前或之后的记录，停止累计
                except ValueError:
                    # 如果时间格式不匹配，尝试其他格式
                    try:
                        detail_datetime = datetime.strptime(detail['business_time'], '%Y-%m-%d')
                        if detail_datetime >= current_datetime:
                            break
                    except ValueError:
                        continue

            # 累加变化值到累计余额（API返回的是变化值，需要累加）（照搬前端逻辑）
            for material in all_materials:
                material_name = material.name
                debt_key = f'{material_name}_debt'
                storage_key = f'{material_name}_storage'

                # 累加变化值
                if debt_key in detail:
                    debt_change = float(detail[debt_key] or 0)
                    material_totals[material_name]['owed'] += debt_change

                if storage_key in detail:
                    storage_change = float(detail[storage_key] or 0)
                    material_totals[material_name]['stored'] += storage_change

        # 转换为模板期望的格式
        result = {}
        for material_name, totals in material_totals.items():
            result[material_name] = {
                'owed_weight': totals['owed'],
                'stored_weight': totals['stored']
            }

        return result

    except Exception as e:
        app.logger.error(f"获取供应商旧料明细失败: {str(e)}")
        import traceback
        app.logger.error(traceback.format_exc())
        return None


def calculate_supplier_previous_balance(supplier_id, business_date, exclude_transaction_id=None):
    """
    计算供应商在指定时间点的上期余额

    Args:
        supplier_id: 供应商ID
        business_date: 业务日期
        exclude_transaction_id: 排除的交易ID（用于编辑模式）

    Returns:
        dict: 包含上期余额的字典，如果计算失败返回None
    """
    app, db = get_app_db()
    try:
        from models import (Supplier, SupplierTransaction, MaterialTransaction, MoneyTransaction,
                           OldMaterial, SupplierMaterialBalance, Purchase, PurchaseItem, Product)
        from datetime import datetime, date

        # 处理business_date参数，支持date和datetime类型
        if isinstance(business_date, date) and not isinstance(business_date, datetime):
            # 如果是date类型，转换为当天的最后时刻，这样可以包含当天的所有记录
            query_datetime = datetime.combine(business_date, datetime.max.time())
        elif isinstance(business_date, datetime):
            # 如果是datetime类型，直接使用
            query_datetime = business_date
        else:
            # 如果是字符串，尝试解析
            try:
                if 'T' in str(business_date):
                    query_datetime = datetime.strptime(str(business_date), '%Y-%m-%dT%H:%M')
                else:
                    query_datetime = datetime.strptime(str(business_date), '%Y-%m-%d')
            except ValueError:
                app.logger.error(f"无法解析业务日期: {business_date}")
                return None

        # 验证供应商是否存在
        supplier = Supplier.query.get(supplier_id)
        if not supplier:
            return None

        # 获取所有旧料类型
        all_materials = OldMaterial.query.order_by(OldMaterial.code).all()

        # 初始化累计值（从0开始，不使用供应商当前余额，因为那是包含所有历史记录的总余额）
        current_balances = {
            'debt_amount': 0.0,
            'deposit_amount': 0.0
        }

        # 获取供应商的旧料初始余额
        material_balances = {}
        for balance in supplier.material_balances:
            material_balances[balance.material_name] = {
                'owed_weight': float(balance.owed_weight or 0),
                'stored_weight': float(balance.stored_weight or 0)
            }

        # 初始化旧料累计值（从初始余额开始）
        for material in all_materials:
            if material.name in material_balances:
                current_balances[f'{material.name}_debt'] = material_balances[material.name]['owed_weight']
                current_balances[f'{material.name}_storage'] = material_balances[material.name]['stored_weight']
            else:
                current_balances[f'{material.name}_debt'] = 0.0
                current_balances[f'{material.name}_storage'] = 0.0

        # 获取所有相关单据并按时间排序（在指定日期之前的）
        all_documents = []

        # 1. 获取供应商往来记录
        # 核心逻辑：计算当前单据之前的所有单据数据之和，基于业务时间排序
        supplier_transactions_query = SupplierTransaction.query.filter_by(
            supplier_id=supplier_id
        )

        # 如果指定了排除的交易ID（编辑模式），排除该记录
        if exclude_transaction_id:
            # 获取被排除的交易记录，用于时间比较
            excluded_transaction = SupplierTransaction.query.get(int(exclude_transaction_id))
            if excluded_transaction:
                # 编辑模式：使用传入的query_datetime进行比较，这样可以确保计算的是"当前单据之前"的数据
                # 这里的query_datetime是前端传递的当前单据的录入时间
                app.logger.info(f"编辑模式：计算单据ID {exclude_transaction_id} 在时间 {query_datetime} 之前的上期数据")

                # 获取在指定时间之前的所有记录（不包括当前单据）
                supplier_transactions_query = supplier_transactions_query.filter(
                    db.and_(
                        SupplierTransaction.created_at < query_datetime,
                        SupplierTransaction.id != int(exclude_transaction_id)
                    )
                )
                app.logger.info(f"编辑模式：使用query_datetime {query_datetime} 进行时间比较，排除交易ID {exclude_transaction_id}")
            else:
                # 如果找不到被排除的记录，排除该ID
                supplier_transactions_query = supplier_transactions_query.filter(
                    SupplierTransaction.id != int(exclude_transaction_id)
                )
        else:
            # 新增模式：获取业务时间早于指定时间的所有记录
            supplier_transactions_query = supplier_transactions_query.filter(
                SupplierTransaction.business_date < query_datetime
            )

        # 统一使用created_at排序，确保时间顺序准确
        supplier_transactions = supplier_transactions_query.order_by(
            SupplierTransaction.created_at.asc(),
            SupplierTransaction.id.asc()
        ).all()

        app.logger.info(f"获取到 {len(supplier_transactions)} 条供应商往来记录用于上期数据计算")

        for transaction in supplier_transactions:
            all_documents.append({
                'type': 'supplier_transaction',
                'data': transaction,
                'business_time': transaction.business_date,
                'created_at': transaction.created_at
            })

        # 2. 获取采购单据记录
        purchases_query = Purchase.query.filter_by(
            is_deleted=False
        ).join(
            PurchaseItem, Purchase.id == PurchaseItem.purchase_id
        ).filter(
            PurchaseItem.supplier_id == supplier_id,
            PurchaseItem.is_deleted == False
        )

        # 根据模式过滤采购单据
        if exclude_transaction_id:
            # 编辑模式：获取在指定时间之前的采购单据
            purchases_query = purchases_query.filter(
                Purchase.business_time < query_datetime
            )

        else:
            # 新增模式：获取业务时间早于指定时间的采购单据
            purchases_query = purchases_query.filter(
                Purchase.business_time < query_datetime
            )

        purchases = purchases_query.order_by(Purchase.business_time.asc(), Purchase.id.asc()).all()

        # 3. 获取其他供应商往来单据中以该供应商为来源的记录（"借料"记录）
        app.logger.info(f"查找以供应商{supplier.name}为还料/存料来源的其他供应商往来记录")

        # 查找还料来源为该供应商的记录
        return_source_query = db.session.query(SupplierTransaction).join(
            MaterialTransaction, SupplierTransaction.id == MaterialTransaction.transaction_id
        ).filter(
            MaterialTransaction.return_source == supplier.name,
            MaterialTransaction.return_weight > 0
        )

        # 查找存料来源为该供应商的记录
        store_source_query = db.session.query(SupplierTransaction).join(
            MaterialTransaction, SupplierTransaction.id == MaterialTransaction.transaction_id
        ).filter(
            MaterialTransaction.store_source == supplier.name,
            MaterialTransaction.store_weight > 0
        )

        # 根据模式过滤来源记录
        if exclude_transaction_id:
            # 编辑模式：获取在指定时间之前的来源记录
            time_filter = SupplierTransaction.created_at < query_datetime
            return_source_query = return_source_query.filter(time_filter)
            store_source_query = store_source_query.filter(time_filter)
            app.logger.info(f"编辑模式：过滤来源记录，created_at早于 {query_datetime}")
        else:
            # 新增模式：获取业务时间早于指定时间的来源记录
            return_source_query = return_source_query.filter(
                SupplierTransaction.business_date < query_datetime
            )
            store_source_query = store_source_query.filter(
                SupplierTransaction.business_date < query_datetime
            )

        return_source_transactions = return_source_query.order_by(
            SupplierTransaction.business_date.asc(), SupplierTransaction.id.asc()
        ).all()

        store_source_transactions = store_source_query.order_by(
            SupplierTransaction.business_date.asc(), SupplierTransaction.id.asc()
        ).all()

        # 合并并去重
        source_transactions = list(set(return_source_transactions + store_source_transactions))

        app.logger.info(f"找到{len(source_transactions)}个其他供应商往来单据涉及该供应商作为来源")

        for transaction in source_transactions:
            all_documents.append({
                'type': 'source_transaction',
                'data': transaction,
                'business_time': transaction.business_date,
                'created_at': transaction.created_at
            })

        for purchase in purchases:
            # 确保business_time是date类型用于排序
            if hasattr(purchase.business_time, 'date'):
                business_date = purchase.business_time.date()
            elif hasattr(purchase.business_time, 'year'):
                business_date = purchase.business_time
            else:
                # 如果是字符串，尝试解析
                try:
                    from datetime import datetime
                    business_date = datetime.strptime(str(purchase.business_time), '%Y-%m-%d').date()
                except:
                    business_date = purchase.business_time

            all_documents.append({
                'type': 'purchase',
                'data': purchase,
                'business_time': business_date,
                'created_at': purchase.created_at
            })

        # 按ID排序所有单据，确保按录入顺序处理
        def sort_key(x):
            # 使用单据ID作为主要排序依据，确保按录入顺序处理
            if x['type'] == 'supplier_transaction':
                return ('supplier', x['data'].id)
            elif x['type'] == 'purchase':
                return ('purchase', x['data'].id)
            elif x['type'] == 'source_transaction':
                return ('source', x['data'].id)
            else:
                return ('other', 0)

        all_documents.sort(key=sort_key)

        # 处理每个单据，累计计算上期数据
        for doc in all_documents:
            if doc['type'] == 'supplier_transaction':
                transaction = doc['data']

                # 计算本次交易的变化
                material_changes = {}
                for material in all_materials:
                    material_changes[f'{material.name}_debt'] = 0.0
                    material_changes[f'{material.name}_storage'] = 0.0

                # 处理物料交易
                for mt in transaction.material_transactions:
                    # 寄料：减少欠料
                    if mt.actual_deposit_weight and mt.actual_deposit_weight > 0:
                        if mt.deposit_material_type:
                            material_changes[f'{mt.deposit_material_type}_debt'] -= float(mt.actual_deposit_weight)

                    # 还料：减少欠料
                    if mt.return_weight and mt.return_weight > 0:
                        if mt.return_material_type:
                            # 还料减少欠料
                            material_changes[f'{mt.return_material_type}_debt'] -= float(mt.return_weight)

                    # 存料：增加存料
                    if mt.store_weight and mt.store_weight > 0:
                        if mt.store_material_type:
                            material_changes[f'{mt.store_material_type}_storage'] += float(mt.store_weight)

                # 处理款项交易
                for money_trans in transaction.money_transactions:
                    if money_trans.return_amount and money_trans.return_amount > 0:
                        if money_trans.return_material_type and money_trans.return_weight and money_trans.return_weight > 0:
                            # 真正的买料
                            material_changes[f'{money_trans.return_material_type}_debt'] -= float(money_trans.return_weight)
                            current_balances['deposit_amount'] -= float(money_trans.return_amount)
                        else:
                            # 还款表格
                            current_balances['debt_amount'] -= float(money_trans.return_amount)

                    # 存款
                    if money_trans.store_amount and money_trans.store_amount > 0:
                        current_balances['deposit_amount'] += float(money_trans.store_amount)

                # 应用物料变化到累计值
                for material in all_materials:
                    current_balances[f'{material.name}_debt'] += material_changes[f'{material.name}_debt']
                    current_balances[f'{material.name}_storage'] += material_changes[f'{material.name}_storage']

            elif doc['type'] == 'purchase':
                purchase = doc['data']

                # 获取该供应商的采购项目
                purchase_items = PurchaseItem.query.filter_by(
                    purchase_id=purchase.id,
                    supplier_id=supplier_id,
                    is_deleted=False
                ).all()

                total_owed_amount_change = 0.0

                for item in purchase_items:
                    # 检查产品类别是否为黄金
                    is_gold_product = False
                    if item.product_id:
                        product = Product.query.get(item.product_id)
                        if product and product.category in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']:
                            is_gold_product = True
                    elif item.product_type in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']:
                        is_gold_product = True

                    # 如果是黄金产品，增加旧料足金欠料
                    if is_gold_product and item.weight:
                        current_balances['旧料足金_debt'] += float(item.weight)

                    # 增加欠款金额
                    labor_fee = float(item.labor_fee or 0)
                    final_amount = float(item.final_amount or 0)
                    total_amount = labor_fee + final_amount
                    if total_amount > 0:
                        total_owed_amount_change += total_amount

                current_balances['debt_amount'] += total_owed_amount_change

            elif doc['type'] == 'source_transaction':
                # 处理其他供应商往来单据中以该供应商为来源的记录（"借料"记录）
                transaction = doc['data']

                # 计算该供应商作为来源的旧料变化
                material_changes = {}
                for material in all_materials:
                    material_changes[f'{material.name}_debt'] = 0.0
                    material_changes[f'{material.name}_storage'] = 0.0

                # 查找涉及该供应商作为来源的物料交易
                for mt in transaction.material_transactions:
                    # 还料来源为该供应商：该供应商欠料增加
                    if (mt.return_source == supplier.name and
                        mt.return_weight and mt.return_weight > 0 and
                        mt.return_material_type):
                        material_changes[f'{mt.return_material_type}_debt'] += float(mt.return_weight)

                    # 存料来源为该供应商：该供应商欠料增加
                    if (mt.store_source == supplier.name and
                        mt.store_weight and mt.store_weight > 0 and
                        mt.store_material_type):
                        material_changes[f'{mt.store_material_type}_debt'] += float(mt.store_weight)

                # 应用物料变化到累计值
                for material in all_materials:
                    current_balances[f'{material.name}_debt'] += material_changes[f'{material.name}_debt']
                    current_balances[f'{material.name}_storage'] += material_changes[f'{material.name}_storage']

        # 返回计算结果，包含所有旧料类型的上期余额
        result = {
            # 保持原有的字段名以兼容现有代码
            'previous_owed_gold': current_balances.get('旧料足金_debt', 0.0),
            'previous_deposit_gold': current_balances.get('旧料足金_storage', 0.0),
            'previous_owed_amount': current_balances['debt_amount'],
            'previous_deposit_amount': current_balances['deposit_amount']
        }

        # 添加所有旧料类型的上期余额数据
        for material in all_materials:
            material_name = material.name
            # 使用简化的字段名，便于前端处理
            debt_key = f'previous_owed_{material_name.replace("旧料", "").lower()}'
            storage_key = f'previous_deposit_{material_name.replace("旧料", "").lower()}'

            result[debt_key] = current_balances.get(f'{material_name}_debt', 0.0)
            result[storage_key] = current_balances.get(f'{material_name}_storage', 0.0)

        return result

    except Exception as e:
        app.logger.error(f"计算供应商上期余额失败: {str(e)}")
        return None

def api_customer_material_balances_save(customer_id):
    """保存客户旧料余额API"""
    app, db = get_app_db()
    try:
        from models import Customer, CustomerMaterialBalance

        # 验证客户是否存在
        customer = Customer.query.get(customer_id)
        if not customer:
            return jsonify({
                'success': False,
                'message': '客户不存在'
            })

        # 获取请求数据
        data = request.get_json()
        if not data or 'balances' not in data:
            return jsonify({
                'success': False,
                'message': '请求数据格式错误'
            })

        balances_data = data['balances']

        # 更新或创建旧料余额记录
        for balance_data in balances_data:
            material_name = balance_data.get('material_name')
            owed_weight = float(balance_data.get('owed_weight', 0))
            stored_weight = float(balance_data.get('stored_weight', 0))

            if not material_name:
                continue

            # 查找现有记录
            existing_balance = CustomerMaterialBalance.query.filter_by(
                customer_id=customer_id,
                material_name=material_name
            ).first()

            if existing_balance:
                # 更新现有记录
                existing_balance.owed_weight = owed_weight
                existing_balance.stored_weight = stored_weight
                existing_balance.updated_at = datetime.now()
            else:
                # 创建新记录
                new_balance = CustomerMaterialBalance(
                    customer_id=customer_id,
                    material_name=material_name,
                    owed_weight=owed_weight,
                    stored_weight=stored_weight
                )
                db.session.add(new_balance)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '旧料余额保存成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"保存客户旧料余额失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"保存客户旧料余额失败: {str(e)}"
        })

# 在文件末尾，注册所有路由
def register_routes(app):
    """注册所有路由到Flask应用"""
    # 使用login_required包装函数
    def login_required(f):
        return flask_login_required(f)
    
    # 日报表数据API
    app.add_url_rule('/api/report/daily', 'api_report_daily', login_required(api_report_daily), methods=['GET'])
    
    # 今日订单API
    app.add_url_rule('/api/orders/today', 'api_orders_today', login_required(api_orders_today), methods=['GET'])
    
    # 今日采购API
    app.add_url_rule('/api/purchases/today', 'api_purchases_today', login_required(api_purchases_today), methods=['GET'])
    
    # 今日供应商往来API
    app.add_url_rule('/api/supplier_transactions/today', 'api_supplier_transactions_today', login_required(api_supplier_transactions_today), methods=['GET'])
    
    # 今日其他项目录入API
    app.add_url_rule('/api/other_transactions/today', 'api_other_transactions_today', login_required(api_other_transactions_today), methods=['GET'])

    # 今日货料调整API
    app.add_url_rule('/api/material_adjustments/today', 'api_material_adjustments_today', login_required(api_material_adjustments_today), methods=['GET'])
    
    # 获取所有产品API
    app.add_url_rule('/api/products/all', 'api_products_all', login_required(api_products_all), methods=['GET'])
    

    
    # 获取所有账户API
    app.add_url_rule('/api/accounts/all', 'api_accounts_all', login_required(api_accounts_all), methods=['GET'])

    # 客户搜索API
    app.add_url_rule('/api/customers/search', 'api_customers_search', login_required(api_customers_search), methods=['GET'])

    # 账户流水明细API
    app.add_url_rule('/api/account_transactions/<int:account_id>', 'api_account_transactions', login_required(api_account_transactions), methods=['GET'])

    # 客户款料明细API
    app.add_url_rule('/api/customer_details/<int:customer_id>', 'api_customer_details', login_required(api_customer_details), methods=['GET'])

    # 客户旧料余额API
    app.add_url_rule('/api/customer_material_balances/<int:customer_id>', 'api_customer_material_balances_get', login_required(api_customer_material_balances_get), methods=['GET'])

    # 供应商款料明细API
    app.add_url_rule('/api/supplier_details/<int:supplier_id>', 'api_supplier_details', login_required(api_supplier_details), methods=['GET'])
    app.add_url_rule('/api/supplier_previous_balance/<int:supplier_id>', 'api_supplier_previous_balance', login_required(api_supplier_previous_balance), methods=['GET'])
    app.add_url_rule('/api/customer_material_balances/<int:customer_id>', 'api_customer_material_balances_save', login_required(api_customer_material_balances_save), methods=['POST'])

    # 供应商统计API
    app.add_url_rule('/api/supplier_stats', 'api_supplier_stats', login_required(api_supplier_stats), methods=['GET'])

    # 库存查询API
    app.add_url_rule('/api/inventory/product_query', 'api_inventory_product_query', login_required(api_inventory_product_query), methods=['GET'])
    app.add_url_rule('/api/inventory/material_query', 'api_inventory_material_query', login_required(api_inventory_material_query), methods=['GET'])
    app.add_url_rule('/api/inventory/product_details', 'api_inventory_product_details', login_required(api_inventory_product_details), methods=['GET'])
    app.add_url_rule('/api/inventory/material_details', 'api_inventory_material_details', login_required(api_inventory_material_details), methods=['GET'])
    app.add_url_rule('/api/inventory/product_query/export', 'api_inventory_product_query_export', login_required(api_inventory_product_query_export), methods=['GET'])
    app.add_url_rule('/api/inventory/material_query/export', 'api_inventory_material_query_export', login_required(api_inventory_material_query_export), methods=['GET'])

    app.logger.info("报表API路由注册完成!")
    return app


def calculate_customer_final_balances(customer_id):
    """从客户款料明细API获取最终余额（避免循环依赖）"""
    app, db = get_app_db()
    try:
        # 直接调用客户款料明细API获取最后一行的总计值
        details_response = api_customer_details(customer_id)

        if hasattr(details_response, 'get_json'):
            details_data = details_response.get_json()
        else:
            details_data = details_response

        if not details_data or not details_data.get('success'):
            app.logger.error(f"获取客户{customer_id}款料明细失败")
            return None

        details = details_data.get('details', [])
        if not details:
            app.logger.error(f"客户{customer_id}没有款料明细数据")
            return None

        # 获取最后一行数据（总计行）
        last_detail = details[-1]

        # 构建返回结果
        result = {
            'debt_amount': float(last_detail.get('debt_amount', 0)),
            'deposit_amount': float(last_detail.get('deposit_amount', 0)),
            '旧料足金_debt': float(last_detail.get('旧料足金_debt', 0)),
            '旧料足金_storage': float(last_detail.get('旧料足金_storage', 0))
        }

        # 添加其他旧料的数据
        materials = details_data.get('materials', [])
        for material in materials:
            material_name = material['name']
            if material_name != '旧料足金':  # 旧料足金已经处理过了
                result[f'{material_name}_debt'] = float(last_detail.get(f'{material_name}_debt', 0))
                result[f'{material_name}_storage'] = float(last_detail.get(f'{material_name}_storage', 0))

        return result

    except Exception as e:
        app.logger.error(f"计算客户{customer_id}最终余额失败: {str(e)}")
        return None


def calculate_supplier_final_balances(supplier_id):
    """计算供应商的最终余额（避免循环调用）"""
    app, db = get_app_db()
    try:
        from models import (Supplier, SupplierTransaction, MaterialTransaction, MoneyTransaction,
                           OldMaterial, Purchase, PurchaseItem, Product)

        # 获取所有旧料类型
        all_materials = OldMaterial.query.order_by(OldMaterial.code).all()

        # 初始化累计值
        current_balances = {
            'debt_amount': 0.0,
            'deposit_amount': 0.0
        }

        # 初始化旧料累计值
        for material in all_materials:
            current_balances[f'{material.name}_debt'] = 0.0
            current_balances[f'{material.name}_storage'] = 0.0

        # 获取所有相关单据并按时间排序
        all_documents = []

        # 1. 获取供应商往来记录
        supplier_transactions = SupplierTransaction.query.filter_by(
            supplier_id=supplier_id
        ).order_by(SupplierTransaction.business_date.asc(), SupplierTransaction.created_at.asc()).all()

        for transaction in supplier_transactions:
            all_documents.append({
                'type': 'supplier_transaction',
                'data': transaction,
                'business_time': transaction.business_date,
                'created_at': transaction.created_at
            })

        # 2. 获取采购单据记录
        purchases = Purchase.query.filter_by(
            is_deleted=False
        ).join(PurchaseItem, Purchase.id == PurchaseItem.purchase_id).filter(
            PurchaseItem.supplier_id == supplier_id,
            PurchaseItem.is_deleted == False
        ).order_by(Purchase.business_time.asc(), Purchase.created_at.asc()).all()

        for purchase in purchases:
            # 确保business_time是date类型用于排序
            if hasattr(purchase.business_time, 'date'):
                business_date = purchase.business_time.date()
            elif hasattr(purchase.business_time, 'year'):
                business_date = purchase.business_time
            else:
                try:
                    from datetime import datetime
                    business_date = datetime.strptime(str(purchase.business_time), '%Y-%m-%d').date()
                except:
                    business_date = purchase.business_time

            all_documents.append({
                'type': 'purchase',
                'data': purchase,
                'business_time': business_date,
                'created_at': purchase.created_at
            })

        # 按时间排序所有单据
        def sort_key(x):
            business_time = x['business_time']
            created_at = x['created_at']

            if hasattr(business_time, 'strftime'):
                business_time_str = business_time.strftime('%Y-%m-%d')
            else:
                business_time_str = str(business_time)

            if hasattr(created_at, 'strftime'):
                created_at_str = created_at.strftime('%Y-%m-%d %H:%M:%S')
            else:
                created_at_str = str(created_at)

            return (business_time_str, created_at_str)

        all_documents.sort(key=sort_key)

        # 处理每个单据
        for doc in all_documents:
            if doc['type'] == 'supplier_transaction':
                transaction = doc['data']

                # 处理物料交易
                for mt in transaction.material_transactions:
                    # 寄料：减少欠料
                    if mt.actual_deposit_weight and mt.actual_deposit_weight > 0:
                        if mt.deposit_material_type:
                            current_balances[f'{mt.deposit_material_type}_debt'] -= float(mt.actual_deposit_weight)

                    # 还料：减少欠料
                    if mt.return_weight and mt.return_weight > 0:
                        if mt.return_material_type:
                            current_balances[f'{mt.return_material_type}_debt'] -= float(mt.return_weight)

                    # 存料：增加存料
                    if mt.store_weight and mt.store_weight > 0:
                        if mt.store_material_type:
                            current_balances[f'{mt.store_material_type}_storage'] += float(mt.store_weight)

                # 处理款项交易
                for money_trans in transaction.money_transactions:
                    if money_trans.return_amount and money_trans.return_amount > 0:
                        if money_trans.return_material_type and money_trans.return_weight and money_trans.return_weight > 0:
                            # 买料表格：减少欠料和存款
                            current_balances[f'{money_trans.return_material_type}_debt'] -= float(money_trans.return_weight)
                            current_balances['deposit_amount'] -= float(money_trans.return_amount)
                        else:
                            # 还款表格：减少欠款
                            current_balances['debt_amount'] -= float(money_trans.return_amount)

                    # 存款：增加存款
                    if money_trans.store_amount and money_trans.store_amount > 0:
                        current_balances['deposit_amount'] += float(money_trans.store_amount)

            elif doc['type'] == 'purchase':
                purchase = doc['data']

                # 获取该供应商的采购项目
                purchase_items = PurchaseItem.query.filter_by(
                    purchase_id=purchase.id,
                    supplier_id=supplier_id,
                    is_deleted=False
                ).all()

                total_owed_amount_change = 0.0

                for item in purchase_items:
                    # 检查产品类别是否为黄金
                    is_gold_product = False
                    if item.product_id:
                        product = Product.query.get(item.product_id)
                        if product and product.category in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']:
                            is_gold_product = True
                    elif item.product_type in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']:
                        is_gold_product = True

                    # 如果是黄金产品，增加旧料足金欠料
                    if is_gold_product and item.weight:
                        current_balances['旧料足金_debt'] += float(item.weight)

                    # 增加欠款金额（工费+结价金额）
                    labor_fee = float(item.labor_fee or 0)
                    final_amount = float(item.final_amount or 0)
                    total_amount = labor_fee + final_amount
                    if total_amount > 0:
                        total_owed_amount_change += total_amount

                current_balances['debt_amount'] += total_owed_amount_change

        return current_balances

    except Exception as e:
        app.logger.error(f"计算供应商{supplier_id}最终余额失败: {str(e)}")
        return None


def calculate_supplier_final_balances_from_details(supplier_id):
    """从供应商款料明细表格计算最终余额（模拟前端表格的最后一行）"""
    app, db = get_app_db()
    try:
        from models import (Supplier, SupplierTransaction, MaterialTransaction, MoneyTransaction,
                           OldMaterial, Purchase, PurchaseItem, Product)

        # 验证供应商是否存在
        supplier = Supplier.query.get(supplier_id)
        if not supplier:
            return None

        # 获取所有旧料类型
        all_materials = OldMaterial.query.order_by(OldMaterial.code).all()

        # 获取供应商的旧料初始值
        material_balances = {}
        for balance in supplier.material_balances:
            material_balances[balance.material_name] = {
                'owed_weight': float(balance.owed_weight or 0),
                'stored_weight': float(balance.stored_weight or 0)
            }

        # 初始化累计值（从SupplierMaterialBalance表中的实际初始值开始）
        current_balances = {
            'debt_amount': float(supplier.initial_owed_amount or 0),
            'deposit_amount': float(supplier.initial_deposit_amount or 0)
        }

        # 初始化旧料累计值 - 从SupplierMaterialBalance表中的实际初始值开始
        for material in all_materials:
            balance = material_balances.get(material.name, {'owed_weight': 0, 'stored_weight': 0})
            current_balances[f'{material.name}_debt'] = float(balance['owed_weight'])
            current_balances[f'{material.name}_storage'] = float(balance['stored_weight'])

        # 获取所有相关单据并按时间排序（与api_supplier_details函数逻辑一致）
        all_documents = []

        # 1. 获取供应商往来记录
        supplier_transactions = SupplierTransaction.query.filter_by(
            supplier_id=supplier_id
        ).order_by(SupplierTransaction.business_date.asc(), SupplierTransaction.created_at.asc()).all()

        for transaction in supplier_transactions:
            all_documents.append({
                'type': 'supplier_transaction',
                'data': transaction,
                'business_time': transaction.business_date,
                'created_at': transaction.created_at
            })

        # 2. 获取采购单据记录
        purchases = Purchase.query.filter_by(
            is_deleted=False
        ).join(PurchaseItem, Purchase.id == PurchaseItem.purchase_id).filter(
            PurchaseItem.supplier_id == supplier_id,
            PurchaseItem.is_deleted == False
        ).order_by(Purchase.business_time.asc(), Purchase.created_at.asc()).all()

        for purchase in purchases:
            # 确保business_time是date类型用于排序
            if hasattr(purchase.business_time, 'date'):
                business_date = purchase.business_time.date()
            elif hasattr(purchase.business_time, 'year'):
                business_date = purchase.business_time
            else:
                try:
                    from datetime import datetime
                    business_date = datetime.strptime(str(purchase.business_time), '%Y-%m-%d').date()
                except:
                    business_date = purchase.business_time

            all_documents.append({
                'type': 'purchase',
                'data': purchase,
                'business_time': business_date,
                'created_at': purchase.created_at
            })

        # 3. 获取其他供应商往来单据中以该供应商为来源的记录
        supplier = Supplier.query.get(supplier_id)
        if supplier:
            # 查找还料来源为该供应商的记录
            return_source_transactions = db.session.query(SupplierTransaction).join(
                MaterialTransaction, SupplierTransaction.id == MaterialTransaction.transaction_id
            ).filter(
                MaterialTransaction.return_source == supplier.name,
                MaterialTransaction.return_weight > 0
            ).order_by(SupplierTransaction.business_date.asc(), SupplierTransaction.created_at.asc()).all()

            # 查找存料来源为该供应商的记录
            store_source_transactions = db.session.query(SupplierTransaction).join(
                MaterialTransaction, SupplierTransaction.id == MaterialTransaction.transaction_id
            ).filter(
                MaterialTransaction.store_source == supplier.name,
                MaterialTransaction.store_weight > 0
            ).order_by(SupplierTransaction.business_date.asc(), SupplierTransaction.created_at.asc()).all()

            # 合并并去重
            source_transactions = list(set(return_source_transactions + store_source_transactions))

            for transaction in source_transactions:
                all_documents.append({
                    'type': 'source_transaction',
                    'data': transaction,
                    'business_time': transaction.business_date,
                    'created_at': transaction.created_at
                })

        # 按时间排序所有单据
        def sort_key(x):
            business_time = x['business_time']
            created_at = x['created_at']

            if hasattr(business_time, 'strftime'):
                business_time_str = business_time.strftime('%Y-%m-%d')
            else:
                business_time_str = str(business_time)

            if hasattr(created_at, 'strftime'):
                created_at_str = created_at.strftime('%Y-%m-%d %H:%M:%S')
            else:
                created_at_str = str(created_at)

            return (business_time_str, created_at_str)

        all_documents.sort(key=sort_key)

        # 逐个处理单据，累计计算余额变化（与前端表格逻辑一致）
        for doc in all_documents:
            if doc['type'] == 'supplier_transaction':
                transaction = doc['data']

                # 计算本次交易的变化
                material_changes = {}
                for material in all_materials:
                    material_changes[f'{material.name}_debt'] = 0.0
                    material_changes[f'{material.name}_storage'] = 0.0

                # 处理物料交易
                for mt in transaction.material_transactions:
                    # 1. 寄料：减少欠料（使用实际到料重量）
                    if mt.actual_deposit_weight and mt.actual_deposit_weight > 0:
                        if mt.deposit_material_type:
                            material_changes[f'{mt.deposit_material_type}_debt'] -= float(mt.actual_deposit_weight)

                    # 2. 还料：减少欠料
                    if mt.return_weight and mt.return_weight > 0:
                        if mt.return_material_type:
                            material_changes[f'{mt.return_material_type}_debt'] -= float(mt.return_weight)

                    # 3. 存料：增加存料
                    if mt.store_weight and mt.store_weight > 0:
                        if mt.store_material_type:
                            material_changes[f'{mt.store_material_type}_storage'] += float(mt.store_weight)

                # 处理款项交易
                debt_amount_change = 0.0
                deposit_amount_change = 0.0

                for money_trans in transaction.money_transactions:
                    if money_trans.return_amount and money_trans.return_amount > 0:
                        if money_trans.return_material_type and money_trans.return_weight and money_trans.return_weight > 0:
                            # 买料表格：减少欠料和存款
                            material_changes[f'{money_trans.return_material_type}_debt'] -= float(money_trans.return_weight)
                            deposit_amount_change -= float(money_trans.return_amount)
                        else:
                            # 还款表格：减少欠款
                            debt_amount_change -= float(money_trans.return_amount)

                    # 存款：增加存款
                    if money_trans.store_amount and money_trans.store_amount > 0:
                        deposit_amount_change += float(money_trans.store_amount)

                # 应用变化到累计值
                for material in all_materials:
                    current_balances[f'{material.name}_debt'] += material_changes[f'{material.name}_debt']
                    current_balances[f'{material.name}_storage'] += material_changes[f'{material.name}_storage']

                current_balances['debt_amount'] += debt_amount_change
                current_balances['deposit_amount'] += deposit_amount_change

            elif doc['type'] == 'purchase':
                purchase = doc['data']

                # 获取该供应商的采购项目
                purchase_items = PurchaseItem.query.filter_by(
                    purchase_id=purchase.id,
                    supplier_id=supplier_id,
                    is_deleted=False
                ).all()

                total_owed_amount_change = 0.0

                for item in purchase_items:
                    # 检查产品类别是否为黄金
                    is_gold_product = False
                    if item.product_id:
                        product = Product.query.get(item.product_id)
                        if product and product.category == '黄金':
                            is_gold_product = True
                    elif item.product_type == '黄金':
                        is_gold_product = True

                    # 如果是黄金产品，增加旧料足金欠料
                    if is_gold_product and item.weight:
                        current_balances['旧料足金_debt'] += float(item.weight)

                    # 增加欠款金额（工费+结价金额）
                    labor_fee = float(item.labor_fee or 0)
                    final_amount = float(item.final_amount or 0)
                    total_amount = labor_fee + final_amount
                    if total_amount > 0:
                        total_owed_amount_change += total_amount

                current_balances['debt_amount'] += total_owed_amount_change

            elif doc['type'] == 'source_transaction':
                # 处理其他供应商往来单据中以该供应商为来源的记录
                transaction = doc['data']

                # 查找涉及该供应商作为来源的物料交易
                for mt in transaction.material_transactions:
                    # 还料来源为该供应商：该供应商欠料增加
                    if (mt.return_source == supplier.name and
                        mt.return_weight and mt.return_weight > 0 and
                        mt.return_material_type):
                        current_balances[f'{mt.return_material_type}_debt'] += float(mt.return_weight)

                    # 存料来源为该供应商：该供应商欠料增加
                    if (mt.store_source == supplier.name and
                        mt.store_weight and mt.store_weight > 0 and
                        mt.store_material_type):
                        current_balances[f'{mt.store_material_type}_debt'] += float(mt.store_weight)

        # 返回最终的累计余额（相当于供应商款料明细表格的最后一行）
        return current_balances

    except Exception as e:
        app.logger.error(f"从款料明细计算供应商{supplier_id}最终余额失败: {str(e)}")
        return None


def calculate_product_inventory_at_date(product, target_date):
    """计算产品在指定日期的库存 - 使用正向计算方法"""
    try:
        from models import Purchase, PurchaseItem, Order, OrderItem, MaterialAdjustment, MaterialAdjustmentItem
        from sqlalchemy import func, and_, or_

        # 获取真正的期初库存（从initial_stock字段获取）
        initial_stock = float(getattr(product, 'initial_stock', 0) or 0)

        # 计算到目标日期为止的所有变化
        # 采购入库
        purchase_items = PurchaseItem.query.join(Purchase).filter(
            func.date(Purchase.business_time) <= target_date,
            or_(
                PurchaseItem.product_id == product.id,
                PurchaseItem.product_name == product.name
            ),
            Purchase.is_deleted == False,
            PurchaseItem.is_deleted == False
        ).all()

        purchase_in = sum(float(item.weight or 0) for item in purchase_items
                        if getattr(item, 'item_type', 'new') in ['new', None])

        return_in = sum(float(item.weight or 0) for item in purchase_items
                      if getattr(item, 'item_type', '') == 'return')

        # 销售出库 - 区分批发和零售
        order_items = OrderItem.query.join(Order).filter(
            func.date(Order.order_date) <= target_date,
            or_(
                OrderItem.product_id == product.id,
                OrderItem.product_name == product.name
            ),
            Order.is_deleted == False
        ).all()

        # 分别计算批发和零售出库，以及订单退货
        wholesale_out = 0
        retail_out = 0
        order_return_in = 0  # 订单退货入库

        for item in order_items:
            weight = float(item.weight or 0)
            if item.item_type == 'return':  # 统计退货入库
                order_return_in += weight
            elif item.item_type == 'new':  # 统计新品出库
                # 根据客户类型区分批发和零售
                if item.order and item.order.customer:
                    customer_type = getattr(item.order.customer, 'customer_type', 'retail')
                    if customer_type == 'wholesale':
                        wholesale_out += weight
                    else:
                        retail_out += weight
                else:
                    # 没有客户信息的默认为零售
                    retail_out += weight

        # 将订单退货计入总退货入库
        return_in += order_return_in
        total_out = wholesale_out + retail_out

        # 货料转换和库存调整
        adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
            func.date(MaterialAdjustment.business_date) <= target_date,
            or_(
                # 新字段名匹配
                and_(
                    MaterialAdjustmentItem.target_type == 'product',
                    MaterialAdjustmentItem.target_name == product.name
                ),
                # 兼容旧字段名
                and_(
                    MaterialAdjustmentItem.item_type == 'product',
                    MaterialAdjustmentItem.item_name == product.name
                )
            )
        ).all()

        conversion_total = 0
        adjustment_total = 0

        for item in adjustment_items:
            weight_change = float(item.weight_change or 0)
            # 根据is_conversion字段判断是否为转换
            if getattr(item, 'is_conversion', False):
                conversion_total += weight_change
            else:
                adjustment_total += weight_change

        # 计算最终库存：期初库存 + 采购入库 + 退货入库 - 批发出库 - 零售出库 + 货料转换 + 库存调整
        final_stock = initial_stock + purchase_in + return_in - total_out + conversion_total + adjustment_total

        return final_stock

    except Exception as e:
        print(f"计算产品库存失败: {str(e)}")
        return 0.0


def calculate_customer_balances(customer_id):
    """计算客户的款料余额（从客户款料明细表获取最后一行总计值）"""
    try:
        # 直接调用已有的函数，它会模拟前端表格的计算逻辑
        final_balances = calculate_customer_final_balances(customer_id)

        if not final_balances:
            return {
                'owed_gold': 0,
                'deposit_gold': 0,
                'owed_amount': 0,
                'deposit_amount': 0
            }

        # 从计算结果中提取对应的值
        return {
            'owed_gold': round(final_balances.get('旧料足金_debt', 0), 2),
            'deposit_gold': round(final_balances.get('旧料足金_storage', 0), 2),
            'owed_amount': round(final_balances.get('debt_amount', 0), 2),
            'deposit_amount': round(final_balances.get('deposit_amount', 0), 2)
        }

    except Exception as e:
        print(f"计算客户余额失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'owed_gold': 0,
            'deposit_gold': 0,
            'owed_amount': 0,
            'deposit_amount': 0
        }

def calculate_supplier_balances(supplier_id):
    """计算供应商的款料余额（从供应商款料明细表获取最后一行总计值）"""
    try:
        # 直接调用已有的函数，它会模拟前端表格的计算逻辑
        final_balances = calculate_supplier_final_balances_from_details(supplier_id)

        if not final_balances:
            return {
                'owed_gold': 0,
                'deposit_gold': 0,
                'owed_amount': 0,
                'deposit_amount': 0
            }

        # 从计算结果中提取对应的值
        return {
            'owed_gold': round(final_balances.get('旧料足金_debt', 0), 2),
            'deposit_gold': round(final_balances.get('旧料足金_storage', 0), 2),
            'owed_amount': round(final_balances.get('debt_amount', 0), 2),
            'deposit_amount': round(final_balances.get('deposit_amount', 0), 2)
        }

    except Exception as e:
        print(f"计算供应商余额失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'owed_gold': 0,
            'deposit_gold': 0,
            'owed_amount': 0,
            'deposit_amount': 0
        }

def calculate_old_material_previous_inventory(material_name, business_date, exclude_record_id=None, record_type='order'):
    """
    计算旧料在指定记录之前的上期库存
    基于旧料明细表中的位置计算，而非时间过滤

    Args:
        material_name: 旧料名称
        business_date: 业务日期
        exclude_record_id: 排除的记录ID（用于编辑模式）
        record_type: 记录类型 ('order', 'supplier_transaction', 'adjustment')

    Returns:
        float: 上期库存数量
    """
    app, db = get_app_db()
    try:
        from models import (Order, OrderItem, SupplierTransaction, MaterialTransaction,
                           MaterialAdjustment, MaterialAdjustmentItem, OldMaterial)
        from datetime import datetime, date

        # 获取旧料对象
        material = OldMaterial.query.filter_by(name=material_name).first()
        if not material:
            return 0.0

        # 初始库存
        initial_stock = float(material.initial_stock or 0)
        current_stock = initial_stock

        # 获取所有影响该旧料库存的记录，按时间和ID排序
        all_records = []

        # 1. 订单中的旧料记录
        order_items = OrderItem.query.join(Order).filter(
            OrderItem.material_name == material_name,
            OrderItem.item_type == 'old'
        ).order_by(Order.order_date.asc(), Order.id.asc()).all()

        for item in order_items:
            all_records.append({
                'type': 'order',
                'id': item.order_id,
                'date': item.order.order_date,
                'data': item
            })

        # 2. 供应商往来中的旧料记录
        material_transactions = MaterialTransaction.query.join(SupplierTransaction).filter(
            db.or_(
                MaterialTransaction.owed_material_type == material_name,
                MaterialTransaction.return_material_type == material_name,
                MaterialTransaction.store_material_type == material_name
            )
        ).order_by(SupplierTransaction.business_date.asc(), SupplierTransaction.id.asc()).all()

        for mt in material_transactions:
            all_records.append({
                'type': 'supplier_transaction',
                'id': mt.transaction_id,
                'date': mt.transaction.business_date,
                'data': mt
            })

        # 3. 库存调整记录
        adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
            MaterialAdjustmentItem.target_name == material_name,
            MaterialAdjustmentItem.target_type == 'old_material'
        ).order_by(MaterialAdjustment.business_date.asc(), MaterialAdjustment.id.asc()).all()

        for item in adjustment_items:
            all_records.append({
                'type': 'adjustment',
                'id': item.adjustment_id,
                'date': item.adjustment.business_date,
                'data': item
            })

        # 按日期和ID排序所有记录
        all_records.sort(key=lambda x: (x['date'], x['id']))

        # 找到当前记录的位置，计算之前所有记录的累计影响
        for record in all_records:
            # 如果是编辑模式，跳过当前编辑的记录
            if (exclude_record_id and record_type == record['type'] and
                record['id'] == exclude_record_id):
                break

            # 如果是新增模式，只处理时间早于指定时间的记录
            if not exclude_record_id and record['date'] >= business_date:
                break

            # 计算该记录对库存的影响
            if record['type'] == 'order':
                item = record['data']
                if item.settlement_type in ['owe', 'return']:
                    # 欠料和还料减少库存
                    current_stock -= float(item.net_weight or 0)
                elif item.settlement_type in ['store', 'settle']:
                    # 存料和结料增加库存
                    current_stock += float(item.net_weight or 0)

            elif record['type'] == 'supplier_transaction':
                mt = record['data']
                # 供应商欠料增加库存，还料减少库存
                if mt.owed_material_type == material_name:
                    current_stock += float(mt.owed_weight or 0)
                if mt.return_material_type == material_name:
                    current_stock -= float(mt.return_weight or 0)
                if mt.store_material_type == material_name:
                    current_stock -= float(mt.store_weight or 0)

            elif record['type'] == 'adjustment':
                item = record['data']
                # 库存调整直接影响库存
                current_stock += float(item.adjustment_amount or 0)

        return round(current_stock, 3)

    except Exception as e:
        app.logger.error(f"计算旧料{material_name}上期库存失败: {str(e)}")
        return 0.0


def calculate_old_material_inventory_at_date(material, target_date):
    """计算指定日期的旧料库存"""
    try:
        from models import SupplierTransaction, MaterialTransaction, MoneyTransaction, Order, OrderItem, MaterialAdjustment, MaterialAdjustmentItem
        from sqlalchemy import func, and_, or_

        # 使用初始库存作为基础
        initial_stock = float(material.initial_stock or 0)

        # 从订单中的旧料录入表格获取各种结算方式的统计
        order_items = OrderItem.query.join(Order).filter(
            func.date(Order.order_date) <= target_date,
            OrderItem.material_name == material.name
        ).all()

        # 按结算方式分类统计
        customer_settle = sum(float(item.net_weight or 0) for item in order_items
                            if item.settlement_type == 'settle')
        customer_return = sum(float(item.net_weight or 0) for item in order_items
                            if item.settlement_type == 'return')
        customer_store = sum(float(item.net_weight or 0) for item in order_items
                           if item.settlement_type == 'store')
        store_deduct = sum(float(item.net_weight or 0) for item in order_items
                         if item.settlement_type == 'deduct')

        # 寄料出库（从供应商往来中获取）
        material_transactions = MaterialTransaction.query.join(SupplierTransaction).filter(
            SupplierTransaction.business_date <= target_date,
            MaterialTransaction.deposit_material_type == material.name
        ).all()

        # 寄料出库
        consign_out = sum(float(mt.deposit_weight or 0) for mt in material_transactions
                        if mt.deposit_material_type == material.name)

        # 货料转换和旧料调整
        adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
            func.date(MaterialAdjustment.business_date) <= target_date,
            or_(
                and_(
                    MaterialAdjustmentItem.target_type == 'old_material',
                    MaterialAdjustmentItem.target_name == material.name
                ),
                and_(
                    MaterialAdjustmentItem.item_type == 'old_material',
                    MaterialAdjustmentItem.item_name == material.name
                )
            )
        ).all()

        conversion_total = 0
        adjustment_total = 0
        for item in adjustment_items:
            weight_change = float(item.weight_change or 0)
            if getattr(item, 'is_conversion', False):
                conversion_total += weight_change
            else:
                adjustment_total += weight_change

        # 计算最终库存（存料抵扣不参与库存计算）
        final_stock = (initial_stock + customer_settle + customer_return + customer_store
                     - consign_out + conversion_total + adjustment_total)

        return final_stock

    except Exception as e:
        print(f"计算旧料库存失败: {str(e)}")
        return 0.0


def api_inventory_product_query():
    """产品库存查询API"""
    app, db = get_app_db()
    try:
        from models import Product, Purchase, PurchaseItem, Order, OrderItem, MaterialAdjustment, MaterialAdjustmentItem
        from datetime import datetime, date
        from sqlalchemy import func, and_, or_

        # 获取参数
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        if not start_date_str or not end_date_str:
            return jsonify({
                'success': False,
                'message': '请提供开始日期和结束日期'
            })

        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

        app.logger.info(f"产品库存查询: {start_date} 到 {end_date}")

        # 获取所有产品
        products = Product.query.order_by(Product.code).all()
        result_data = []

        for product in products:
            # 计算查询起始日期前一天的库存作为期初库存
            from datetime import timedelta
            period_start_date = start_date - timedelta(days=1)
            initial_stock = calculate_product_inventory_at_date(product, period_start_date)

            # 通过正向计算得到期末库存
            final_stock = calculate_product_inventory_at_date(product, end_date)

            # 计算期间内的各种变化
            # 采购入库
            purchase_items = PurchaseItem.query.join(Purchase).filter(
                func.date(Purchase.business_time) >= start_date,
                func.date(Purchase.business_time) <= end_date,
                or_(
                    PurchaseItem.product_id == product.id,
                    PurchaseItem.product_name == product.name
                ),
                Purchase.is_deleted == False,
                PurchaseItem.is_deleted == False
            ).all()

            purchase_in = sum(float(item.weight or 0) for item in purchase_items
                            if getattr(item, 'item_type', 'new') in ['new', None])

            # 退货入库（包括采购退货和订单退货）
            return_in = sum(float(item.weight or 0) for item in purchase_items
                          if getattr(item, 'item_type', '') == 'return')

            # 销售出库 - 区分批发和零售
            order_items = OrderItem.query.join(Order).filter(
                func.date(Order.order_date) >= start_date,
                func.date(Order.order_date) <= end_date,
                or_(
                    OrderItem.product_id == product.id,
                    OrderItem.product_name == product.name
                ),
                Order.is_deleted == False
            ).all()

            # 分别计算批发和零售出库，以及订单退货
            wholesale_out = 0
            retail_out = 0
            order_return_in = 0  # 订单退货入库

            for item in order_items:
                weight = float(item.weight or 0)
                if item.item_type == 'return':  # 统计退货入库
                    order_return_in += weight
                elif item.item_type == 'new':  # 统计新品出库
                    # 根据客户类型区分批发和零售
                    if item.order and item.order.customer:
                        customer_type = getattr(item.order.customer, 'customer_type', 'retail')
                        if customer_type == 'wholesale':
                            wholesale_out += weight
                        else:
                            retail_out += weight
                    else:
                        # 没有客户信息的默认为零售
                        retail_out += weight

            # 将订单退货计入总退货入库
            return_in += order_return_in
            total_out = wholesale_out + retail_out

            # 货料转换和库存调整
            adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
                func.date(MaterialAdjustment.business_date) >= start_date,
                func.date(MaterialAdjustment.business_date) <= end_date,
                or_(
                    # 新字段名匹配
                    and_(
                        MaterialAdjustmentItem.target_type == 'product',
                        MaterialAdjustmentItem.target_name == product.name
                    ),
                    # 兼容旧字段名
                    and_(
                        MaterialAdjustmentItem.item_type == 'product',
                        MaterialAdjustmentItem.item_name == product.name
                    )
                )
            ).all()

            conversion = 0
            adjustment = 0

            for item in adjustment_items:
                weight_change = float(item.weight_change or 0)
                # 根据is_conversion字段判断是否为转换
                if getattr(item, 'is_conversion', False):
                    conversion += weight_change
                else:
                    adjustment += weight_change

            # 计算净变化（期末库存 - 期初库存）
            net_change = final_stock - initial_stock

            # 验证计算的正确性：净变化应该等于所有变化的总和
            calculated_change = purchase_in + return_in - total_out + conversion + adjustment

            # 如果计算不一致，记录日志但仍使用正向计算的结果
            if abs(net_change - calculated_change) > 0.01:
                app.logger.warning(f"产品 {product.name} 净变化计算不一致: 直接计算={net_change:.2f}, 累计计算={calculated_change:.2f}")

            result_data.append({
                'product_id': product.id,
                'product_name': product.name,
                'initial_stock': initial_stock,
                'purchase_in': purchase_in,
                'return_in': return_in,
                'wholesale_out': wholesale_out,
                'retail_out': retail_out,
                'total_out': total_out,
                'conversion': conversion,
                'adjustment': adjustment,
                'final_stock': final_stock,
                'net_change': net_change
            })

        return jsonify({
            'success': True,
            'data': result_data
        })

    except Exception as e:
        app.logger.error(f"产品库存查询失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"产品库存查询失败: {str(e)}"
        })


def api_inventory_material_query():
    """旧料库存查询API"""
    app, db = get_app_db()
    try:
        from models import OldMaterial, SupplierTransaction, MaterialTransaction, Order, OrderItem, MaterialAdjustment, MaterialAdjustmentItem
        from datetime import datetime, date
        from sqlalchemy import func, and_, or_

        # 获取参数
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        if not start_date_str or not end_date_str:
            return jsonify({
                'success': False,
                'message': '请提供开始日期和结束日期'
            })

        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

        app.logger.info(f"旧料库存查询: {start_date} 到 {end_date}")

        # 获取所有旧料
        materials = OldMaterial.query.order_by(OldMaterial.code).all()
        result_data = []

        for material in materials:
            # 计算查询范围起始日期0:00时的库存作为期初库存
            from datetime import timedelta
            period_start_date = start_date - timedelta(days=1)
            initial_stock = calculate_old_material_inventory_at_date(material, period_start_date)

            # 检查是否有初始录入记录（如果旧料创建时间在查询范围内）
            initial_entry = 0
            if material.created_at:
                material_created_date = material.created_at.date()
                if start_date <= material_created_date <= end_date:
                    # 无论初始库存是否为0，都添加初始录入记录
                    initial_entry = float(material.initial_stock or 0)
                    # 如果有初始录入，期初库存应该从0开始
                    initial_stock = 0

            # 计算期间内的各种变化
            # 从订单中的旧料录入表格获取各种结算方式的统计
            order_items = OrderItem.query.join(Order).filter(
                func.date(Order.order_date) >= start_date,
                func.date(Order.order_date) <= end_date,
                OrderItem.material_name == material.name
            ).all()

            # 按结算方式分类统计
            customer_settle = sum(float(item.net_weight or 0) for item in order_items
                                if item.settlement_type == 'settle')
            customer_return = sum(float(item.net_weight or 0) for item in order_items
                                if item.settlement_type == 'return')
            customer_store = sum(float(item.net_weight or 0) for item in order_items
                               if item.settlement_type == 'store')
            store_deduct = sum(float(item.net_weight or 0) for item in order_items
                             if item.settlement_type == 'deduct')

            # 寄料出库和寄料损耗（从供应商往来中获取）
            material_transactions = MaterialTransaction.query.join(SupplierTransaction).filter(
                SupplierTransaction.business_date >= start_date,
                SupplierTransaction.business_date <= end_date,
                MaterialTransaction.deposit_material_type == material.name
            ).all()

            # 寄料出库
            consign_out = sum(float(mt.deposit_weight or 0) for mt in material_transactions
                            if mt.deposit_material_type == material.name)

            # 寄料损耗
            consign_loss = sum(float(mt.deposit_loss or 0) for mt in material_transactions
                             if mt.deposit_material_type == material.name)

            # 货料转换和旧料调整
            adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
                func.date(MaterialAdjustment.business_date) >= start_date,
                func.date(MaterialAdjustment.business_date) <= end_date,
                or_(
                    # 新字段名匹配
                    and_(
                        MaterialAdjustmentItem.target_type == 'old_material',
                        MaterialAdjustmentItem.target_name == material.name
                    ),
                    # 兼容旧字段名
                    and_(
                        MaterialAdjustmentItem.item_type == 'old_material',
                        MaterialAdjustmentItem.item_name == material.name
                    )
                )
            ).all()

            conversion = 0  # 货料转换
            adjustment = 0  # 旧料调整

            for item in adjustment_items:
                weight_change = float(item.weight_change or 0)
                if getattr(item, 'is_conversion', False):
                    conversion += weight_change
                else:
                    adjustment += weight_change

            # 计算期末库存（按照每日报表的公式）
            # 期末库存 = 期初库存 + 初始录入 + 客户结料 + 客户还料 + 客户存料 - 寄料出库 + 货料转换 + 旧料调整
            # 注意：存料抵扣只做统计，不参与库存计算；寄料损耗不影响库存计算
            final_stock = (initial_stock + initial_entry + customer_settle + customer_return + customer_store
                         - consign_out + conversion + adjustment)

            # 计算净变化（包含初始录入）
            net_change = final_stock - initial_stock

            result_data.append({
                'material_id': material.id,
                'material_name': material.name,
                'initial_stock': initial_stock,
                'initial_entry': initial_entry,
                'customer_settle': customer_settle,
                'customer_return': customer_return,
                'customer_store': customer_store,
                'store_deduct': store_deduct,
                'consign_out': consign_out,
                'consign_loss': consign_loss,
                'conversion': conversion,
                'adjustment': adjustment,
                'final_stock': final_stock,
                'net_change': net_change
            })

        return jsonify({
            'success': True,
            'data': result_data
        })

    except Exception as e:
        app.logger.error(f"旧料库存查询失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"旧料库存查询失败: {str(e)}"
        })


def api_inventory_product_details():
    """产品库存详细单据API"""
    app, db = get_app_db()
    try:
        from models import Product, Purchase, PurchaseItem, Order, OrderItem, MaterialAdjustment, MaterialAdjustmentItem
        from datetime import datetime
        from sqlalchemy import func, and_, or_

        # 获取参数
        product_id = request.args.get('product_id')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        if not all([product_id, start_date_str, end_date_str]):
            return jsonify({
                'success': False,
                'message': '请提供产品ID、开始日期和结束日期'
            })

        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

        # 获取产品信息
        product = Product.query.get(product_id)
        if not product:
            return jsonify({
                'success': False,
                'message': '产品不存在'
            })

        app.logger.info(f"产品库存详细查询: {product.name} ({start_date} 到 {end_date})")

        details = []

        # 检查产品创建时间是否在查询日期范围内，如果是则添加初始录入记录
        if product.created_at:
            product_created_date = product.created_at.date()
            if start_date <= product_created_date <= end_date:
                # 获取产品的初始库存
                initial_stock = float(getattr(product, 'initial_stock', 0) or 0)

                # 无论初始库存是否为0，都添加初始录入记录
                details.append({
                    'date': product_created_date.strftime('%Y-%m-%d'),
                    'doc_type': '初始录入',
                    'doc_no': '初始录入',
                    'partner_name': '系统',
                    'change_amount': initial_stock,
                    'change_type': '初始录入',
                    'settlement_type': '',
                    'notes': f'期初值：0克，期末值：{initial_stock}克'
                })

        # 采购入库记录
        purchase_items = PurchaseItem.query.join(Purchase).filter(
            func.date(Purchase.business_time) >= start_date,
            func.date(Purchase.business_time) <= end_date,
            or_(
                PurchaseItem.product_id == product.id,
                PurchaseItem.product_name == product.name
            ),
            Purchase.is_deleted == False,
            PurchaseItem.is_deleted == False
        ).order_by(Purchase.business_time).all()

        for item in purchase_items:
            from models import Supplier
            supplier = Supplier.query.get(item.supplier_id) if item.supplier_id else None
            supplier_name = supplier.name if supplier else '未知供应商'

            change_type = '采购入库'
            if getattr(item, 'item_type', '') == 'return':
                change_type = '退货入库'

            # 安全获取关联的采购单信息
            purchase = getattr(item, 'purchase', None)
            if purchase:
                details.append({
                    'date': purchase.business_time.strftime('%Y-%m-%d'),
                    'doc_type': '采购单',
                    'doc_no': purchase.purchase_no,
                    'partner_name': supplier_name,
                    'change_amount': float(item.weight or 0),
                    'change_type': change_type,
                    'notes': f'{item.product_name or product.name} {item.weight or 0}克'
                })

        # 订单出库记录
        order_items = OrderItem.query.join(Order).filter(
            func.date(Order.order_date) >= start_date,
            func.date(Order.order_date) <= end_date,
            or_(
                OrderItem.product_id == product.id,
                OrderItem.product_name == product.name
            )
        ).order_by(Order.order_date).all()

        for item in order_items:
            from models import Customer
            # 安全获取关联的订单信息
            order = getattr(item, 'order', None)
            if order:
                customer = Customer.query.get(order.customer_id) if order.customer_id else None
                customer_name = customer.name if customer else '未知客户'
                customer_type = getattr(customer, 'customer_type', 'retail') if customer else 'retail'

                # 根据客户类型和商品类型确定变化类型
                if item.item_type == 'return':
                    change_type = '退货入库'
                    change_amount = float(item.weight or 0)  # 退货为正数
                elif customer_type == 'wholesale':
                    change_type = '批发出库'
                    change_amount = -float(item.weight or 0)  # 出库为负数
                else:
                    change_type = '零售出库'
                    change_amount = -float(item.weight or 0)  # 出库为负数

                details.append({
                    'date': order.order_date.strftime('%Y-%m-%d'),
                    'doc_type': '订单',
                    'doc_no': order.order_no,
                    'partner_name': customer_name,
                    'change_amount': change_amount,
                    'change_type': change_type,
                    'settlement_type': getattr(item, 'settlement_type', ''),
                    'notes': f'{item.product_name or product.name} {item.weight or 0}克'
                })

        # 货料调整记录
        adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
            func.date(MaterialAdjustment.business_date) >= start_date,
            func.date(MaterialAdjustment.business_date) <= end_date,
            or_(
                # 新字段名匹配
                and_(
                    MaterialAdjustmentItem.target_type == 'product',
                    MaterialAdjustmentItem.target_name == product.name
                ),
                # 兼容旧字段名
                and_(
                    MaterialAdjustmentItem.item_type == 'product',
                    MaterialAdjustmentItem.item_name == product.name
                )
            )
        ).all()

        for item in adjustment_items:
            adjustment = item.adjustment
            weight_change = float(item.weight_change or 0)

            # 根据调整类型确定变化类型
            if getattr(item, 'is_conversion', False):
                change_type = '货料转换'
                doc_type = '库存调整'  # 统一使用库存调整，便于跳转
            else:
                change_type = '库存调整'
                doc_type = '库存调整'

            details.append({
                'date': adjustment.business_date.strftime('%Y-%m-%d'),
                'doc_type': doc_type,
                'doc_no': adjustment.adjustment_no,
                'partner_name': '',
                'change_amount': weight_change,
                'change_type': change_type,
                'settlement_type': '',
                'notes': f'{product.name} {weight_change:+.3f}克'
            })

        # 按日期排序
        details.sort(key=lambda x: x['date'])

        return jsonify({
            'success': True,
            'data': details
        })

    except Exception as e:
        app.logger.error(f"产品库存详细查询失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"产品库存详细查询失败: {str(e)}"
        })


def api_inventory_material_details():
    """旧料库存详细单据API"""
    app, db = get_app_db()
    try:
        from models import OldMaterial, SupplierTransaction, MaterialTransaction, Order, OrderItem, MaterialAdjustment, MaterialAdjustmentItem
        from datetime import datetime
        from sqlalchemy import func, and_, or_

        # 获取参数
        material_id = request.args.get('material_id')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        if not all([material_id, start_date_str, end_date_str]):
            return jsonify({
                'success': False,
                'message': '请提供旧料ID、开始日期和结束日期'
            })

        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

        # 获取旧料信息
        material = OldMaterial.query.get(material_id)
        if not material:
            return jsonify({
                'success': False,
                'message': '旧料不存在'
            })

        app.logger.info(f"旧料库存详细查询: {material.name} ({start_date} 到 {end_date})")

        details = []

        # 添加初始录入记录（如果旧料创建时间在查询日期范围内）
        if material.created_at:
            # 获取旧料的创建日期，如果在查询范围内则添加初始录入记录
            created_date = material.created_at.date()
            if start_date <= created_date <= end_date:
                # 无论初始库存是否为0，都添加初始录入记录
                initial_stock_value = material.initial_stock or 0
                details.append({
                    'date': created_date.strftime('%Y-%m-%d'),
                    'doc_type': '初始录入',
                    'doc_no': f'初始录入-{material.name}',
                    'partner_name': '系统',
                    'change_type': '初始录入',
                    'change_amount': initial_stock_value,
                    'settlement_type': 'initial',
                    'notes': f'期初值：0克，期末值：{initial_stock_value}克'
                })

        # 客户结料记录（从订单中获取）
        customer_settle_orders = OrderItem.query.join(Order).filter(
            func.date(Order.order_date) >= start_date,
            func.date(Order.order_date) <= end_date,
            OrderItem.material_name == material.name
        ).order_by(Order.order_date).all()

        for item in customer_settle_orders:
            from models import Customer
            # 安全获取关联的订单信息
            order = getattr(item, 'order', None)
            if order:
                customer = Customer.query.get(order.customer_id) if order.customer_id else None
                customer_name = customer.name if customer else '未知客户'

                # 根据结算类型确定变化类型和数量
                settlement_type = getattr(item, 'settlement_type', 'settle')
                change_amount = float(item.net_weight or 0)

                if settlement_type == 'settle':
                    change_type = '客户结料'
                    # 客户结料增加料部库存，保持正数
                elif settlement_type == 'return':
                    change_type = '客户还料'
                    # 还料增加库存，保持正数
                elif settlement_type == 'store':
                    change_type = '客户存料'
                    # 存料增加库存，保持正数
                elif settlement_type == 'deduct':
                    change_type = '存料抵扣'
                    # 存料抵扣只做记录，不参与库存计算
                elif settlement_type == 'owe':
                    # 欠料不影响料部库存，跳过此记录
                    continue
                else:
                    change_type = '客户结料'
                    # 默认为客户结料，增加料部库存

                details.append({
                    'date': order.order_date.strftime('%Y-%m-%d'),
                    'doc_type': '订单',
                    'doc_no': order.order_no,
                    'partner_name': customer_name,
                    'change_amount': change_amount,
                    'change_type': change_type,
                    'settlement_type': settlement_type,
                    'notes': f'{material.name} {abs(change_amount)}克'
                })

        # 供应商往来记录 - 只包含影响料部库存的寄料相关记录
        material_transactions = MaterialTransaction.query.join(SupplierTransaction).filter(
            SupplierTransaction.business_date >= start_date,
            SupplierTransaction.business_date <= end_date,
            MaterialTransaction.deposit_material_type == material.name,
            or_(
                and_(MaterialTransaction.deposit_weight.isnot(None), MaterialTransaction.deposit_weight != 0),
                and_(MaterialTransaction.deposit_loss.isnot(None), MaterialTransaction.deposit_loss != 0)
            )
        ).order_by(SupplierTransaction.business_date).all()

        for mt in material_transactions:
            from models import Supplier
            # 安全获取关联的供应商往来信息
            transaction = getattr(mt, 'transaction', None)
            if transaction:
                supplier = Supplier.query.get(transaction.supplier_id) if transaction.supplier_id else None
                supplier_name = supplier.name if supplier else '未知供应商'

                # 寄料出库
                if mt.deposit_material_type == material.name and mt.deposit_weight:
                    details.append({
                        'date': transaction.business_date.strftime('%Y-%m-%d'),
                        'doc_type': '供应商往来',
                        'doc_no': transaction.transaction_no,
                        'partner_name': supplier_name,
                        'change_amount': -float(mt.deposit_weight),  # 出库为负数，使用寄料克重
                        'change_type': '寄料出库',
                        'notes': f'{material.name} {mt.deposit_weight}克'
                    })

                # 寄料损耗
                if mt.deposit_material_type == material.name and mt.deposit_loss:
                    details.append({
                        'date': transaction.business_date.strftime('%Y-%m-%d'),
                        'doc_type': '供应商往来',
                        'doc_no': transaction.transaction_no,
                        'partner_name': supplier_name,
                        'change_amount': -float(mt.deposit_loss),  # 损耗为负数
                        'change_type': '寄料损耗',
                        'notes': f'{material.name} 损耗 {mt.deposit_loss}克'
                    })

        # 货料调整记录
        adjustment_items = MaterialAdjustmentItem.query.join(MaterialAdjustment).filter(
            func.date(MaterialAdjustment.business_date) >= start_date,
            func.date(MaterialAdjustment.business_date) <= end_date,
            or_(
                # 新字段名匹配
                and_(
                    MaterialAdjustmentItem.target_type == 'old_material',
                    MaterialAdjustmentItem.target_name == material.name
                ),
                # 兼容旧字段名
                and_(
                    MaterialAdjustmentItem.item_type == 'old_material',
                    MaterialAdjustmentItem.item_name == material.name
                )
            )
        ).all()

        for item in adjustment_items:
            adjustment = item.adjustment
            weight_change = float(item.weight_change or 0)

            # 根据调整类型确定变化类型
            if getattr(item, 'is_conversion', False):
                change_type = '货料转换'
                doc_type = '库存调整'  # 统一使用库存调整，便于跳转
            else:
                change_type = '库存调整'
                doc_type = '库存调整'

            details.append({
                'date': adjustment.business_date.strftime('%Y-%m-%d'),
                'doc_type': doc_type,
                'doc_no': adjustment.adjustment_no,
                'partner_name': adjustment.creator.username if adjustment.creator else '系统',
                'change_type': change_type,
                'change_amount': weight_change,
                'settlement_type': 'adjustment',
                'notes': item.notes or f'{change_type}: {material.name} {weight_change:+.2f}克'
            })

        # 按日期排序
        details.sort(key=lambda x: x['date'])

        return jsonify({
            'success': True,
            'data': details
        })

    except Exception as e:
        app.logger.error(f"旧料库存详细查询失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"旧料库存详细查询失败: {str(e)}"
        })


def api_inventory_product_query_export():
    """产品库存查询导出API"""
    app, db = get_app_db()
    try:
        from datetime import datetime
        import pandas as pd
        from io import BytesIO
        from flask import send_file

        # 获取参数
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        if not start_date_str or not end_date_str:
            return jsonify({
                'success': False,
                'message': '请提供开始日期和结束日期'
            })

        # 调用查询API获取数据
        with app.test_request_context(f'?start_date={start_date_str}&end_date={end_date_str}'):
            result = api_inventory_product_query()
            data = result.get_json()

        if not data.get('success'):
            return jsonify(data)

        # 转换为DataFrame
        df_data = []
        for item in data['data']:
            df_data.append({
                '产品名称': item['product_name'],
                '期初库存(克)': f"{item['initial_stock']:.2f}",
                '采购入库(克)': f"{item['purchase_in']:.2f}",
                '退货入库(克)': f"{item['return_in']:.2f}",
                '批发出库(克)': f"{item['wholesale_out']:.2f}",
                '零售出库(克)': f"{item['retail_out']:.2f}",
                '总计出库(克)': f"{item['total_out']:.2f}",
                '货料转换(克)': f"{item['conversion']:.2f}",
                '库存调整(克)': f"{item['adjustment']:.2f}",
                '期末库存(克)': f"{item['final_stock']:.2f}",
                '净变化(克)': f"{item['net_change']:+.2f}"
            })

        df = pd.DataFrame(df_data)

        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='产品库存查询', index=False)

        output.seek(0)

        # 生成文件名
        filename = f"产品库存查询_{start_date_str}至{end_date_str}.xlsx"

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        app.logger.error(f"产品库存查询导出失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"导出失败: {str(e)}"
        })


def api_inventory_material_query_export():
    """旧料库存查询导出API"""
    app, db = get_app_db()
    try:
        from datetime import datetime
        import pandas as pd
        from io import BytesIO
        from flask import send_file

        # 获取参数
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        if not start_date_str or not end_date_str:
            return jsonify({
                'success': False,
                'message': '请提供开始日期和结束日期'
            })

        # 调用查询API获取数据
        with app.test_request_context(f'?start_date={start_date_str}&end_date={end_date_str}'):
            result = api_inventory_material_query()
            data = result.get_json()

        if not data.get('success'):
            return jsonify(data)

        # 转换为DataFrame
        df_data = []
        for item in data['data']:
            df_data.append({
                '旧料名称': item['material_name'],
                '期初库存(克)': f"{item['initial_stock']:.2f}",
                '客户结料(克)': f"{item['customer_settle']:.2f}",
                '客户还料(克)': f"{item['customer_return']:.2f}",
                '客户存料(克)': f"{item['customer_store']:.2f}",
                '存料抵扣(克)': f"{item['store_deduct']:.2f}",
                '寄料出库(克)': f"{item['consign_out']:.2f}",
                '寄料损耗(克)': f"{item['consign_loss']:.2f}",
                '货料转换(克)': f"{item['conversion']:.2f}",
                '旧料调整(克)': f"{item['adjustment']:.2f}",
                '期末库存(克)': f"{item['final_stock']:.2f}",
                '净变化(克)': f"{item['net_change']:+.2f}"
            })

        df = pd.DataFrame(df_data)

        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='旧料库存查询', index=False)

        output.seek(0)

        # 生成文件名
        filename = f"旧料库存查询_{start_date_str}至{end_date_str}.xlsx"

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        app.logger.error(f"旧料库存查询导出失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"导出失败: {str(e)}"
        })

def api_account_transactions(account_id):
    """获取账户流水明细API"""
    try:
        from models import AccountTransaction, Account, Customer, Supplier
        from datetime import datetime
        app, db = get_app_db()

        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 验证账户是否存在
        account = Account.query.get(account_id)
        if not account:
            return jsonify({'success': False, 'message': '账户不存在'})

        # 构建查询
        query = AccountTransaction.query.filter_by(account_id=account_id)

        # 添加日期筛选
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(db.func.date(AccountTransaction.transaction_date) >= start_date_obj)
            except ValueError:
                return jsonify({'success': False, 'message': '开始日期格式错误'})

        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(db.func.date(AccountTransaction.transaction_date) <= end_date_obj)
            except ValueError:
                return jsonify({'success': False, 'message': '结束日期格式错误'})

        # 按时间排序
        transactions = query.order_by(AccountTransaction.transaction_date.asc()).all()

        # 计算余额变化
        result = []

        # 如果有筛选日期，检查是否需要添加初始录入记录
        if start_date:
            # 获取账户的初始余额
            initial_balance = float(account.initial_balance or 0)

            # 检查账户创建时间是否在查询日期范围内，如果是则添加初始录入记录
            if account.created_at:
                account_created_date = account.created_at.date()
                if start_date_obj <= account_created_date <= (datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else start_date_obj):
                    # 无论初始余额是否为0，都添加初始录入记录
                    result.append({
                        'id': 'initial_entry',
                        'transaction_date': account_created_date.strftime('%Y-%m-%d'),
                        'transaction_type': 'initial_entry',
                        'amount': initial_balance,
                        'balance_before': 0,
                        'balance_after': initial_balance,
                        'counterparty': '系统',
                        'purpose': '初始录入',
                        'related_document': '初始录入',
                        'related_document_no': '初始录入',
                        'related_document_url': '',
                        'notes': f'期初值：0元，期末值：{initial_balance}元'
                    })

            # 计算期初余额：账户初始余额 + 开始日期之前的所有交易变化
            before_transactions = AccountTransaction.query.filter(
                AccountTransaction.account_id == account_id,
                db.func.date(AccountTransaction.transaction_date) < start_date_obj
            ).order_by(AccountTransaction.transaction_date.asc()).all()

            # 计算开始日期之前的余额变化
            before_balance_change = 0
            for trans in before_transactions:
                if trans.transaction_type in ['income', 'deposit']:
                    before_balance_change += trans.amount or 0
                elif trans.transaction_type in ['expense', 'withdrawal']:
                    before_balance_change -= trans.amount or 0

            current_balance = initial_balance + before_balance_change
        else:
            current_balance = account.initial_balance or 0

        for transaction in transactions:
            # 计算交易前余额
            balance_before = current_balance

            # 计算交易后余额
            if transaction.transaction_type in ['income', 'deposit']:
                current_balance += transaction.amount or 0
            elif transaction.transaction_type in ['expense', 'withdrawal']:
                current_balance -= transaction.amount or 0

            balance_after = current_balance

            # 获取交易方信息
            counterparty = '-'
            if transaction.customer_id:
                customer = Customer.query.get(transaction.customer_id)
                counterparty = customer.name if customer else '-'
            elif transaction.supplier_id:
                supplier = Supplier.query.get(transaction.supplier_id)
                counterparty = supplier.name if supplier else '-'

            # 构建关联单据信息和获取单据备注
            related_document = '-'
            related_document_no = '-'
            related_document_url = ''
            document_notes = '-'

            if transaction.order_id:
                from models import Order
                order = Order.query.get(transaction.order_id)
                if order:
                    if order.order_no:
                        related_document = f'订单-{order.order_no}'
                        related_document_no = order.order_no
                    else:
                        related_document = f'订单-{transaction.order_id}'
                        related_document_no = str(transaction.order_id)
                    related_document_url = f'/orders/{transaction.order_id}'
                    document_notes = order.notes or '-'
                else:
                    related_document = f'订单-{transaction.order_id}'
                    related_document_no = str(transaction.order_id)
                    related_document_url = f'/orders/{transaction.order_id}'
            elif transaction.purchase_id:
                from models import Purchase
                purchase = Purchase.query.get(transaction.purchase_id)
                if purchase:
                    if purchase.purchase_no:
                        related_document = f'采购-{purchase.purchase_no}'
                        related_document_no = purchase.purchase_no
                    else:
                        related_document = f'采购-{transaction.purchase_id}'
                        related_document_no = str(transaction.purchase_id)
                    related_document_url = f'/purchases/edit/{transaction.purchase_id}'
                    document_notes = purchase.notes or '-'
                else:
                    related_document = f'采购-{transaction.purchase_id}'
                    related_document_no = str(transaction.purchase_id)
                    related_document_url = f'/purchases/edit/{transaction.purchase_id}'
            elif transaction.supplier_transaction_id:
                from models import SupplierTransaction
                supplier_trans = SupplierTransaction.query.get(transaction.supplier_transaction_id)
                if supplier_trans:
                    if supplier_trans.transaction_no:
                        related_document = f'供应商往来-{supplier_trans.transaction_no}'
                        related_document_no = supplier_trans.transaction_no
                    else:
                        related_document = f'供应商往来-{transaction.supplier_transaction_id}'
                        related_document_no = str(transaction.supplier_transaction_id)
                    related_document_url = f'/supplier_transactions/{transaction.supplier_transaction_id}/edit'
                    document_notes = supplier_trans.notes or '-'
                else:
                    related_document = f'供应商往来-{transaction.supplier_transaction_id}'
                    related_document_no = str(transaction.supplier_transaction_id)
                    related_document_url = f'/supplier_transactions/{transaction.supplier_transaction_id}/edit'
            elif hasattr(transaction, 'other_transaction_id') and transaction.other_transaction_id:
                from models import Transaction
                other_trans = Transaction.query.get(transaction.other_transaction_id)
                if other_trans:
                    if other_trans.transaction_no:
                        related_document = f'其他项目-{other_trans.transaction_no}'
                        related_document_no = other_trans.transaction_no
                    else:
                        related_document = f'其他项目-{transaction.other_transaction_id}'
                        related_document_no = str(transaction.other_transaction_id)
                    related_document_url = f'/other_transactions/{transaction.other_transaction_id}/edit'
                    document_notes = other_trans.notes or '-'
                else:
                    related_document = f'其他项目-{transaction.other_transaction_id}'
                    related_document_no = str(transaction.other_transaction_id)
                    related_document_url = f'/other_transactions/{transaction.other_transaction_id}/edit'

            result.append({
                'id': transaction.id,
                'transaction_date': transaction.transaction_date.isoformat() if transaction.transaction_date else None,
                'transaction_type': transaction.transaction_type,
                'amount': float(transaction.amount or 0),
                'balance_before': float(balance_before),
                'balance_after': float(balance_after),
                'counterparty': counterparty,
                'purpose': transaction.purpose or '-',
                'related_document': related_document,
                'related_document_no': related_document_no,
                'related_document_url': related_document_url,
                'notes': document_notes
            })

        return jsonify(result)

    except Exception as e:
        print(f"获取账户流水明细失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'获取数据失败: {str(e)}'})
