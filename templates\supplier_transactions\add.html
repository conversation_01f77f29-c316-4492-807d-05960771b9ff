{% extends "base.html" %}

{% block title %}{{ '编辑' if edit_mode else '新增' }}供应商往来 - {{ app_name }}{% endblock %}

{% block page_title %}{% if edit_mode %}编辑供应商往来{% else %}供应商往来录入{% endif %}{% endblock %}

{% block extra_css %}
<style>
/* ========== 统一表格样式系统 - 增强版 ========== */

/* 基础表格重置 - 覆盖所有表格类型 */
.table, 
.supplier-transaction-table,
#transactions-container table,
.transaction-row table {
    border-collapse: collapse !important;
    border: 1px solid #dee2e6 !important;
    margin: 0 !important;
    width: 100% !important;
    background: white !important;
}

/* 统一所有表头样式 - 包括还款存款表格 */
#transactions-container .table thead th,
#supplier-owed-materials .supplier-transaction-table thead th,
#transactions-container table thead th,
.transaction-row table thead th,
.table thead th {
    font-size: 16px !important;
    font-weight: 700 !important;
    text-align: center !important;
    padding: 4px 2px !important;
    vertical-align: middle !important;
    height: 28px !important;
    line-height: 1.2 !important;
    border: 1px solid #dee2e6 !important;
    border-top: none !important;
    border-bottom: 1px solid #dee2e6 !important;
    white-space: nowrap !important;
}

/* 统一所有数据行样式 - 包括还款存款表格 */
#transactions-container .table tbody td,
#supplier-owed-materials .supplier-transaction-table tbody td,
#transactions-container table tbody td,
.transaction-row table tbody td,
.table tbody td {
    font-size: 14px !important;
    font-weight: 700 !important;
    text-align: center !important;
    padding: 2px 2px !important;
    vertical-align: middle !important;
    height: 24px !important;
    line-height: 1.2 !important;
    border: 1px solid #dee2e6 !important;
    border-top: none !important;
    background: white !important;
}

/* 统一所有输入框和选择框样式 - 包括还款存款表格 */
#transactions-container .table input,
#transactions-container .table select,
#supplier-owed-materials .supplier-transaction-table input,
#supplier-owed-materials .supplier-transaction-table select,
#transactions-container table input,
#transactions-container table select,
.transaction-row table input,
.transaction-row table select,
.table input,
.table select {
    font-size: 14px !important;
    font-weight: 700 !important;
    text-align: center !important;
    border: none !important;
    background: transparent !important;
    width: 100% !important;
    height: 20px !important;
    padding: 0 2px !important;
    outline: none !important;
    line-height: 1.2 !important;
    box-shadow: none !important;
}

/* 统一所有删除按钮样式 - 包括还款存款表格 */
#transactions-container .btn-danger,
#supplier-owed-materials .btn-danger,
.btn-sm.btn-danger,
.transaction-row .btn-danger,
.table .btn-danger {
    width: 18px !important;
    height: 18px !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 10px !important;
    line-height: 1 !important;
    border-radius: 2px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: #dc3545 !important;
    border: 1px solid #dc3545 !important;
    min-width: 18px !important;
    max-width: 18px !important;
    min-height: 18px !important;
    max-height: 18px !important;
}

/* 删除按钮图标统一 */
#transactions-container .btn-danger i,
#supplier-owed-materials .btn-danger i,
.btn-sm.btn-danger i,
.transaction-row .btn-danger i,
.table .btn-danger i {
    font-size: 8px !important;
}

/* 还款表格特定样式 */
.transaction-row[data-type="return_money"] table thead th {
    background-color: #ffecb3 !important;
    color: #333 !important;
}

/* 存款表格特定样式 */
.transaction-row[data-type="deposit_money"] table thead th {
    background-color: #f8c4c8 !important;
    color: #333 !important;
}

/* 还料表格特定样式 */
.transaction-row[data-type="return_material"] table thead th {
    background-color: #c2dbff !important;
    color: #333 !important;
}

/* 存料表格特定样式 */
.transaction-row[data-type="store_material"] table thead th {
    background-color: #bce4d0 !important;
    color: #333 !important;
}

/* 买料表格特定样式 */
.transaction-row[data-type="buy_material"] table thead th {
    background-color: #e9ecef !important;
    color: #333 !important;
}

/* 寄料表格特定样式 */
.transaction-row[data-type="deposit_material"] table thead th {
    background-color: #fff3cd !important;
    color: #333 !important;
}

/* 旧料明细表头颜色 */
#supplier-owed-materials .supplier-transaction-table thead th {
    background-color: #6c757d !important;
    color: white !important;
}

/* 表格标题样式 - 右移20px */
.transaction-row > div:first-child {
    width: 80px !important;
    text-align: left !important;
    padding-left: 20px !important;
    padding-right: 10px !important;
    display: flex !important;
    align-items: center !important;
    font-size: 14px !important;
    font-weight: 700 !important;
}

/* 表格容器样式 */
.transaction-row {
    margin-bottom: 2px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: flex-start !important;
}

#transactions-container {
    padding: 0 !important;
}

/* 旧料明细卡片样式 */
.col-md-3 .card {
    background: white !important;
    border: 1px solid #dee2e6 !important;
}

.col-md-3 .card .card-header {
    background-color: #000000 !important;
    color: white !important;
    border-bottom: 1px solid #dee2e6 !important;
    font-size: 14px !important;
    font-weight: 700 !important;
    padding: 6px 12px !important;
}

.col-md-3 .card .card-body {
    background-color: white !important;
    padding: 0 !important;
}

/* 旧料明细表格容器 */
#supplier-owed-materials .supplier-transaction-table {
    border-radius: 0 !important;
    box-shadow: none !important;
    background: white !important;
}

/* 表单标签样式 */
.form-label {
    font-size: 14px !important;
    font-weight: 700 !important;
}

/* 页面主容器样式 */
main {
    overflow-y: auto !important;
    max-height: 100vh !important;
}

.row {
    align-items: flex-start !important;
}

/* 移除数字输入框箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

/* 按钮容器对齐 */
.d-flex .btn-danger {
    flex-shrink: 0 !important;
}

/* 移除可能的Bootstrap覆盖样式 */
.table-sm th,
.table-sm td {
    padding: 2px 2px !important;
    height: 24px !important;
}

.table-sm thead th {
    height: 28px !important;
    padding: 4px 2px !important;
}

/* 确保表格在编辑模式下也保持一致 */
.edit-mode .table,
.edit-mode .transaction-row table {
    border-collapse: collapse !important;
    border: 1px solid #dee2e6 !important;
}

.edit-mode .table thead th,
.edit-mode .transaction-row table thead th {
    height: 28px !important;
    padding: 4px 2px !important;
    border: 1px solid #dee2e6 !important;
    border-top: none !important;
}

.edit-mode .table tbody td,
.edit-mode .transaction-row table tbody td {
    height: 24px !important;
    padding: 2px 2px !important;
    border: 1px solid #dee2e6 !important;
    border-top: none !important;
}

/* 强制覆盖任何内联样式 */
#transactions-container * {
    box-sizing: border-box !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 添加 CSRF token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- 顶部信息区域 - 单行设计 -->
    <div class="row mb-3" style="border-bottom: 2px solid #ffc107; padding-bottom: 10px;">
        <div class="col">
            <div class="d-flex gap-3">
                <div class="flex-grow-1" style="width: 20%;">
                    <label for="business-date" class="form-label mb-1">录入时间:</label>
                    <input type="datetime-local" id="business-date" name="business_date" class="form-control form-control-sm"
                           value="{% if edit_mode %}{{ transaction.created_at.strftime('%Y-%m-%dT%H:%M') if transaction.created_at else today_datetime }}{% else %}{{ today_datetime }}{% endif %}" required>
                </div>
                
                <div class="flex-grow-1" style="width: 20%;">
                    <label for="transaction-no" class="form-label mb-1">供应商往来编号:</label>
                    <input type="text" id="transaction-no" name="transaction_no" class="form-control form-control-sm" 
                           value="{{ transaction.transaction_no if edit_mode else transaction_no }}" readonly>
                    <input type="hidden" id="transaction-id" value="{{ transaction.id if edit_mode else '' }}">
                </div>

                <div class="flex-grow-1" style="width: 25%;">
                    <label for="supplier-id" class="form-label mb-1">供应商名称:</label>
                    <div class="supplier-search-container" style="position: relative;">
                        <input type="text" id="supplier-search" class="form-control form-control-sm"
                               placeholder="搜索供应商..." autocomplete="off"
                               value="{% if edit_mode and transaction.supplier_id %}{{ suppliers|selectattr('id', 'equalto', transaction.supplier_id)|first|attr('name') }}{% endif %}">
                        <input type="hidden" id="supplier-id" name="supplier_id"
                               value="{% if edit_mode and transaction.supplier_id %}{{ transaction.supplier_id }}{% endif %}">
                        <div class="supplier-search-results" style="display: none; position: absolute; z-index: 999999; background: white; border: 1px solid #ced4da; border-radius: 4px; box-shadow: 0 4px 12px rgba(0,0,0,0.25); max-height: 200px; overflow-y: auto;"></div>
                    </div>
                </div>
                
                <div class="flex-grow-1" style="width: 35%;">
                    <label for="notes" class="form-label mb-1">备注:</label>
                    <input type="text" id="notes" name="notes" class="form-control form-control-sm" 
                           value="{{ transaction.notes if edit_mode else '' }}">
                </div>
            </div>
        </div>
    </div>

    <!-- 添加欠料明细容器，放在动态表格容器之前 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card" style="height: 400px; border: none;">
                <div class="card-header py-2" style="background-color: #000000; color: white;">
                    <h6 class="mb-0 fw-bold">旧料明细</h6>
                        </div>
                <div class="card-body p-0" style="height: calc(100% - 50px); overflow: hidden; background-color: white;">
                    <div id="supplier-owed-materials">
                        {% if edit_mode and transaction.supplier_id and supplier_old_materials and supplier_old_materials|length > 0 %}
                        <!-- 编辑模式下直接显示预计算的旧料明细 -->
                        <div class="p-2">
                            <div class="simple-table-responsive">
                                <table class="supplier-transaction-table" style="table-layout: fixed; width: 100%;">
                                    <thead>
                                        <tr>
                                            <th style="width: 33.33%;">旧料名称</th>
                                            <th style="width: 33.33%;">上期欠料</th>
                                            <th style="width: 33.33%;">上期存料</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for material_name, details in supplier_old_materials.items() %}
                                        <tr>
                                            <td>{{ material_name }}</td>
                                            <td class="text-end">{{ "%.2f"|format(details.owed_weight) }}克</td>
                                            <td class="text-end">{{ "%.2f"|format(details.stored_weight) }}克</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% elif edit_mode and transaction.supplier_id %}
                        <!-- 编辑模式下但没有旧料明细数据 -->
                        <div class="text-center py-3">
                            <small class="text-muted">该供应商暂无旧料明细</small>
                        </div>
                        {% else %}
                        <div class="text-center py-3">
                            <small class="text-muted">请选择供应商查看旧料明细</small>
                        </div>
                        {% endif %}
                </div>
            </div>
        </div>
        </div>
        <div class="col-md-9">
            <!-- 动态表格容器 -->
            <div class="card">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between mb-2">
                        <h6 class="mb-0 fw-bold">款料明细录入</h6>
                        <div class="d-flex">
                            <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="addRowByType('deposit_material')" style="font-weight: bold;">寄料</button>
                            <button type="button" class="btn btn-sm btn-outline-success me-1" onclick="addRowByType('buy_material')" style="font-weight: bold;">买料</button>
                            <button type="button" class="btn btn-sm btn-outline-primary me-1" onclick="addRowByType('return_material')" style="font-weight: bold;">还料</button>
                            <button type="button" class="btn btn-sm btn-outline-info me-1" onclick="addRowByType('store_material')" style="font-weight: bold;">存料</button>
                            <button type="button" class="btn btn-sm btn-outline-warning me-1" onclick="addRowByType('return_money')" style="font-weight: bold;">还款</button>
                            <button type="button" class="btn btn-sm btn-outline-danger me-1" onclick="addRowByType('deposit_money')" style="font-weight: bold;">存款</button>
                    </div>
                    </div>
                    <div id="transactions-scroll-container" style="height: 400px; overflow-y: auto; border: none; background: white;">
                        <div id="transactions-container" style="width: 100%; padding: 0;">
                            {% if edit_mode and transaction %}
                            <!-- 编辑模式下直接显示交易记录表格，按正确顺序：寄料 → 买料 → 还料 → 存料 → 还款 → 存款 -->

                            <!-- 寄料记录 -->
                            {% set deposit_materials = transaction.material_transactions|selectattr('deposit_weight')|list %}
                            {% if deposit_materials %}
                            <div class="transaction-row d-flex align-items-center" data-type="deposit_material" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 10px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">寄料</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">旧料名称</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">寄料克重</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">实际到料</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">寄料损耗</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in deposit_materials %}
                                            <tr style="height: 18px;">
                                                <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
                                                    <select class="form-control form-control-sm old-material-select" name="deposit_material_type[]" onchange="updateCalculations()" style="font-size: 14px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                                                        <option value="">请选择</option>
                                                        <option value="{{ mt.deposit_material_type or '' }}" selected>{{ mt.deposit_material_type or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
                                                    <input type="number" class="form-control form-control-sm deposit-weight" name="deposit_weight[]" step="0.01" value="{{ "%.2f"|format(mt.deposit_weight) if mt.deposit_weight else '' }}" oninput="updateDepositLoss(this)" style="font-size: 14px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
                                                    <input type="number" class="form-control form-control-sm actual-deposit-weight" name="actual_deposit_weight[]" step="0.01" value="{{ "%.2f"|format(mt.actual_deposit_weight) if mt.actual_deposit_weight else '' }}" oninput="updateDepositLoss(this)" style="font-size: 14px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
                                                    <input type="number" class="form-control form-control-sm deposit-loss" name="deposit_loss[]" step="0.01" value="{{ "%.2f"|format(mt.deposit_loss) if mt.deposit_loss else '' }}" readonly style="font-size: 14px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="material_note[]" value="{{ mt.note or '' }}" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)" style="height: 18px; padding: 0 2px; font-size: 10px;">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- 买料记录 -->
                            {% set buy_money_materials = transaction.money_transactions|selectattr('return_purpose', 'equalto', '买料')|list %}
                            {% if buy_money_materials %}
                            <div class="transaction-row d-flex align-items-center" data-type="buy_material">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 10px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">买料</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm" style="table-layout: fixed; width: 100%;">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">旧料名称</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">买料克重</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">料价</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">总计金额</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in buy_money_materials %}
                                            <tr style="height: 20px;">
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <select class="form-control form-control-sm old-material-select" name="buy_material_type[]" onchange="updateCalculations()" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                        <option value="">请选择</option>
                                                        <option value="{{ mt.return_material_type or '' }}" selected>{{ mt.return_material_type or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <input type="number" class="form-control form-control-sm buy-weight" name="buy_weight[]" step="0.01" value="{{ "%.2f"|format(mt.return_weight) if mt.return_weight else '' }}" oninput="calculateBuyTotal(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <input type="number" class="form-control form-control-sm material-price" name="material_price[]" step="0.01" value="{{ "%.2f"|format(mt.return_amount / mt.return_weight) if mt.return_weight and mt.return_weight > 0 else '' }}" oninput="calculateBuyTotal(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <input type="number" class="form-control form-control-sm buy-total" name="buy_total[]" step="1" value="{{ "%.2f"|format(mt.return_amount) if mt.return_amount else '' }}" oninput="calculateMaterialPrice(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="buy_note[]" value="{{ mt.note or '' }}" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)" style="height: 18px; padding: 0 2px; font-size: 10px;">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- 还料记录 -->
                            {% set return_materials = transaction.material_transactions|selectattr('return_weight')|list %}
                            {% if return_materials %}
                            <div class="transaction-row d-flex align-items-center" data-type="return_material" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 10px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">还料</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">旧料名称</th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">还料克重</th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">还料来源</th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in return_materials %}
                                            <tr style="height: 22px;">
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm old-material-select" name="return_material_type[]" onchange="updateCalculations()">
                                                        <option value="">请选择</option>
                                                        <option value="{{ mt.return_material_type or '' }}" selected>{{ mt.return_material_type or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <input type="number" class="form-control form-control-sm return-weight" name="return_weight[]" step="0.01" value="{{ "%.2f"|format(mt.return_weight) if mt.return_weight else '' }}" oninput="updateCalculations()">
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm return-material-source" name="return_material_source[]" onchange="handleReturnSourceChange(this)">
                                                        <option value="">请选择还料来源</option>
                                                        <option value="{{ mt.return_source or '' }}" selected>{{ mt.return_source or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="material_note[]" value="{{ mt.note or '' }}">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- 存料记录 -->
                            {% set store_materials = transaction.material_transactions|selectattr('store_weight')|list %}
                            {% if store_materials %}
                            <div class="transaction-row d-flex align-items-center" data-type="store_material" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 10px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">存料</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">旧料名称</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">存料克重</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">存料来源</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in store_materials %}
                                            <tr style="height: 22px;">
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm old-material-select" name="store_material_type[]" onchange="updateCalculations()">
                                                        <option value="">请选择</option>
                                                        <option value="{{ mt.store_material_type or '' }}" selected>{{ mt.store_material_type or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <input type="number" class="form-control form-control-sm store-weight" name="store_weight[]" step="0.01" value="{{ "%.2f"|format(mt.store_weight) if mt.store_weight else '' }}" oninput="updateCalculations()">
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm store-material-source" name="store_material_source[]" onchange="updateCalculations()">
                                                        <option value="">请选择存料来源</option>
                                                        <option value="{{ mt.store_source or '' }}" selected>{{ mt.store_source or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="material_note[]" value="{{ mt.note or '' }}">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            </div>
                            {% endif %}

                            <!-- 还款记录 -->
                            {% set return_money = [] %}
                            {% for mt in transaction.money_transactions %}
                                {% if mt.return_amount and (not mt.return_weight or mt.return_weight == 0) %}
                                    {% set _ = return_money.append(mt) %}
                                {% endif %}
                            {% endfor %}
                            {% if return_money %}
                            <div class="transaction-row d-flex align-items-center" data-type="return_money" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 10px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">还款</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">还款金额</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">还款来源</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">还款用途</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in return_money %}
                                            <tr style="height: 22px;">
                                                <td style="width: 16.66% !important;">
                                                    <input type="number" class="form-control form-control-sm return-amount" name="return_amount[]" step="0.01" value="{{ "%.2f"|format(mt.return_amount) if mt.return_amount else '' }}" oninput="updateCalculations()">
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm return-money-source" name="return_money_source[]" onchange="updateCalculations()">
                                                        <option value="">请选择还款来源</option>
                                                        <option value="{{ mt.return_source or '' }}" selected>{{ mt.return_source or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm return-purpose" name="return_purpose[]" onchange="updateCalculations()">
                                                        <option value="还工费" {% if mt.return_purpose == '还工费' or not mt.return_purpose %}selected{% endif %}>还工费</option>
                                                        <option value="买料" {% if mt.return_purpose == '买料' %}selected{% endif %}>买料</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="money_note[]" value="{{ mt.note or '' }}">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}
                            <!-- 存款记录 -->
                            {% set deposit_money = transaction.money_transactions|selectattr('store_amount')|list %}
                            {% if deposit_money %}
                            <div class="transaction-row d-flex align-items-center" data-type="deposit_money" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 10px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">存款</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">存款金额</th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">存款来源</th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in deposit_money %}
                                            <tr style="height: 22px;">
                                                <td style="width: 16.66% !important;">
                                                    <input type="number" class="form-control form-control-sm deposit-amount" name="deposit_amount[]" step="0.01" value="{{ "%.2f"|format(mt.store_amount) if mt.store_amount else '' }}" oninput="updateCalculations()">
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm deposit-source" name="deposit_source[]" onchange="updateCalculations()">
                                                        <option value="">请选择存款来源</option>
                                                        <option value="{{ mt.store_source or '' }}" selected>{{ mt.store_source or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="money_note[]" value="{{ mt.note or '' }}">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}



                            {% elif edit_mode %}
                            <!-- 编辑模式下但没有交易记录 -->
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <p>该交易记录暂无明细数据</p>
                            </div>
                            {% else %}
                            <!-- 新增模式下显示添加提示 -->
                            <div class="text-center text-muted py-4" id="initial-placeholder">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <p>点击上方按钮添加交易记录</p>
                                <small>支持还料、买料、存料、寄料、还款、存款等操作</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- 供应商信息卡片（放在表格下方） -->
    <div class="row mb-3">
        <div class="col-md-9">
            <div class="card mb-2">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">上期欠料:</span>
                                <span id="previous-owed-gold" class="ms-3 text-end">{{ "%.2f"|format(default_previous_balance.previous_owed_gold) }}克</span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">本期欠料:</span>
                                <span id="current-owed-gold" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_owed_gold) }}{% else %}0{% endif %}">
                                    {% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_owed_gold) }}克{% else %}0.00克{% endif %}
                                </span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">本期还料:</span>
                                <span id="return-owed-gold" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.return_owed_gold) }}{% else %}0{% endif %}">
                                    {% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.return_owed_gold) }}克{% else %}0.00克{% endif %}
                                </span>
                            </div>
                            <div style="min-width: 180px;">
                                <span class="small fw-bold">总计欠料:</span>
                                <span id="total-owed-gold" class="ms-3 fw-bold text-end" data-value="{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_owed_gold) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_owed_gold) }}{% endif %}">
                                    {% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_owed_gold) }}克{% else %}{{ "%.2f"|format(default_previous_balance.previous_owed_gold) }}克{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                        <div class="d-flex align-items-center">
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">上期存料:</span>
                                <span id="previous-deposit-gold" class="ms-3 text-end">{{ "%.2f"|format(default_previous_balance.previous_deposit_gold) }}克</span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">本期存料:</span>
                                <span id="current-deposit-gold" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_deposit_gold) }}{% else %}0{% endif %}">
                                    {% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_deposit_gold) }}克{% else %}0.00克{% endif %}
                                </span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">存料抵扣:</span>
                                <span id="withdraw-deposit-gold" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.withdraw_deposit_gold) }}{% else %}0{% endif %}">
                                    {% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.withdraw_deposit_gold) }}克{% else %}0.00克{% endif %}
                                </span>
                            </div>
                            <div style="min-width: 180px;">
                                <span class="small fw-bold">总计存料:</span>
                                <span id="total-deposit-gold" class="ms-3 fw-bold text-end" data-value="{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_deposit_gold) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_deposit_gold) }}{% endif %}">
                                    {% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_deposit_gold) }}克{% else %}{{ "%.2f"|format(default_previous_balance.previous_deposit_gold) }}克{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                        <div class="d-flex align-items-center">
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">上期欠款:</span>
                                <span id="previous-owed-amount" class="ms-3 text-end">¥{{ "%.2f"|format(default_previous_balance.previous_owed_amount) }}</span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">本期欠款:</span>
                                <span id="current-owed-amount" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_owed_amount) }}{% else %}0{% endif %}">
                                    ¥{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_owed_amount) }}{% else %}0{% endif %}
                                </span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">本期还款:</span>
                                <span id="return-owed-amount" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.return_owed_amount) }}{% else %}0{% endif %}">
                                    ¥{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.return_owed_amount) }}{% else %}0{% endif %}
                                </span>
                            </div>
                            <div style="min-width: 180px;">
                                <span class="small fw-bold">总计欠款:</span>
                                <span id="total-owed-amount" class="ms-3 fw-bold text-end" data-value="{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_owed_amount) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_owed_amount) }}{% endif %}">
                                    ¥{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_owed_amount) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_owed_amount) }}{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                        <div class="d-flex align-items-center">
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">上期存款:</span>
                                <span id="previous-deposit-amount" class="ms-3 text-end">¥{{ "%.2f"|format(default_previous_balance.previous_deposit_amount) }}</span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">本期存款:</span>
                                <span id="current-deposit-amount" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_deposit_amount) }}{% else %}0{% endif %}">
                                    ¥{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_deposit_amount) }}{% else %}0{% endif %}
                                </span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">存款抵扣:</span>
                                <span id="withdraw-deposit-amount" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.withdraw_deposit_amount) }}{% else %}0{% endif %}">
                                    ¥{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.withdraw_deposit_amount) }}{% else %}0{% endif %}
                                </span>
                            </div>
                            <div style="min-width: 180px;">
                                <span class="small fw-bold">总计存款:</span>
                                <span id="total-deposit-amount" class="ms-3 fw-bold text-end" data-value="{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_deposit_amount) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_deposit_amount) }}{% endif %}">
                                    ¥{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_deposit_amount) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_deposit_amount) }}{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card h-100">
                <div class="card-body p-2 d-flex align-items-center justify-content-center">
                    <button type="button" id="save-btn" class="btn btn-success me-2" style="font-weight: bold;">保存</button>
                    <a href="/supplier_transactions" class="btn btn-secondary" style="font-weight: bold;">返回</a>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// 全局供应商数据变量
window.suppliersData = [];

// 统一错误处理函数
function handleError(error, context = '操作') {
    const message = error.message || '发生未知错误';
    alert(`${context}失败: ${message}`);
}

// 统一成功提示函数
function showSuccess(message, callback) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: '操作成功',
            text: message
        }).then(callback);
    } else {
        alert(message);
        if (callback) callback();
    }
}
{% if suppliers and suppliers|length > 0 %}
window.suppliersData = [
    {% for supplier in suppliers %}
    {
        id: {{ supplier.id }},
        name: "{{ supplier.name|replace('"', '\\"') }}",
        owed_gold: {{ supplier.owed_gold or 0 }},
        deposit_gold: {{ supplier.deposit_gold or 0 }},
        owed_amount: {{ supplier.owed_amount or 0 }},
        deposit_amount: {{ supplier.deposit_amount or 0 }},
        contact_person: "{{ supplier.contact_person or '' }}",
        phone: "{{ supplier.phone or '' }}",
        supplier_type: "{{ supplier.supplier_type or '' }}",
        pinyin: "{{ supplier.pinyin or '' }}",
        pinyin_initials: "{{ supplier.pinyin_initials or '' }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
];
{% endif %}

// 主要的页面初始化函数 - 统一的DOMContentLoaded事件处理器
document.addEventListener('DOMContentLoaded', function() {
    try {
        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            // 静默处理错误
        });

        // 首先初始化全局变量
        initializeGlobalVariables();

        // 初始化上期余额的dataset值（避免计算错误）
        initializePreviousBalanceDataset();

        // 立即初始化总计显示，避免从0开始跳动
        initializeTotalDisplays();

        // 检查是否为编辑模式
        const isEditMode = document.getElementById('transaction-id').value !== '';

        // 立即初始化页面基础功能，不等待异步数据
        // 编辑模式下延迟更新选择框，避免覆盖预设值
        if (!isEditMode) {
            updateAllSelects();
        }
        setupDataChangeListeners();
        initializePageFeatures();
        if (isEditMode) {
            // 编辑模式下预设置加载状态，避免显示空白
            presetEditModeDisplay();
            // 立即加载编辑数据
            startLoadEditDataIfNeeded();
        }

        // 在后台异步加载数据，但不阻塞页面显示
        Promise.all([
            fetchOldMaterials(),
            fetchAccounts(),
            fetchGoldSuppliers()
        ]).then(() => {
            // 数据加载完成后，更新选择框以确保最新数据
            // 重置更新标志，允许重新更新
            window.allSelectsUpdated = false;
            updateAllSelects();

            // 如果不是编辑模式，现在可以安全地初始化供应商信息
            if (!isEditMode) {
                const supplierIdInput = document.getElementById('supplier-id');
                if (supplierIdInput && supplierIdInput.value) {
                    updateSupplierInfo();
                }
            }
        }).catch(error => {
            // 数据加载失败时的静默处理
        });

        // 绑定保存按钮事件
        const saveBtn = document.getElementById('save-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                submitForm();
            });
        }

    } catch (error) {
        handleError(error, '页面初始化');
    }
});

// 预设置编辑模式显示，避免空白状态跳动
function presetEditModeDisplay() {
    // 隐藏交易记录的空白提示
    const initialPlaceholder = document.getElementById('initial-placeholder');
    if (initialPlaceholder) {
        initialPlaceholder.style.display = 'none';
    }

    // 编辑模式下不显示加载状态，因为数据已经在服务器端预计算
    // 直接保持服务器端渲染的值即可
}

// 初始化所有余额数据的dataset值（从服务器端渲染的值中获取）
function initializePreviousBalanceDataset() {
    // 编辑模式下，所有数据都已经在服务器端预计算并渲染
    const isEditMode = document.getElementById('transaction-id').value !== '';

    if (isEditMode) {
        // 编辑模式下，从已渲染的data-value属性中获取值
        const elements = [
            'previous-owed-gold', 'current-owed-gold', 'return-owed-gold', 'total-owed-gold',
            'previous-deposit-gold', 'current-deposit-gold', 'withdraw-deposit-gold', 'total-deposit-gold',
            'previous-owed-amount', 'current-owed-amount', 'return-owed-amount', 'total-owed-amount',
            'previous-deposit-amount', 'current-deposit-amount', 'withdraw-deposit-amount', 'total-deposit-amount'
        ];

        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element && element.dataset.value !== undefined) {
                // 数据已经在data-value属性中，无需额外处理
                console.log(`编辑模式：${id} 数据已预设 = ${element.dataset.value}`);
            }
        });
    } else {
        // 新增模式下，只设置上期余额的dataset值
        const elements = [
            { id: 'previous-owed-gold', dataset: 'previousOwedGold' },
            { id: 'previous-deposit-gold', dataset: 'previousDepositGold' },
            { id: 'previous-owed-amount', dataset: 'previousOwedAmount' },
            { id: 'previous-deposit-amount', dataset: 'previousDepositAmount' }
        ];

        elements.forEach(item => {
            const element = document.getElementById(item.id);
            if (element) {
                // 从显示的文本中提取数值并设置到dataset
                const text = element.textContent || '';
                const match = text.match(/[\d.]+/);
                if (match) {
                    element.dataset[item.dataset] = match[0];
                }
            }
        });
    }
}

// 如果是编辑模式，开始加载数据
function startLoadEditDataIfNeeded() {
    const transactionIdInput = document.getElementById('transaction-id');
    if (transactionIdInput && transactionIdInput.value) {
        const transactionId = transactionIdInput.value;


        // 确保所有必要的数据已加载
        if (!window.goldSuppliers || !Array.isArray(window.goldSuppliers) || window.goldSuppliers.length === 0) {

            useDefaultSuppliers();
        }

        if (!window.accounts || window.accounts.length === 0) {

        }
        
        fetch(`/api/supplier_transactions/get/${transactionId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data || typeof data !== 'object') {
                    throw new Error('API返回的数据格式无效');
                }

                if (data.success === true || data.status === 'success') {
                    if (!data.transaction || typeof data.transaction !== 'object') {
                        throw new Error('交易数据缺失或格式无效');
                    }

                    // 确保数组字段存在
                    if (!data.transaction.material_transactions) {
                        data.transaction.material_transactions = [];
                    }
                    if (!data.transaction.money_transactions) {
                        data.transaction.money_transactions = [];
                    }

                    // 调用loadEditData函数
                    try {
                        loadEditData(data.transaction);
                    } catch (error) {
                        handleError(error, '加载数据');
                    }
                } else {
                    const errorMessage = data.message || '加载交易数据失败';
                    alert(errorMessage);
                }
            })
            .catch(error => {
                let errorMessage = '加载交易数据失败';
                if (error.message) {
                    if (error.message.includes('JSON')) {
                        errorMessage = '服务器返回的数据格式错误，请检查服务器状态';
                    } else if (error.message.includes('HTTP')) {
                        errorMessage = '网络请求失败，请检查网络连接';
                    } else {
                        errorMessage = '请求错误: ' + error.message;
                    }
                }
                alert(errorMessage);
            });
        } else {
        // 检查是否有保存的表单数据
        const savedFormData = sessionStorage.getItem('supplierTransactionFormData');
        if (savedFormData) {
            try {
                const formData = JSON.parse(savedFormData);

                // 恢复表单数据
                if (formData.supplier_id) {
                    document.getElementById('supplier-id').value = formData.supplier_id;
                    updateSupplierInfo();
                }

                if (formData.business_date) {
                    document.getElementById('business-date').value = formData.business_date;
                }

                if (formData.notes) {
                    document.getElementById('notes').value = formData.notes;
                }

                sessionStorage.removeItem('supplierTransactionFormData');
            } catch (e) {
                // 忽略解析错误
            }
        }
    }
    
    // 初始化完成后，只在有实际数据变化时才计算
    // updateCalculations(); // 移除初始化时的计算，避免不必要的跳动
}

function initializeDisplayElements() {
    const displayElements = {
        'owed-gold': ['previous', 'current', 'return', 'total'],
        'deposit-gold': ['previous', 'current', 'withdraw', 'total'],
        'owed-amount': ['previous', 'current', 'return', 'total'],
        'deposit-amount': ['previous', 'current', 'withdraw', 'total']
    };

    for (const [type, prefixes] of Object.entries(displayElements)) {
        prefixes.forEach(prefix => {
            const elementId = `${prefix}-${type}`;
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = type.includes('amount') ? '¥0.00' : '0.00克';
            }
        });
    }
}

// 修改updateSupplierInfo函数确保显示2位小数
function updateSupplierInfo() {
    const supplierIdInput = document.getElementById('supplier-id');
    if (!supplierIdInput) return;

    // 检查是否处于编辑模式
    const isEditMode = document.getElementById('transaction-id').value !== '';

    try {
        if (supplierIdInput.value) {
            // 从全局供应商数据中获取供应商信息
            let supplierData = null;

            // 使用全局的suppliersData变量
            if (window.suppliersData && Array.isArray(window.suppliersData)) {
                supplierData = window.suppliersData.find(s => s.id == supplierIdInput.value);
            }

            if (!supplierData) {
                return;
            }

            // 在编辑模式下，完全禁用自动上期数据更新，只在编辑数据加载时进行一次性更新
            if (isEditMode) {
                updateSupplierOldMaterialsFromCache(supplierData);
                return;
            }

            const businessDate = document.getElementById('business-date')?.value;

            if (businessDate) {
                let queryDateTime = businessDate;

                // 在新增模式下，使用稍微晚一点的时间确保包含所有已保存的单据
                if (!isEditMode) {
                    // 解析业务日期时间，然后加1分钟
                    const dateTime = new Date(businessDate);
                    dateTime.setMinutes(dateTime.getMinutes() + 1);

                    const year = dateTime.getFullYear();
                    const month = String(dateTime.getMonth() + 1).padStart(2, '0');
                    const day = String(dateTime.getDate()).padStart(2, '0');
                    const hours = String(dateTime.getHours()).padStart(2, '0');
                    const minutes = String(dateTime.getMinutes()).padStart(2, '0');
                    queryDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;

                }

                // 编辑模式需要排除当前交易ID
                const excludeTransactionId = isEditMode ? getTransactionIdFromUrl() : null;
                loadUnifiedPreviousBalance(supplierData.id, queryDateTime, excludeTransactionId, supplierData.name, supplierData.contact_person);
            } else {
                // 如果没有业务日期，使用当前日期时间
                const now = new Date();

                // 在新增模式下，使用稍微晚一点的时间确保包含所有已保存的单据
                // 在编辑模式下，使用排除交易ID的方式
                if (!isEditMode) {
                    // 新增模式：使用当前时间+1分钟，确保包含所有已保存的单据
                    now.setMinutes(now.getMinutes() + 1);
                }

                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const todayDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;

                const excludeTransactionId = isEditMode ? getTransactionIdFromUrl() : null;
                loadUnifiedPreviousBalance(supplierData.id, todayDateTime, excludeTransactionId, supplierData.name, supplierData.contact_person);
            }
            // ===========================================================

            // 无论编辑模式还是新增模式，选择供应商后都需要清零本期数据
            updateElement('current-owed-gold', '0.00克');
            document.getElementById('current-owed-gold').dataset.value = '0.00';

            updateElement('return-owed-gold', '0.00克');
            document.getElementById('return-owed-gold').dataset.value = '0.00';

            updateElement('current-deposit-gold', '0.00克');
            document.getElementById('current-deposit-gold').dataset.value = '0.00';

            updateElement('withdraw-deposit-gold', '0.00克');
            document.getElementById('withdraw-deposit-gold').dataset.value = '0.00';

            updateElement('current-owed-amount', '¥0.00');
            document.getElementById('current-owed-amount').dataset.value = '0.00';

            updateElement('return-owed-amount', '¥0.00');
            document.getElementById('return-owed-amount').dataset.value = '0.00';

            updateElement('current-deposit-amount', '¥0.00');
            document.getElementById('current-deposit-amount').dataset.value = '0.00';

            updateElement('withdraw-deposit-amount', '¥0.00');
            document.getElementById('withdraw-deposit-amount').dataset.value = '0.00';

            // 总计数据将在 updateCalculations 中根据 上期 + 本期计算

            // 更新旧料明细
            updateSupplierOldMaterialsFromCache(supplierData);

        } else {
            // 如果没有选择供应商，则重置所有显示元素
            initializeDisplayElements();
            // 重置旧料明细区域
            preloadOldMaterialNames();
        }
        
        // 选择供应商后，只有在有实际数据变化时才更新计算
        // 由于上期数据已通过API直接更新，这里不需要立即计算
        // updateCalculations();

    } catch (error) {
        // 出错时设置默认值，不需要额外计算
        // updateCalculations(); // 移除不必要的计算调用
    }
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

// 统一获取上期数据（款料区域 + 旧料明细）- 避免数据跳动
async function loadUnifiedPreviousBalance(supplierId, businessDate, excludeTransactionId = null, supplierName = '', contactPerson = '') {
    try {
        // 生成唯一的请求标识符
        const requestKey = `unified_${supplierId}_${businessDate}_${excludeTransactionId || 'null'}`;

        // 防止重复调用
        if (window.currentUnifiedRequest === requestKey) {
            return;
        }

        // 如果已经有相同的数据，跳过请求
        if (window.lastUnifiedRequest === requestKey && window.currentPreviousBalance) {
            return;
        }

        // 设置全局锁，防止其他函数同时更新上期数据
        window.previousBalanceUpdating = true;

        window.currentUnifiedRequest = requestKey;

        // 同时调用两个API
        const [balanceResponse, detailsResponse] = await Promise.all([
            fetch(`/api/supplier_previous_balance/${supplierId}?business_date=${businessDate}${excludeTransactionId ? `&exclude_transaction_id=${excludeTransactionId}` : ''}`),
            fetch(`/api/supplier_details/${supplierId}`)
        ]);

        if (!balanceResponse.ok || !detailsResponse.ok) {
            throw new Error(`API请求失败: balance=${balanceResponse.status}, details=${detailsResponse.status}`);
        }

        const [balanceResult, detailsResult] = await Promise.all([
            balanceResponse.json(),
            detailsResponse.json()
        ]);

        if (balanceResult.success && detailsResult.success) {
            const balanceData = balanceResult.data;

            // 直接更新款料区域的上期数据（参考订单页批发客户区域的实现）
            const updates = [
                ['previous-owed-gold', balanceData.previous_owed_gold.toFixed(2) + '克'],
                ['previous-deposit-gold', balanceData.previous_deposit_gold.toFixed(2) + '克'],
                ['previous-owed-amount', '¥' + balanceData.previous_owed_amount.toFixed(2)],
                ['previous-deposit-amount', '¥' + balanceData.previous_deposit_amount.toFixed(2)]
            ];

            // 批量更新DOM元素，减少重排重绘
            updates.forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    // 同时更新dataset值用于计算
                    const numericValue = parseFloat(value.replace(/[^0-9.-]/g, ''));
                    element.dataset.value = numericValue.toFixed(2);
                }
            });

            // 保存全局变量
            window.currentPreviousBalance = balanceData;

            // 同时更新旧料明细
            if (detailsResult.details && supplierName) {
                const materialTotals = calculateMaterialTotalsFromDetails(detailsResult.details, detailsResult.materials);
                displayOldMaterialDetailsFromTotals(materialTotals, supplierName, contactPerson);
            }

            // 标记请求完成并缓存
            window.lastUnifiedRequest = window.currentUnifiedRequest;
            window.currentUnifiedRequest = null;
            window.previousBalanceUpdating = false; // 释放锁

            // 由于已经通过批量DOM更新设置了上期数据，这里不需要额外计算
            // 只有在有交易数据时才需要重新计算
            if (document.querySelectorAll('.transaction-row tbody tr').length > 0) {
                updateCalculations();
            }
        } else {
            window.currentUnifiedRequest = null;
            window.previousBalanceUpdating = false; // 释放锁
            setDefaultPreviousBalance();
        }
    } catch (error) {
        window.currentUnifiedRequest = null;
        window.previousBalanceUpdating = false; // 释放锁
        setDefaultPreviousBalance();
    }
}

// 从供应商款料明细API获取上期余额数据（保留原函数用于其他地方调用）
async function loadSupplierPreviousBalance(supplierId, businessDate, excludeTransactionId = null) {
    try {
        // 检查是否有其他函数正在更新上期数据
        if (window.previousBalanceUpdating) {
            return;
        }

        // 生成唯一的请求标识符
        const requestKey = `${supplierId}_${businessDate}_${excludeTransactionId || 'null'}`;

        // 防止重复调用
        if (window.currentPreviousBalanceRequest === requestKey) {
            return;
        }

        // 如果已经有相同的数据，跳过请求
        if (window.lastPreviousBalanceRequest === requestKey && window.currentPreviousBalance) {
            return;
        }
        window.currentPreviousBalanceRequest = requestKey;

        let url = `/api/supplier_previous_balance/${supplierId}?business_date=${businessDate}`;
        if (excludeTransactionId) {
            url += `&exclude_transaction_id=${excludeTransactionId}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            const data = result.data;

            // 直接更新上期数据显示（参考订单页批发客户区域的实现）
            const updates = [
                ['previous-owed-gold', data.previous_owed_gold.toFixed(2) + '克'],
                ['previous-deposit-gold', data.previous_deposit_gold.toFixed(2) + '克'],
                ['previous-owed-amount', '¥' + data.previous_owed_amount.toFixed(2)],
                ['previous-deposit-amount', '¥' + data.previous_deposit_amount.toFixed(2)]
            ];

            // 批量更新DOM元素，减少重排重绘
            updates.forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    // 同时更新dataset值用于计算
                    const numericValue = parseFloat(value.replace(/[^0-9.-]/g, ''));
                    element.dataset.value = numericValue.toFixed(2);
                }
            });

            // 保存全局变量供旧料明细使用
            window.currentPreviousBalance = data;

            // 标记请求完成并缓存
            window.lastPreviousBalanceRequest = window.currentPreviousBalanceRequest;
            window.currentPreviousBalanceRequest = null;

            // 同步更新旧料明细表格
            const currentSupplier = getCurrentSupplierData();
            if (currentSupplier) {
                displayOldMaterialDetailsFromBalance(null, currentSupplier.name, currentSupplier.contact_person);
            }

            // 更新计算
            updateCalculations();
        } else {
            // 清除请求标志
            window.currentPreviousBalanceRequest = null;
            // 如果API失败，使用默认值0
            setDefaultPreviousBalance();
        }
    } catch (error) {
        // 清除请求标志
        window.currentPreviousBalanceRequest = null;
        // 如果出错，使用默认值0
        setDefaultPreviousBalance();
    }
}



// 初始化上期余额的dataset值（从服务器端渲染的值中获取）
function initializePreviousBalanceDataset() {
    // 从页面中获取服务器端渲染的默认值
    const previousOwedGoldElement = document.getElementById('previous-owed-gold');
    const previousDepositGoldElement = document.getElementById('previous-deposit-gold');
    const previousOwedAmountElement = document.getElementById('previous-owed-amount');
    const previousDepositAmountElement = document.getElementById('previous-deposit-amount');

    if (previousOwedGoldElement) {
        const value = parseFloat(previousOwedGoldElement.textContent.replace('克', '')) || 0;
        previousOwedGoldElement.dataset.value = value.toFixed(2);
    }

    if (previousDepositGoldElement) {
        const value = parseFloat(previousDepositGoldElement.textContent.replace('克', '')) || 0;
        previousDepositGoldElement.dataset.value = value.toFixed(2);
    }

    if (previousOwedAmountElement) {
        const value = parseFloat(previousOwedAmountElement.textContent.replace('¥', '')) || 0;
        previousOwedAmountElement.dataset.value = value.toFixed(2);
    }

    if (previousDepositAmountElement) {
        const value = parseFloat(previousDepositAmountElement.textContent.replace('¥', '')) || 0;
        previousDepositAmountElement.dataset.value = value.toFixed(2);
    }
}

// 初始化总计显示，避免从0开始跳动到最终值
function initializeTotalDisplays() {
    // 获取上期数据作为初始总计值（新增模式下）
    const previousOwedGold = parseFloat(document.getElementById('previous-owed-gold')?.textContent?.replace('克', '')) || 0;
    const previousDepositGold = parseFloat(document.getElementById('previous-deposit-gold')?.textContent?.replace('克', '')) || 0;
    const previousOwedAmount = parseFloat(document.getElementById('previous-owed-amount')?.textContent?.replace('¥', '')) || 0;
    const previousDepositAmount = parseFloat(document.getElementById('previous-deposit-amount')?.textContent?.replace('¥', '')) || 0;

    // 初始化本期数据为0，总计为上期数据
    const initialUpdates = [
        ['current-owed-gold', '0.00克'],
        ['return-owed-gold', '0.00克'],
        ['total-owed-gold', previousOwedGold.toFixed(2) + '克'],
        ['current-deposit-gold', '0.00克'],
        ['withdraw-deposit-gold', '0.00克'],
        ['total-deposit-gold', previousDepositGold.toFixed(2) + '克'],
        ['return-owed-amount', '¥0.00'],
        ['current-owed-amount', '¥0.00'],
        ['current-deposit-amount', '¥0.00'],
        ['withdraw-deposit-amount', '¥0.00'],
        ['total-owed-amount', '¥' + previousOwedAmount.toFixed(2)],
        ['total-deposit-amount', '¥' + previousDepositAmount.toFixed(2)]
    ];

    // 批量设置初始值，避免跳动
    initialUpdates.forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// 设置默认的上期余额（全部为0）
function setDefaultPreviousBalance() {
    // 检查是否为编辑模式，如果是则不重置为0
    const isEditMode = document.getElementById('transaction-id').value !== '';
    if (isEditMode) {
        return; // 编辑模式下保持现有值
    }

    updateElement('previous-owed-gold', '0.00克');
    document.getElementById('previous-owed-gold').dataset.value = '0.00';

    updateElement('previous-deposit-gold', '0.00克');
    document.getElementById('previous-deposit-gold').dataset.value = '0.00';

    updateElement('previous-owed-amount', '¥0.00');
    document.getElementById('previous-owed-amount').dataset.value = '0.00';

    updateElement('previous-deposit-amount', '¥0.00');
    document.getElementById('previous-deposit-amount').dataset.value = '0.00';
}

// 从URL中获取交易ID（用于编辑模式）
function getTransactionIdFromUrl() {
    const path = window.location.pathname;
    const match = path.match(/\/supplier_transaction_edit\/(\d+)/);
    return match ? parseInt(match[1]) : null;
}

// 初始化业务日期变化监听器
function initializeBusinessDateListener() {
    const businessDateInput = document.getElementById('business-date');
    const supplierSelect = document.getElementById('supplier-id');

    if (businessDateInput && supplierSelect) {
        businessDateInput.addEventListener('change', function() {
            const supplierId = supplierSelect.value;
            const businessDate = this.value;

            if (supplierId && businessDate) {
                // 在编辑模式下，禁用业务日期变化时的上期数据更新
                const isEditMode = window.location.pathname.includes('/edit/');
                if (isEditMode) {
                    return;
                }

                const excludeTransactionId = null; // 新增模式不需要排除

                // 获取供应商信息用于旧料明细更新
                const currentSupplier = getCurrentSupplierData();
                if (currentSupplier) {
                    loadUnifiedPreviousBalance(supplierId, businessDate, excludeTransactionId, currentSupplier.name, currentSupplier.contact_person);
                } else {
                    loadSupplierPreviousBalance(supplierId, businessDate, excludeTransactionId);
                }
            }
        });
    }
}

// 从URL中获取交易ID
function getTransactionIdFromUrl() {
    const pathParts = window.location.pathname.split('/');
    const editIndex = pathParts.indexOf('edit');
    if (editIndex !== -1 && editIndex + 1 < pathParts.length) {
        return pathParts[editIndex + 1];
    }
    return null;
}

// 添加更新旧料明细的函数
function preloadOldMaterialNames() {
    // 检查是否为编辑模式，如果是则不重置旧料明细
    const isEditMode = document.getElementById('transaction-id').value !== '';
    if (isEditMode) {
        return; // 编辑模式下保持现有内容
    }

    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (supplierOwedMaterialsDiv) {
        supplierOwedMaterialsDiv.innerHTML = '<div class="text-center py-3"><small class="text-muted">请选择供应商查看旧料明细</small></div>';
    }
}

function updateSupplierOldMaterialsFromCache(supplierData) {
    // 编辑模式下使用服务器端预计算的旧料明细，不需要异步加载
    const isEditMode = document.getElementById('transaction-id').value !== '';
    if (isEditMode) {
        // 编辑模式下旧料明细已经在服务器端预计算并渲染，无需更新
        return;
    }

    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    // 如果没有旧料数据，显示空提示
    if (!supplierData || !supplierData.name) {
        supplierOwedMaterialsDiv.innerHTML = '<div class="text-center py-3"><small class="text-muted">暂无旧料明细数据</small></div>';
        return;
    }

    // 新增模式下才需要异步获取旧料明细
    // 直接从供应商款料明细表计算上期数据
    displayOldMaterialDetailsFromBalance(null, supplierData.name, supplierData.contact_person);
}

// 优化版本：使用与款料区域相同的逻辑更新旧料明细，避免重复调用
function updateOldMaterialDetailsFromPreviousBalance(supplierId, supplierName, contactPerson) {
    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    // 防止多次执行的标志
    if (window.oldMaterialDetailsUpdating) {
        return;
    }
    window.oldMaterialDetailsUpdating = true;

    // 直接从供应商款料明细表计算上期数据
    displayOldMaterialDetailsFromBalance(null, supplierName, contactPerson);

    // 延迟释放标志，避免过快的重复调用
    setTimeout(() => {
        window.oldMaterialDetailsUpdating = false;
    }, 100);
}

// 辅助函数：获取当前选中的供应商数据
function getCurrentSupplierData() {
    const supplierSelect = document.getElementById('supplier');
    if (!supplierSelect || !supplierSelect.value) return null;

    const selectedOption = supplierSelect.options[supplierSelect.selectedIndex];
    if (!selectedOption) return null;

    return {
        id: supplierSelect.value,
        name: selectedOption.dataset.name || selectedOption.text,
        contact_person: selectedOption.dataset.contactPerson || ''
    };
}

// 新增函数：获取当前单据的业务时间
function getCurrentBusinessDate() {
    const businessDateInput = document.getElementById('business-date');
    if (businessDateInput && businessDateInput.value) {
        return businessDateInput.value;
    }

    // 如果没有设置业务时间，返回今天的日期
    const today = new Date();
    return today.toISOString().split('T')[0];
}

// 新增函数：获取当前单据ID（编辑模式下）
function getCurrentTransactionId() {
    // 检查是否是编辑模式
    const isEditMode = window.location.pathname.includes('/edit');
    if (!isEditMode) {
        return null;
    }

    // 从URL中提取单据ID
    const pathParts = window.location.pathname.split('/');
    const editIndex = pathParts.indexOf('edit');
    if (editIndex > 0) {
        return pathParts[editIndex - 1];
    }

    return null;
}

// 优化版本：从供应商款料明细表计算并显示旧料明细，避免重复调用
function displayOldMaterialDetailsFromBalance(balanceData, supplierName, contactPerson) {
    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    // 获取当前供应商ID
    const supplierIdInput = document.getElementById('supplier-id');
    if (!supplierIdInput || !supplierIdInput.value) {
        displayOldMaterialDetailsDefault(supplierName, contactPerson, {});
        return;
    }

    const supplierId = supplierIdInput.value;

    // 防止重复调用的标志
    const callKey = `${supplierId}_${supplierName}_${Date.now()}`;
    if (window.lastOldMaterialDetailsCall === callKey) {
        return;
    }
    window.lastOldMaterialDetailsCall = callKey;

    // 从供应商款料明细表获取上期数据
    fetchSupplierMaterialDetailsForOldMaterials(supplierId, supplierName, contactPerson);
}

// 优化版本：从供应商款料明细表获取旧料上期数据，避免重复调用
let oldMaterialDetailsCache = new Map();
let oldMaterialDetailsPromises = new Map();

function fetchSupplierMaterialDetailsForOldMaterials(supplierId, supplierName, contactPerson) {
    const cacheKey = `${supplierId}_${getCurrentBusinessDate()}`;

    // 如果有缓存，直接使用
    if (oldMaterialDetailsCache.has(cacheKey)) {
        const cachedData = oldMaterialDetailsCache.get(cacheKey);
        displayOldMaterialDetailsFromTotals(cachedData, supplierName, contactPerson);
        return;
    }

    // 如果正在请求中，等待现有请求
    if (oldMaterialDetailsPromises.has(cacheKey)) {
        oldMaterialDetailsPromises.get(cacheKey).then(materialTotals => {
            displayOldMaterialDetailsFromTotals(materialTotals, supplierName, contactPerson);
        });
        return;
    }

    // 创建新的请求
    const promise = fetch(`/api/supplier_details/${supplierId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.details) {
                // 计算当前单据上方所有旧料的累计值
                const materialTotals = calculateMaterialTotalsFromDetails(data.details, data.materials);

                // 缓存结果
                oldMaterialDetailsCache.set(cacheKey, materialTotals);

                return materialTotals;
            } else {
                throw new Error('获取供应商款料明细失败');
            }
        })
        .catch(error => {
            console.error('❌ 获取供应商款料明细出错:', error);
            // 返回空数据
            return {};
        })
        .finally(() => {
            // 清除Promise缓存
            oldMaterialDetailsPromises.delete(cacheKey);
        });

    // 缓存Promise
    oldMaterialDetailsPromises.set(cacheKey, promise);

    // 执行请求并显示结果
    promise.then(materialTotals => {
        if (Object.keys(materialTotals).length > 0) {
            displayOldMaterialDetailsFromTotals(materialTotals, supplierName, contactPerson);
        } else {
            displayOldMaterialDetailsDefault(supplierName, contactPerson, {});
        }
    });
}

// 新增函数：从款料明细数据计算旧料累计值（累计所有之前单据的旧料数据变化）
function calculateMaterialTotalsFromDetails(details, materials) {
    console.log('🧮 开始计算旧料累计值（累计所有之前单据的旧料数据变化）...');

    // 获取当前单据的业务时间
    const currentBusinessDate = getCurrentBusinessDate();
    console.log('📅 当前单据业务时间:', currentBusinessDate);

    // 获取当前单据ID（编辑模式下）
    const currentTransactionId = getCurrentTransactionId();
    console.log('🆔 当前单据ID:', currentTransactionId);

    // 初始化累计值
    const materialTotals = {};

    // 为所有旧料类型初始化累计值
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        window.oldMaterials.forEach(material => {
            materialTotals[material.name] = {
                owed: 0,
                stored: 0
            };
        });
    }

    // 按时间排序所有记录
    const sortedDetails = details.slice().sort((a, b) => {
        if (a.business_time === '初始值') return -1;
        if (b.business_time === '初始值') return 1;
        return new Date(a.business_time) - new Date(b.business_time);
    });

    // 累计所有之前单据的旧料数据变化
    for (let i = 0; i < sortedDetails.length; i++) {
        const detail = sortedDetails[i];

        // 处理初始值记录
        if (detail.business_time === '初始值') {
            console.log('📋 处理初始值记录');
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                window.oldMaterials.forEach(material => {
                    const materialName = material.name;
                    const owedKey = `${materialName}_debt`;
                    const storedKey = `${materialName}_storage`;

                    if (detail[owedKey] !== undefined) {
                        const owedValue = parseFloat(detail[owedKey]) || 0;
                        materialTotals[materialName].owed = owedValue; // 初始值直接设置
                        console.log(`  ${materialName} 初始欠料: ${owedValue}`);
                    }
                    if (detail[storedKey] !== undefined) {
                        const storedValue = parseFloat(detail[storedKey]) || 0;
                        materialTotals[materialName].stored = storedValue; // 初始值直接设置
                        console.log(`  ${materialName} 初始存料: ${storedValue}`);
                    }
                });
            }
            continue;
        }

        // 跳过当前单据（编辑模式下）
        if (currentTransactionId && detail.document_id &&
            detail.document_id.toString() === currentTransactionId.toString()) {
            console.log(`⏭️ 跳过当前单据记录: ${detail.business_time} (ID: ${detail.document_id})`);
            continue;
        }

        // 检查业务时间是否在当前单据之前
        if (currentBusinessDate && detail.business_time) {
            const detailDate = new Date(detail.business_time);
            const currentDate = new Date(currentBusinessDate);

            if (detailDate >= currentDate) {
                console.log(`⏭️ 到达当前或之后的记录，停止累计: ${detail.business_time}`);
                break;
            }
        }

        console.log(`✅ 累计之前单据记录: ${detail.business_time}`);

        // 累加变化值到累计余额（API返回的是变化值，需要累加）
        if (window.oldMaterials && window.oldMaterials.length > 0) {
            window.oldMaterials.forEach(material => {
                const materialName = material.name;
                const debtKey = `${materialName}_debt`;
                const storageKey = `${materialName}_storage`;

                // 累加变化值
                if (detail.hasOwnProperty(debtKey)) {
                    const debtChange = parseFloat(detail[debtKey]) || 0;
                    materialTotals[materialName].owed += debtChange;
                    console.log(`  ${materialName} 欠料变化: +${debtChange} = ${materialTotals[materialName].owed} (记录时间: ${detail.business_time})`);
                }

                if (detail.hasOwnProperty(storageKey)) {
                    const storageChange = parseFloat(detail[storageKey]) || 0;
                    materialTotals[materialName].stored += storageChange;
                    console.log(`  ${materialName} 存料变化: +${storageChange} = ${materialTotals[materialName].stored} (记录时间: ${detail.business_time})`);
                }
            });
        }
    }

    console.log('📊 旧料累计值计算完成（累计所有之前单据的变化）:', materialTotals);
    return materialTotals;
}

// 新增函数：根据累计值显示旧料明细
function displayOldMaterialDetailsFromTotals(materialTotals, supplierName, contactPerson) {
    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    console.log('📋 显示旧料明细（基于累计值）:', materialTotals);

    // 构建HTML
    let html = `
        <div class="p-2">
            <div class="simple-table-responsive">
                <table class="supplier-transaction-table" style="table-layout: fixed; width: 100%;">
                    <thead>
                        <tr>
                            <th style="width: 33.33%;">旧料名称</th>
                            <th style="width: 33.33%;">上期欠料</th>
                            <th style="width: 33.33%;">上期存料</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // 遍历所有旧料类型，显示累计值
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        window.oldMaterials.forEach(material => {
            const materialName = material.name;
            const owedAmount = materialTotals[materialName]?.owed || 0;
            const depositAmount = materialTotals[materialName]?.stored || 0;

            // 显示所有旧料类型，包括0值
            html += `
                <tr>
                    <td>${materialName}</td>
                    <td class="${owedAmount > 0 ? 'text-danger' : owedAmount < 0 ? 'text-success' : ''}">${owedAmount.toFixed(2)}克</td>
                    <td class="${depositAmount > 0 ? 'text-success' : depositAmount < 0 ? 'text-danger' : ''}">${depositAmount.toFixed(2)}克</td>
                </tr>
            `;
        });
    } else {
        html += `
            <tr>
                <td colspan="3" class="text-center text-muted py-3">
                    旧料数据加载中...
                </td>
            </tr>
        `;
    }

    html += `
                    </tbody>
                </table>
            </div>
            <div class="mt-2 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    上期数据来自供应商款料明细表累计值
                </small>
            </div>
        </div>
    `;

    supplierOwedMaterialsDiv.innerHTML = html;
}

// 新增函数：默认显示（当没有全局数据时）
function displayOldMaterialDetailsDefault(supplierName, contactPerson, basicData) {
    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    console.log('📋 显示旧料明细（默认模式）:', basicData);

    // 构建HTML
    let html = `
        <div class="p-2">
            <div class="simple-table-responsive">
                <table class="supplier-transaction-table" style="table-layout: fixed; width: 100%;">
                    <thead>
                        <tr>
                            <th style="width: 33.33%;">旧料名称</th>
                            <th style="width: 33.33%;">上期欠料</th>
                            <th style="width: 33.33%;">上期存料</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // 显示所有旧料类型的数据，包括0值
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        window.oldMaterials.forEach(material => {
            const materialName = material.name;
            let owedAmount = 0;
            let depositAmount = 0;

            // 如果是旧料足金，使用传入的数据
            if (materialName === '旧料足金') {
                owedAmount = basicData.previous_owed_gold || 0;
                depositAmount = basicData.previous_deposit_gold || 0;
            }
            // 其他旧料类型显示为0

            html += `
                <tr>
                    <td>${materialName}</td>
                    <td class="${owedAmount > 0 ? 'text-danger' : owedAmount < 0 ? 'text-success' : ''}">${owedAmount.toFixed(2)}克</td>
                    <td class="${depositAmount > 0 ? 'text-success' : depositAmount < 0 ? 'text-danger' : ''}">${depositAmount.toFixed(2)}克</td>
                </tr>
            `;
        });
    } else {
        html += `
            <tr>
                <td colspan="3" class="text-center text-muted py-3">
                    旧料数据加载中...
                </td>
            </tr>
        `;
    }

    html += `
                    </tbody>
                </table>
            </div>
            <div class="mt-2 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    与款料区域数据一致
                </small>
            </div>
        </div>
    `;

    supplierOwedMaterialsDiv.innerHTML = html;
}

document.getElementById('save-btn')?.addEventListener('click', async function() {
    try {
        // 基本验证 (已有)

        // 获取结算后的总计数据（去掉单位后的数值）
        const totalOwedGold = parseFloat(document.getElementById('total-owed-gold').textContent.replace('克', '')) || 0;
        const totalDepositGold = parseFloat(document.getElementById('total-deposit-gold').textContent.replace('克', '')) || 0;
        const totalOwedAmount = parseFloat(document.getElementById('total-owed-amount').textContent.replace('¥', '')) || 0;
        const totalDepositAmount = parseFloat(document.getElementById('total-deposit-amount').textContent.replace('¥', '')) || 0;

        // 构建数据对象，包含更新供应商信息的数据
        const data = {
            supplier_id: parseInt(document.getElementById('supplier-id')?.value),
            business_date: document.getElementById('business-date')?.value,
            transaction_no: document.getElementById('transaction-no')?.value || '',
            notes: document.getElementById('notes')?.value || '',
            material_transactions: [],
            money_transactions: [],
            // 添加总计数据用于更新供应商信息
            updated_supplier_info: {
                owed_gold: totalOwedGold,
                deposit_gold: totalDepositGold,
                owed_amount: totalOwedAmount,
                deposit_amount: totalDepositAmount
            },
            // 添加账户交易数据
            account_transactions: []
        };

        // 基本验证
        if (!data.supplier_id) {
            alert('请选择供应商');
            return;
        }
        if (!data.business_date) {
            alert('请选择业务日期');
            return;
        }

        console.log('开始收集交易数据');
        
        // 收集所有料项和款项数据
        // 从所有行获取数据，无论它们是在哪个表格中
        const allTransactionRows = document.querySelectorAll('.transaction-row');
        allTransactionRows.forEach(row => {
            const type = row.getAttribute('data-type');
            const tableRows = row.querySelectorAll('tbody tr');
            
            console.log(`处理 ${type} 类型的交易行，找到 ${tableRows.length} 行`);
            
            tableRows.forEach((tr, idx) => {
                console.log(`处理 ${type} 的第 ${idx+1} 行`);
                
                // 针对不同类型收集数据
                if (type === 'return_material') {
                    const returnWeight = parseFloat(tr.querySelector('[name="return_weight[]"]')?.value) || 0;
                    const returnSource = tr.querySelector('[name="return_material_source[]"]')?.value || '';
                    const returnMaterialTypeElement = tr.querySelector('[name="return_material_type[]"]');
                    const returnMaterialType = returnMaterialTypeElement?.value || '';
                    const note = tr.querySelector('[name="material_note[]"]')?.value || '';

                    console.log(`还料数据: 重量=${returnWeight}, 来源=${returnSource}, 类型=${returnMaterialType}`);
                    console.log(`还料旧料类型选择框:`, returnMaterialTypeElement);
                    console.log(`还料旧料类型选择框的所有选项:`, Array.from(returnMaterialTypeElement?.options || []).map(opt => `${opt.value}:${opt.text}`));
                    
                    if (returnWeight > 0) {
                data.material_transactions.push({
                    return_weight: returnWeight,
                    return_source: returnSource,
                    return_material_type: returnMaterialType,
                            note: note
                        });
                        
                        // 如果还料来源是其他金料供应商，添加账户交易记录
                        if (returnSource !== '' && returnSource !== '存料抵扣') {
                            // 检查是否是金料供应商
                            const isGoldSupplier = window.goldSuppliers && window.goldSuppliers.some(s => s.name === returnSource);
                            if (isGoldSupplier) {
                                // 找到对应供应商信息
                                const goldSupplier = window.goldSuppliers.find(s => s.name === returnSource);
                                if (goldSupplier) {
                                    console.log(`添加其他金料供应商 ${returnSource} 的欠料记录: ${returnWeight}克`);
                                    // 这里只记录信息，实际更新在后端进行
                                }
                            }
                        }
                    }
                } else if (type === 'deposit_material') {
                    const depositWeight = parseFloat(tr.querySelector('[name="deposit_weight[]"]')?.value) || 0;
                    const actualDepositWeight = parseFloat(tr.querySelector('[name="actual_deposit_weight[]"]')?.value) || 0;
                    const depositLoss = parseFloat(tr.querySelector('[name="deposit_loss[]"]')?.value) || 0;
                    const depositMaterialType = tr.querySelector('[name="deposit_material_type[]"]')?.value || '';
                    const note = tr.querySelector('[name="material_note[]"]')?.value || '';
                    
                    console.log(`寄料数据: 类型=${depositMaterialType}, 重量=${depositWeight}, 实际重量=${actualDepositWeight}, 损耗=${depositLoss}`);
                    
                    if (depositWeight > 0) {
                        data.material_transactions.push({
                            deposit_weight: depositWeight,
                            deposit_material_type: depositMaterialType,
                            actual_deposit_weight: actualDepositWeight,
                            deposit_loss: depositLoss,
                            note: note
                        });
                    }
                } else if (type === 'buy_material') {
                    const materialTypeElement = tr.querySelector('[name="buy_material_type[]"]');
                    const materialType = materialTypeElement?.value || '';
                    const buyWeight = parseFloat(tr.querySelector('[name="buy_weight[]"]')?.value) || 0;
                    const materialPrice = parseFloat(tr.querySelector('[name="material_price[]"]')?.value) || 0;
                    const buyTotal = parseFloat(tr.querySelector('[name="buy_total[]"]')?.value) || 0;
                    const note = tr.querySelector('[name="buy_note[]"]')?.value || '';

                    console.log(`买料数据: 类型=${materialType}, 重量=${buyWeight}, 单价=${materialPrice}`);
                    console.log(`买料旧料类型选择框:`, materialTypeElement);
                    console.log(`买料旧料类型选择框的所有选项:`, Array.from(materialTypeElement?.options || []).map(opt => `${opt.value}:${opt.text}`));
                    
                    if (buyWeight > 0) {
                        // 买料将转化为还款交易，同时带有材料信息
                        data.money_transactions.push({
                            return_amount: buyTotal,
                            return_source: '现金', // 买料默认来源为现金
                            return_purpose: '买料',
                            return_material_type: materialType,
                            return_weight: buyWeight,
                            note: note
                        });
                    }
                } else if (type === 'store_material') {
                    const materialType = tr.querySelector('[name="store_material_type[]"]')?.value || '';
                    const storeWeight = parseFloat(tr.querySelector('[name="store_weight[]"]')?.value) || 0;
                    const storeSource = tr.querySelector('[name="store_material_source[]"]')?.value || '';
                    const note = tr.querySelector('[name="material_note[]"]')?.value || '';
                    
                    console.log(`存料数据: 类型=${materialType}, 重量=${storeWeight}, 来源=${storeSource}`);
                    
                    if (storeWeight > 0) {
                        data.material_transactions.push({
                            store_weight: storeWeight,
                            store_source: storeSource,
                            store_material_type: materialType,
                            note: note
                        });
                    }
                } else if (type === 'return_money') {
                    const returnAmount = parseFloat(tr.querySelector('[name="return_amount[]"]')?.value) || 0;
                    const returnSource = tr.querySelector('[name="return_money_source[]"]')?.value || '';
                    const returnPurpose = tr.querySelector('[name="return_purpose[]"]')?.value || '还工费';  // 添加还款用途
                    const note = tr.querySelector('[name="money_note[]"]')?.value || '';
                    
                    console.log(`还款数据: 金额=${returnAmount}, 来源=${returnSource}, 用途=${returnPurpose}`);
                    
                    if (returnAmount > 0) {
                        data.money_transactions.push({
                            return_amount: returnAmount,
                            return_source: returnSource,
                            return_purpose: returnPurpose,
                            note: note
                        });
                        
                        // 如果还款来源是账户，创建账户交易记录
                        if (returnSource !== '' && returnSource !== '存款抵扣') {
                            // 检查是否是账户
                            const isAccount = window.accounts && window.accounts.some(a => a.name === returnSource);
                            if (isAccount) {
                                console.log(`添加账户 ${returnSource} 的支出记录: ¥${returnAmount}`);
                                // 获取供应商信息以便在账户交易记录中显示
                                const supplierName = document.querySelector('#supplier-id option:checked')?.text || '';
                                
                                data.account_transactions.push({
                                    account_name: returnSource,
                                    amount: -returnAmount,  // 支出为负数
                                    transaction_type: 'expense',
                                    purpose: returnPurpose === '买料' ? '供应商买料' : '供应商还款',
                                    related_party: supplierName,
                                    notes: note || `支付给供应商 ${supplierName} 的${returnPurpose === '买料' ? '买料款' : '工费'}`
                                });
                            }
                        }
                    }
                } else if (type === 'deposit_money') {
                    const storeAmount = parseFloat(tr.querySelector('[name="deposit_amount[]"]')?.value) || 0;
                    const depositType = tr.querySelector('[name="deposit_source[]"]')?.value || '';
                    const note = tr.querySelector('[name="money_note[]"]')?.value || '';
                    
                    console.log(`存款数据: 金额=${storeAmount}, 类型=${depositType}`);
                    
                    if (storeAmount > 0) {
                        data.money_transactions.push({
                            store_amount: storeAmount,
                            store_source: depositType,
                            note: note
                        });
                        
                        // 如果存款来源是账户，创建账户交易记录
                        if (depositType !== '') {
                            // 检查是否是账户
                            const isAccount = window.accounts && window.accounts.some(a => a.name === depositType);
                            if (isAccount) {
                                console.log(`添加账户 ${depositType} 的支出记录: ¥${storeAmount}`);
                                // 获取供应商信息以便在账户交易记录中显示
                                const supplierName = document.querySelector('#supplier-id option:checked')?.text || '';
                                
                                data.account_transactions.push({
                                    account_name: depositType,
                                    amount: -storeAmount,  // 支出为负数
                                    transaction_type: 'expense',
                                    purpose: '供应商存款',
                                    related_party: supplierName,
                                    notes: note || `支付给供应商 ${supplierName} 的存款`
                                });
                            }
                        }
                    }
                }
            });
        });
        
        console.log('收集到的数据总结:');
        console.log(`- 物料交易: ${data.material_transactions.length}条`);
        console.log(`- 款项交易: ${data.money_transactions.length}条`);
        console.log(`- 账户交易: ${data.account_transactions.length}条`);
        console.log('完整数据对象:', data);
        
        // 发送数据到服务器，编辑模式下使用PUT请求更新数据
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const transactionId = document.getElementById('transaction-id').value;
        const url = transactionId 
            ? `/supplier_transactions/${transactionId}/edit`  // 编辑模式
            : '/supplier_transactions/add';                  // 新增模式
            
        console.log(`使用API: ${url}`);
            
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        });

        // 改进错误处理，预防JSON解析错误
        let result;
        try {
            const responseText = await response.text();
            console.log('服务器原始响应:', responseText);
            
            // 检查响应是否为HTML (通常表示服务器错误页面)
            if (responseText.trim().startsWith('<')) {
                console.error('服务器返回了HTML而非JSON，可能是服务器错误');
                throw new Error('服务器返回了HTML错误页面，请联系管理员');
            }
            
            // 尝试解析JSON
            try {
                result = JSON.parse(responseText);
            } catch (jsonError) {
                console.error('JSON解析错误:', jsonError);
                throw new Error('无法解析服务器响应，请联系管理员');
            }
        } catch (error) {
            handleError(error, '处理响应');
            return;
        }

        console.log('服务器响应对象:', result);
        
        if (result.success) {
            // 检查是否是无变化的情况
            if (result.no_changes) {
                alert('没有检测到数据变更，将返回列表页面');
                window.location.href = '/supplier_transactions';
            } else {
            alert('保存成功！');
                window.location.href = '/supplier_transactions';
            }
        } else {
            // 检查是否是交易编号重复错误
            if (result.message && result.message.includes('交易编号重复')) {
                alert('交易编号重复，系统将自动刷新页面获取新编号');
                // 保留用户已输入的数据
                const formData = {
                    supplier_id: document.getElementById('supplier-id').value,
                    business_date: document.getElementById('business-date').value,
                    notes: document.getElementById('notes').value
                };
                // 将数据存储在sessionStorage中
                sessionStorage.setItem('supplierTransactionFormData', JSON.stringify(formData));
                // 刷新页面
                window.location.reload();
                return;
            }
            
            handleError(new Error(result.message || '发生未知错误'), '保存');
        }
    } catch (error) {
        handleError(error, '保存过程');
    }
});

// 添加缺失的handleReturnSourceChange函数
function handleReturnSourceChange(selectElement) {
    const row = selectElement.closest('tr');
    if (!row) return;

    const wastageLossInput = row.querySelector('[name="wastage_loss[]"]');

    // 损耗计算已简化，不再使用actual_return_weight字段
    if (wastageLossInput) {
        wastageLossInput.value = '0.00';
    }

    // 更新所有计算
    updateCalculations();
}

// 修改handleReturnInputChange函数确保显示2位小数
function handleReturnInputChange(input) {
    const row = input.closest('tr');
    if (!row) return;
    
    const returnWeight = parseFloat(row.querySelector('[name="return_weight[]"]')?.value) || 0;

    // 损耗计算已简化，不再使用actual_return_weight字段
    const wastageLoss = 0;
    const wastageLossInput = row.querySelector('[name="wastage_loss[]"]');
    if (wastageLossInput) {
        wastageLossInput.value = wastageLoss.toFixed(2);
    }
    
    // 更新总计算
    updateCalculations();
}

// 添加损耗计算函数
function updateWastageLoss(element) {
    const row = element.closest('.transaction-row');
    if (!row) return;
    
    // 由于我们已经删除了实际还料重量和损耗字段，这里不再进行相关计算
    // 但保留函数以确保其他地方的调用不会出错
    
    // 触发计算更新
    updateCalculations();
}

// 按指定顺序插入表格
function insertTableInOrder(container, tableHTML, type) {
    // 定义表格类型的顺序：寄料、买料、还料、存料、还款、存款
    const typeOrder = ['deposit_material', 'buy_material', 'return_material', 'store_material', 'return_money', 'deposit_money'];
    const currentTypeIndex = typeOrder.indexOf(type);
    
    if (currentTypeIndex === -1) {
        // 如果类型不在顺序中，直接添加到末尾
        container.insertAdjacentHTML('beforeend', tableHTML);
        return;
    }
    
    // 查找应该插入的位置
    const existingTables = container.querySelectorAll('.transaction-row');
    let insertPosition = null;
    
    for (let i = 0; i < existingTables.length; i++) {
        const existingType = existingTables[i].getAttribute('data-type');
        const existingTypeIndex = typeOrder.indexOf(existingType);
        
        if (existingTypeIndex > currentTypeIndex) {
            insertPosition = existingTables[i];
            break;
        }
    }
    
    if (insertPosition) {
        // 在指定位置之前插入
        insertPosition.insertAdjacentHTML('beforebegin', tableHTML);
    } else {
        // 添加到末尾
        container.insertAdjacentHTML('beforeend', tableHTML);
    }
}

// 检查 addRowByType 函数是否存在，如果不存在，添加它
// 这个函数用于添加不同类型的交易表格行
function addRowByType(type) {
    console.log(`添加行类型: ${type}`);

    // 移除初始占位内容（如果存在）
    const placeholder = document.getElementById('initial-placeholder');
    if (placeholder) {
        placeholder.remove();
    }

    // 检查该类型的表格是否已经存在
    const existingTable = document.querySelector(`.transaction-row[data-type="${type}"]`);

    if (existingTable) {
        // 如果表格已经存在，只需添加一行，不需要重新创建表格
        const tbody = existingTable.querySelector('tbody');
        if (tbody) {
            let rowContent = '';
            switch(type) {
                case 'return_material':
                    rowContent = createReturnMaterialRow();
                    break;
                case 'deposit_material': // <--- 添加寄料 case
                    rowContent = createDepositMaterialRow();
                    break;
                case 'buy_material':
                    rowContent = createBuyMaterialRow();
                    break;
                case 'store_material':
                    rowContent = createStoreMaterialRow();
                    break;
                case 'return_money':
                    rowContent = createReturnMoneyRow();
                    break;
                case 'deposit_money':
                    rowContent = createDepositMoneyRow();
                    break;
            }
            
            if (rowContent) {
                tbody.insertAdjacentHTML('beforeend', rowContent);
            const newRow = tbody.lastElementChild;
                if (newRow) {
                    initializeRowElements(newRow); // Ensure initialization is called
                }
            }
            return; // Exit function after adding row to existing table
        }
    }

    // 如果表格不存在，创建新表格
        const transactionsContainer = document.getElementById('transactions-container');
    if (transactionsContainer) {
        let title = '';
        let theadHtml = '';
        
        // 根据类型设置不同的表头和标题
        switch(type) {
            case 'return_material':
                title = '还料';
                theadHtml = `
                    <tr style="height: 20px;">
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">旧料名称</th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">还料克重</th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">还料来源</th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 16px; font-weight: bold; padding: 2px 1px; border: none;">备注</th>
                    </tr>
                `;
                break;
            case 'deposit_material':
                title = '寄料';
                theadHtml = `
                    <tr style="height: 24px;">
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">旧料名称</th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">寄料克重</th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">实际到料</th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">寄料损耗</th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;"></th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">备注</th>
                    </tr>
                `;
                break;
            case 'buy_material':
                title = '买料';
                theadHtml = `
                    <tr style="height: 24px;">
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">旧料名称</th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">买料克重</th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">料价</th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">总计金额</th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;"></th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">备注</th>
                    </tr>
                `;
                break;
            case 'store_material':
                title = '存料';
                theadHtml = `
                    <tr style="height: 18px;">
                        <th class="header-store-material" style="width: 16.66%;">旧料名称</th>
                        <th class="header-store-material" style="width: 16.66%;">存料克重</th>
                        <th class="header-store-material" style="width: 16.66%;">存料来源</th>
                        <th class="header-store-material" style="width: 16.66%;"></th>
                        <th class="header-store-material" style="width: 16.66%;"></th>
                        <th class="header-store-material" style="width: 16.66%;">备注</th>
                    </tr>
                `;
                break;
            case 'return_money':
                title = '还款';
                theadHtml = `
                    <tr style="height: 18px;">
                        <th class="header-return-payment" style="width: 16.66%;">还款金额</th>
                        <th class="header-return-payment" style="width: 16.66%;">还款来源</th>
                        <th class="header-return-payment" style="width: 16.66%;">还款用途</th>
                        <th class="header-return-payment" style="width: 16.66%;"></th>
                        <th class="header-return-payment" style="width: 16.66%;"></th>
                        <th class="header-return-payment" style="width: 16.66%;">备注</th>
                    </tr>
                `;
                break;
            case 'deposit_money':
                title = '存款';
                theadHtml = `
                    <tr style="height: 18px;">
                        <th class="header-store-payment" style="width: 16.66%;">存款金额</th>
                        <th class="header-store-payment" style="width: 16.66%;">存款来源</th>
                        <th class="header-store-payment" style="width: 16.66%;"></th>
                        <th class="header-store-payment" style="width: 16.66%;"></th>
                        <th class="header-store-payment" style="width: 16.66%;"></th>
                        <th class="header-store-payment" style="width: 16.66%;">备注</th>
                    </tr>
                `;
                break;
        }
        
        // 创建表格行HTML
        const rowHTML = `
        <div class="transaction-row d-flex align-items-center" data-type="${type}" style="margin-bottom: 0 !important;">
                <div style="width: 60px; text-align: left; padding-right: 10px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">${title}</strong></div>
            <div style="width: calc(100% - 70px);">
                    <table class="table table-sm table-bordered">
                        <thead>
                                ${theadHtml}
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
            </div>
            </div>
        `;

        // 添加行HTML - 按指定顺序插入
        insertTableInOrder(transactionsContainer, rowHTML, type);

        // 获取新创建的表格行
        const newTableBody = transactionsContainer.querySelector(`.transaction-row[data-type="${type}"] tbody`);

        // 添加表格行内容
        let rowContent = '';
        switch(type) {
            case 'return_material':
                rowContent = createReturnMaterialRow();
                break;
            case 'deposit_material': // <--- 添加寄料 case
                rowContent = createDepositMaterialRow();
                break;
            case 'buy_material':
                rowContent = createBuyMaterialRow();
                break;
            case 'store_material':
                rowContent = createStoreMaterialRow();
                break;
            case 'return_money':
                rowContent = createReturnMoneyRow();
                break;
            case 'deposit_money':
                rowContent = createDepositMoneyRow();
                break;
        }

        // 添加表格行
        if (newTableBody && rowContent) {
            newTableBody.insertAdjacentHTML('beforeend', rowContent);

            // 初始化新行中的元素
            const newRow = newTableBody.querySelector('tr:last-child');
            if (newRow) {
        initializeRowElements(newRow);
            }
        }
    }
}

// 创建还料行
function createReturnMaterialRow() {
    return `
    <tr style="height: 18px;">
        <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
            <select class="form-control form-control-sm old-material-select" name="return_material_type[]" onchange="updateCalculations()" style="font-size: 14px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                <option value="">请选择</option>
            </select>
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
            <input type="number" class="form-control form-control-sm return-weight" name="return_weight[]" step="0.01" value="" oninput="updateCalculations()" style="font-size: 14px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
            <select class="form-control form-control-sm return-material-source" name="return_material_source[]" onchange="handleReturnSourceChange(this)" style="font-size: 14px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                <option value="">请选择还料来源</option>
            </select>
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
            <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="material_note[]" oninput="updateCalculations()" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)" style="height: 18px; padding: 0 2px; font-size: 10px;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </td>
    </tr>`;
}

// 创建买料行
function createBuyMaterialRow() {
    return `
        <tr style="height: 20px;">
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <select class="form-control form-control-sm old-material-select" name="buy_material_type[]" onchange="updateCalculations()" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                    <option value="">请选择</option>
                </select>
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <input type="number" class="form-control form-control-sm buy-weight" name="buy_weight[]" step="0.01" oninput="calculateBuyTotal(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <input type="number" class="form-control form-control-sm material-price" name="material_price[]" step="0.01" oninput="calculateBuyTotal(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <input type="number" class="form-control form-control-sm buy-total" name="buy_total[]" step="1" oninput="calculateMaterialPrice(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <div class="d-none"></div>
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <div class="d-flex">
                    <input type="text" class="form-control form-control-sm" name="buy_note[]" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                    <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)" style="height: 18px; padding: 0 2px; font-size: 10px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// 创建存料行
function createStoreMaterialRow() {
    return `
    <tr style="height: 22px;">
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm old-material-select" name="store_material_type[]" onchange="updateCalculations()">
                <option value="">请选择</option>
            </select>
        </td>
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm store-weight" name="store_weight[]" step="0.01" value="" oninput="updateCalculations()">
        </td>
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm store-material-source" name="store_material_source[]" onchange="updateCalculations()">
                <option value="">请选择存料来源</option>
            </select>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="material_note[]" oninput="updateCalculations()">
                <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </td>
    </tr>`;
}

// 创建寄料行
function createDepositMaterialRow() {
    return `
    <tr style="height: 22px;">
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm old-material-select" name="deposit_material_type[]" onchange="updateCalculations()">
                <option value="">请选择</option>
            </select>
        </td>
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm deposit-weight" name="deposit_weight[]" step="0.01" value="" oninput="updateDepositLoss(this)">
        </td>
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm actual-deposit-weight" name="actual_deposit_weight[]" step="0.01" value="" oninput="updateDepositLoss(this)">
        </td>
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm deposit-loss" name="deposit_loss[]" step="0.01" value="" readonly>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="material_note[]" oninput="updateCalculations()">
                <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </td>
    </tr>`;
}

// 创建还款行
function createReturnMoneyRow() {
    return `
    <tr style="height: 22px;">
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm return-amount" name="return_amount[]" step="0.01" value="" oninput="updateCalculations()">
            </td>
        <td style="width: 16.66%;">
                <select class="form-control form-control-sm return-money-source" name="return_money_source[]" onchange="updateCalculations()">
                <option value="">请选择还款来源</option>
                </select>
            </td>
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm return-purpose" name="return_purpose[]" onchange="updateCalculations()">
                <option value="还工费">还工费</option>
                <option value="买料">买料</option>
            </select>
        </td>
        <td style="width: 16.66%;"></td>
        <td style="width: 16.66%;"></td>
        <td style="width: 16.66%;">
                <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="money_note[]" oninput="updateCalculations()">
                    <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </td>
    </tr>`;
}

// 创建存款行
function createDepositMoneyRow() {
    return `
    <tr style="height: 22px;">
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm deposit-amount" name="deposit_amount[]" step="0.01" value="" oninput="updateCalculations()">
        </td>
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm deposit-source" name="deposit_source[]" onchange="updateCalculations()">
                <option value="">请选择存款来源</option>
            </select>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="money_note[]" oninput="updateCalculations()">
                <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </td>
    </tr>`;
}

// 初始化行元素函数
function initializeRowElements(row) {
    console.log('初始化行元素');
    if (!row) return;
    
    // 获取行所在的表格类型
    const transactionRow = row.closest('.transaction-row');
    if (!transactionRow) return;
    
    const type = transactionRow.getAttribute('data-type');
    console.log('初始化行类型:', type);
    
    // 初始化旧料选择框
    const oldMaterialSelects = row.querySelectorAll('.old-material-select');
    oldMaterialSelects.forEach(select => {
        // 初始化旧料选择框
        if (window.oldMaterials) {
            // 清空现有选项，保留第一个空选项
            while (select.options.length > 1) {
                select.remove(1);
            }
            
            // 添加旧料类型选项
            window.oldMaterials.forEach(material => {
                const option = document.createElement('option');
                option.value = material.name;
                option.textContent = material.name;
                select.appendChild(option);
            });
        }
    });
    
    // 根据不同行类型初始化特定元素
    if (type === 'return_material') {
        // 初始化还料来源
        const returnMaterialSources = row.querySelectorAll('.return-material-source');
        returnMaterialSources.forEach(select => {
            initializeReturnMaterialSource(select);
        });
    } else if (type === 'store_material') {
        // 初始化存料来源
        const storeMaterialSources = row.querySelectorAll('.store-material-source');
        storeMaterialSources.forEach(select => {
            initializeStoreMaterialSource(select);
        });
    } else if (type === 'return_money') {
        // 初始化还款来源
        const returnMoneySources = row.querySelectorAll('.return-money-source');
        returnMoneySources.forEach(select => {
            initializeReturnMoneySource(select);
        });
        
        // 初始化还款用途
        const returnPurposes = row.querySelectorAll('[name="return_purpose[]"]');
        returnPurposes.forEach(select => {
            initializeReturnMoneyPurpose(select);
        });
    } else if (type === 'deposit_money') {
        // 初始化存款来源
        const depositSources = row.querySelectorAll('[name="deposit_source[]"]');
        depositSources.forEach(select => {
            initializeDepositMoneySource(select);
        });
    } else if (type === 'deposit_material') {
        // 初始化寄料字段事件处理
        console.log('初始化寄料行元素...');
        const depositWeightInputs = row.querySelectorAll('[name="deposit_weight[]"]');
        const actualDepositWeightInputs = row.querySelectorAll('[name="actual_deposit_weight[]"]');
        
        depositWeightInputs.forEach(input => {
            console.log('绑定寄料克重输入事件');
            input.addEventListener('input', function() {
                updateDepositLoss(this);
            });
            // 确保初始值为数字而不是空字符串
            if (input.value === '' || input.value === '0') {
                input.value = '0';
            }
        });
        
        actualDepositWeightInputs.forEach(input => {
            console.log('绑定实际到料输入事件');
            input.addEventListener('input', function() {
                updateDepositLoss(this);
            });
            // 确保初始值为数字而不是空字符串
            if (input.value === '' || input.value === '0') {
                input.value = '0';
            }
        });
        
        // 初始化寄料下拉框（如有）
        const depositMaterialTypes = row.querySelectorAll('[name="deposit_material_type[]"]');
        depositMaterialTypes.forEach(select => {
            // 初始化旧料选择框
            if (window.oldMaterials) {
                updateOldMaterialSelect(select);
            }
        });
        
        // 初次触发计算
        if (depositWeightInputs.length > 0) {
            updateDepositLoss(depositWeightInputs[0]);
        }
    }
}

// 删除表格行函数
function removeTableRow(button) {
    const row = button.closest('tr');
    if (!row) return;

    const tbody = row.closest('tbody');
    if (!tbody) return;

    // 删除行
    row.remove();

    // 如果没有更多行，删除整个表格
    if (tbody.querySelectorAll('tr').length === 0) {
        const transactionContainer = tbody.closest('.transaction-row');
        if (transactionContainer) {
            transactionContainer.remove();
        }
    }

    // 更新计算
    updateCalculations();
}

// 删除整个交易行
function removeTransactionRow(button) {
    const transactionRow = button.closest('.transaction-row');
    if (transactionRow) {
        transactionRow.remove();
        // 更新计算
        updateCalculations();
    }
}

// 添加物料行函数
function addMaterialRow(type) {
    let tableBody;
    let rowHTML;
        
        switch(type) {
        case 'return':
            tableBody = document.querySelector('.transaction-row[data-type="return_material"] tbody');
            rowHTML = createReturnMaterialRow();
                break;
        case 'buy':
            tableBody = document.querySelector('.transaction-row[data-type="buy_material"] tbody');
            rowHTML = createBuyMaterialRow();
                break;
        case 'store':
            tableBody = document.querySelector('.transaction-row[data-type="store_material"] tbody');
            rowHTML = createStoreMaterialRow();
                break;
    }
    
    if (tableBody && rowHTML) {
        tableBody.insertAdjacentHTML('beforeend', rowHTML);
        
        // 初始化新添加行的元素
        const newRow = tableBody.lastElementChild;
        if (newRow) {
        initializeRowElements(newRow);
    }
    
    // 更新计算
        updateCalculations();
    }
}

// 添加款项行函数
function addMoneyRow(type) {
    let tableBody;
    let rowHTML;
    
    switch(type) {
        case 'return':
            tableBody = document.querySelector('.transaction-row[data-type="return_money"] tbody');
            rowHTML = createReturnMoneyRow();
                break;
        case 'deposit':
            tableBody = document.querySelector('.transaction-row[data-type="deposit_money"] tbody');
            rowHTML = createDepositMoneyRow();
                break;
        }
        
    if (tableBody && rowHTML) {
        tableBody.insertAdjacentHTML('beforeend', rowHTML);
        
        // 初始化新添加行的元素
        const newRow = tableBody.lastElementChild;
        if (newRow) {
            initializeRowElements(newRow);
            
            // 特殊处理金钱来源选择框
            if (type === 'return') {
                const returnSource = newRow.querySelector('[name="return_money_source[]"]');
                if (returnSource) {
                    initializeReturnMoneySource(returnSource);
                }
            } else if (type === 'deposit') {
                const depositType = newRow.querySelector('[name="deposit_source[]"]');
                if (depositType) {
                    initializeDepositMoneySource(depositType);
                }
            }
    }
    
    // 更新计算
        updateCalculations();
    }
}

// 还料重量变更处理函数
function handleReturnInputChange(input) {
    // 移除损耗计算逻辑，仅更新总计算
    updateCalculations();
}

// 还料来源变更处理函数
function handleReturnSourceChange(select) {
    // 移除与实际还料和损耗相关的代码，只保留更新总计算
    updateCalculations();
}

// 买料计算函数
function calculateBuyTotal(input) {
    const row = input.closest('tr');
    if (!row) return;

    const weight = parseFloat(row.querySelector('[name="buy_weight[]"]')?.value || 0);
    const price = parseFloat(row.querySelector('[name="material_price[]"]')?.value || 0);

    if (!isNaN(weight) && !isNaN(price)) {
        const total = weight * price;
        const totalInput = row.querySelector('[name="buy_amount[]"]');
        if (totalInput) {
            totalInput.value = total.toFixed(2);
        }
    }

    // 更新总计算
    updateCalculations();
}

// 根据总金额计算单价
function calculateMaterialPrice(input) {
    const row = input.closest('tr');
    if (!row) return;
    
    const total = parseFloat(input.value || 0);
    const weight = parseFloat(row.querySelector('[name="buy_weight[]"]')?.value || 0);
    
    if (!isNaN(total) && !isNaN(weight) && weight > 0) {
        const price = total / weight;
        row.querySelector('[name="material_price[]"]').value = price.toFixed(2);
    }
    
    // 更新总计算
    updateCalculations();
}

// 添加初始化新行元素的函数
function initializeNewRowElements(newRow, type) {
    // 初始化旧料选择框
    const materialSelects = newRow.querySelectorAll('.old-material-select');
    if (materialSelects.length > 0 && window.oldMaterials) {
        updateOldMaterialSelects(materialSelects);
    }
    
    // 初始化事件监听器
    if (type === 'return_material') {
        const returnWeightInput = newRow.querySelector('.return-weight');
        const returnSourceSelect = newRow.querySelector('.return-material-source');

        if (returnWeightInput) {
            returnWeightInput.addEventListener('input', function() { 
                updateCalculations(); 
            });
        }
        
        if (returnSourceSelect) {
            returnSourceSelect.addEventListener('change', function() { 
                handleReturnSourceChange(this); 
            });
        }
        
        // 初始化还料来源选择框
        if (returnSourceSelect) {
            initializeReturnMaterialSource(returnSourceSelect);
        }
    } else if (type === 'buy_material') {
        const buyWeightInput = newRow.querySelector('.buy-weight');
        const materialPriceInput = newRow.querySelector('.material-price');
        
        if (buyWeightInput) {
            buyWeightInput.addEventListener('input', function() {
                updateBuyMaterialTotal(this);
            });
        }
        
        if (materialPriceInput) {
            materialPriceInput.addEventListener('input', function() {
                updateBuyMaterialTotal(this);
            });
        }
    } else if (type === 'store_material') {
        // 初始化存料来源下拉框
        const storeSourceSelect = newRow.querySelector('.store-material-source');
        if (storeSourceSelect) {
            initializeStoreMaterialSource(storeSourceSelect);
        }
    } else if (type === 'return_money') {
        // 初始化还款资金来源下拉框
        const returnMoneySource = newRow.querySelector('.return-money-source');
        if (returnMoneySource && window.accounts) {
            // 清空现有选项，保留第一个空选项
            while (returnMoneySource.options.length > 1) {
                returnMoneySource.remove(1);
            }
            
            // 添加存款抵扣固定选项
            const offsetOption = document.createElement('option');
            offsetOption.value = '存款抵扣';
            offsetOption.textContent = '存款抵扣';
            returnMoneySource.appendChild(offsetOption);
            
            // 添加账户选项
            window.accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.name;
                option.textContent = account.name;
                returnMoneySource.appendChild(option);
            });
        }
    } else if (type === 'deposit_money') {
        // 初始化存款资金来源下拉框
        const depositTypeSelect = newRow.querySelector('[name="deposit_source[]"]');
        if (depositTypeSelect && window.accounts) {
            // 清空现有选项，保留第一个空选项
            while (depositTypeSelect.options.length > 1) {
                depositTypeSelect.remove(1);
            }
            
            // 添加账户选项
            window.accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.name;
                option.textContent = account.name;
                depositTypeSelect.appendChild(option);
            });
        }
    }
}

// 在保存按钮点击事件中添加对供应商信息的更新逻辑
// 此处代码已删除，以防止重复的事件监听器
// document.getElementById('save-btn')?.addEventListener('click', async function() { ... });

// 添加fetchOldMaterials函数实现
function fetchOldMaterials() {
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        // 如果已经有缓存的旧料数据，直接使用
        updateOldMaterialSelects(document.querySelectorAll('.old-material-select'));
        return Promise.resolve(window.oldMaterials);
    }

    return fetch('/api/old_materials')
        .then(response => {
            if (!response.ok) {
                throw new Error('API请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.materials) {
                window.oldMaterials = data.materials;
                // console.log('旧料数据加载成功，共', data.materials.length, '条记录'); // 生产环境中注释掉
                // 更新所有旧料下拉选择框
                updateOldMaterialSelects(document.querySelectorAll('.old-material-select'));
                return data.materials;
            } else {
                throw new Error('获取旧料数据失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('获取旧料数据出错:', error);
            // 使用默认材料列表
            const defaultMaterials = [
                { name: '旧料足金' },
                { name: '旧料18K金' },
                { name: '旧料22K金' },
                { name: '旧料铂金' },
                { name: '旧料银' }
            ];
            window.oldMaterials = defaultMaterials;
            updateOldMaterialSelects(document.querySelectorAll('.old-material-select'));
            return defaultMaterials;
        });
}

// 更新旧料选择框的函数
function updateOldMaterialSelects(selects) {
    if (!window.oldMaterials || !window.oldMaterials.length) return;

    selects.forEach(select => {
        const currentValue = select.value;
        // 检查是否有data-original-value属性（编辑模式下设置的原始值）
        const originalValue = select.getAttribute('data-original-value');

        // 如果已经有正确的原始值，并且当前值已经正确，跳过更新
        if (originalValue && currentValue === originalValue) {
            console.log(`跳过旧料选择框更新，已正确设置为: ${originalValue}`);
            return;
        }

        // 保留第一个空选项
        while (select.options.length > 1) {
            select.remove(1);
        }

        let hasSelected = false;
        const targetValue = originalValue || currentValue;

        window.oldMaterials.forEach(material => {
            const option = document.createElement('option');
            option.value = material.name;
            option.textContent = material.name;
            // 优先使用原始值，其次是当前值，最后才是默认值
            if (material.name === targetValue) {
                option.selected = true;
                hasSelected = true;
                console.log(`设置旧料选择框为: ${material.name} (来源: ${originalValue ? '原始值' : '当前值'})`);
            } else if (!targetValue && material.name === '旧料足金') {
                // 只有在没有任何值时才默认选择旧料足金
                option.selected = true;
                hasSelected = true;
                console.log(`设置旧料选择框为默认值: 旧料足金`);
            }
            select.appendChild(option);
        });

        // 如果没有选中项，选择第一个（空白选项）
        if (!hasSelected && select.options.length > 0) {
            select.options[0].selected = true;
        }

        // 强制设置value，确保选择框的值正确
        if (targetValue) {
            select.value = targetValue;
            console.log(`强制设置旧料选择框值为: ${select.value}`);
        }
    });
}

// 买料计算函数
function calculateBuyTotal(input) {
    const row = input.closest('tr');
    if (!row) return;

    const weight = parseFloat(row.querySelector('[name="buy_weight[]"]')?.value || 0);
    const price = parseFloat(row.querySelector('[name="material_price[]"]')?.value || 0);

    if (!isNaN(weight) && !isNaN(price)) {
        const total = weight * price;
        row.querySelector('[name="buy_total[]"]').value = total.toFixed(2);
    }

    // 更新总计算
    updateCalculations();
}

// 根据总金额计算单价
function calculateMaterialPrice(input) {
    const row = input.closest('tr');
    if (!row) return;

    const total = parseFloat(input.value || 0);
    const weight = parseFloat(row.querySelector('[name="buy_weight[]"]')?.value || 0);

    if (!isNaN(total) && !isNaN(weight) && weight > 0) {
        const price = total / weight;
        row.querySelector('[name="material_price[]"]').value = price.toFixed(2);
    }

    // 更新总计算
    updateCalculations();
}

// 更新单个旧料选择框的函数
function updateOldMaterialSelect(select, selectedValue = null) {
    if (!window.oldMaterials || !window.oldMaterials.length || !select) return;

    const currentValue = selectedValue || select.value;
    // 保留第一个空选项
    while (select.options.length > 1) {
        select.remove(1);
    }

    let hasSelected = false;
    window.oldMaterials.forEach(material => {
        const option = document.createElement('option');
        option.value = material.name;
        option.textContent = material.name;
        // 如果是当前值或者是'旧料足金'且没有当前值，则选中
        if (material.name === currentValue || (material.name === '旧料足金' && !currentValue)) {
            option.selected = true;
            hasSelected = true;
        }
        select.appendChild(option);
    });

    // 如果没有选中项，选择第一个（空白选项）
    if (!hasSelected && select.options.length > 0) {
        select.options[0].selected = true;
    }
}

// 获取账户列表函数
function fetchAccounts() {
    if (window.accounts && window.accounts.length > 0) {
        // 如果已有缓存的账户数据，直接使用
        updateAccountSelects();
        return Promise.resolve(window.accounts);
    }

    return fetch('/api/accounts')
        .then(response => {
            if (!response.ok) {
                throw new Error('API请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                window.accounts = data.data;
                // console.log('账户数据加载成功，共', data.data.length, '条记录'); // 生产环境中注释掉
                // 更新所有账户下拉选择框
                updateAccountSelects();
                return data.data;
            } else {
                throw new Error('获取账户数据失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('获取账户数据出错:', error);
            // 不使用模拟数据，保持空数组
            window.accounts = [];
            console.warn('账户数据获取失败，请检查API接口');
            return window.accounts;
        });
}

// 更新账户选择框函数
function updateAccountSelects() {
    // 不再使用硬编码的默认账户，只使用从数据库获取的账户
    if (!window.accounts || window.accounts.length === 0) {
        console.warn('没有找到账户数据，下拉框将为空');
        return;
    }
    
    // 分别处理还款和存款的资金来源选择框
    const returnMoneySelects = document.querySelectorAll('.return-money-source');
    const depositTypeSelects = document.querySelectorAll('[name="deposit_source[]"]');
    
    // 更新还款的资金来源下拉框（包含存款抵扣选项）
    returnMoneySelects.forEach(select => {
        const currentValue = select.value;
        // 清空现有选项，保留第一个空选项
        while (select.options.length > 1) {
            select.remove(1);
        }
        
        // 添加存款抵扣固定选项
        const offsetOption = document.createElement('option');
        offsetOption.value = '存款抵扣';
        offsetOption.textContent = '存款抵扣';
        select.appendChild(offsetOption);
        
        // 只添加账户信息表中的账户选项
        window.accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.name;
            option.textContent = account.name;
            select.appendChild(option);
        });
        
        // 如果有之前选中的值，恢复选中状态
        if (currentValue) {
            for (let i = 0; i < select.options.length; i++) {
                if (select.options[i].value === currentValue) {
                     select.options[i].selected = true;
                     break;
                 }
             }
        }
    });
    
    // 更新存款的资金来源下拉框（不包含存款抵扣选项）
    depositTypeSelects.forEach(select => {
        const currentValue = select.value;
        // 清空现有选项，保留第一个空选项
        while (select.options.length > 1) {
            select.remove(1);
        }
        
        // 只添加账户信息表中的账户选项
        window.accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.name;
            option.textContent = account.name;
            select.appendChild(option);
        });
        
        // 如果有之前选中的值，恢复选中状态
        if (currentValue) {
            for (let i = 0; i < select.options.length; i++) {
                if (select.options[i].value === currentValue) {
                    select.options[i].selected = true;
                    break;
                }
            }
        }
    });
}

// 添加获取金料供应商的函数
function fetchGoldSuppliers() {
    if (window.goldSuppliers && window.goldSuppliers.length > 0) {
        // 如果已有缓存数据，直接使用
        updateGoldSupplierSelects();
        updateStoreMaterialSelects();
        return Promise.resolve(window.goldSuppliers);
    }

    // 尝试通过特定API获取金料供应商
    return fetch('/api/suppliers?type=gold_material', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('金料供应商API不可用，状态码: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success' && data.data && data.data.length > 0) {
            window.goldSuppliers = data.data;
            // console.log('金料供应商数据加载成功，共', data.data.length, '条记录'); // 生产环境中注释掉
            // 更新所有来源选择框
            updateGoldSupplierSelects();
            updateStoreMaterialSelects();
            return data.data;
        } else {
            throw new Error('没有找到金料供应商');
        }
    })
    .catch(error => {
        console.warn('金料供应商API请求失败:', error);
        // 尝试备用API - 获取所有供应商然后筛选
        return fetchAllSuppliersAndFilter();
    });
}

// 从所有供应商中筛选金料供应商
function fetchAllSuppliersAndFilter() {
    console.log('尝试从所有供应商中筛选金料供应商...');
    
    try {
        fetch('/api/suppliers', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                console.warn('备用API也不可用，状态码:', response.status);
                return Promise.reject('备用API不可用');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success' && data.data && data.data.length > 0) {
                // 筛选supplier_type为'gold'或'gold_material'的供应商
                window.goldSuppliers = data.data.filter(supplier => 
                    supplier.supplier_type === 'gold_material' ||
                    supplier.supplier_type === 'gold' ||
                    supplier.supplier_type === 'material' ||
                    supplier.name.includes('金') ||
                    supplier.name.includes('料')
                );
                
                if (window.goldSuppliers.length === 0) {
                    console.warn('没有找到匹配的金料供应商，使用所有供应商');
                    window.goldSuppliers = data.data;
                }
                
                console.log('从所有供应商中筛选金料供应商成功:', window.goldSuppliers.length);
                updateGoldSupplierSelects();
                updateStoreMaterialSelects();
                return;
            } else {
                return Promise.reject('没有找到供应商数据');
            }
        })
        .catch(error => {
            console.warn('备用API请求失败:', error);
            // 最后使用从页面获取供应商的方法
            fetchSuppliersFromPage();
        });
    } catch (error) {
        console.error('获取所有供应商失败:', error);
        // 出错时尝试从页面获取
        fetchSuppliersFromPage();
    }
}

// 从页面中获取供应商数据
function fetchSuppliersFromPage() {
    console.log('尝试从页面获取供应商数据...');
    
    try {
        // 从下拉列表中获取供应商数据
        const supplierSelect = document.getElementById('supplier-id');
        if (supplierSelect) {
            const suppliers = [];
            
            // 从下拉列表中提取所有供应商
            Array.from(supplierSelect.options).forEach(option => {
                if (option.value && option.text) {
                    try {
                        // 尝试解析data-info属性
                        const supplierData = option.dataset.info ? JSON.parse(option.dataset.info) : null;
                        suppliers.push({
                            id: option.value,
                            name: option.text,
                            ...supplierData
                        });
                    } catch (e) {
                        // 如果JSON解析失败，只使用基本信息
                        suppliers.push({
                            id: option.value,
                            name: option.text
                        });
                    }
                }
            });
            
            // 筛选出可能是金料供应商的选项
            window.goldSuppliers = suppliers.filter(supplier => 
                supplier.supplier_type === 'material' || 
                supplier.name.includes('金') || 
                supplier.name.includes('料')
            );
            
            // 如果过滤后没有供应商，使用所有供应商
            if (window.goldSuppliers.length === 0) {
                console.warn('从页面没有找到匹配的金料供应商，使用所有供应商');
                window.goldSuppliers = suppliers;
            }
            
            if (window.goldSuppliers.length === 0) {
                // 如果仍然没有数据，使用默认值
                useDefaultSuppliers();
            } else {
                console.log('从页面获取金料供应商数据成功:', window.goldSuppliers.length);
                updateGoldSupplierSelects();
                updateStoreMaterialSelects();
            return;
        }
        } else {
            console.warn('页面中没有找到供应商下拉列表');
            useDefaultSuppliers();
        }
    } catch (error) {
        console.error('从页面获取供应商数据失败:', error);
        useDefaultSuppliers();
    }
}

// 不再使用默认供应商，改为显示错误信息
function useDefaultSuppliers() {
    console.error('金料供应商数据获取失败，请检查API接口');
    window.goldSuppliers = [];
    console.warn('金料供应商数据为空，相关下拉框将无选项');
}

// 更新旧料来源选择框
function updateMaterialSourceSelects() {
    // 更新还料和存料来源选择框
    updateGoldSupplierSelects();
    updateStoreMaterialSelects();
    
    console.log('已更新所有材料来源选择框');
}

// 初始化页面基本功能
function initializeBasicFeatures() {
    // 获取页面元素
    const supplierSelect = document.getElementById('supplier-id');
    const businessDateInput = document.getElementById('business-date');
    const transactionNoInput = document.getElementById('transaction-no');

    // 检查是否为编辑模式
    const isEditMode = {{ 'true' if edit_mode else 'false' }};

    // 更新业务日期为当前日期时间（仅在新增模式下且为空时）
    if (businessDateInput && !businessDateInput.value && !isEditMode) {
        // 设置下拉选择框
        if (returnMaterialType && item.return_material_type) {
            console.log(`设置还料的旧料类型: ${item.return_material_type}`);
            returnMaterialType.setAttribute('data-original-value', item.return_material_type);
            // 检查数据是否已加载，如果已加载则立即设置
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                updateOldMaterialSelect(returnMaterialType, item.return_material_type);
                returnMaterialType.value = item.return_material_type;
                console.log(`还料旧料类型设置完成: ${returnMaterialType.value}`);
            } else {
                updateOldMaterialSelect(returnMaterialType, item.return_material_type);
                returnMaterialType.value = item.return_material_type;
            }
        }
        
        if (returnSource && item.return_source) {
                        console.log('设置还料来源:', item.return_source);
                        // 确保下拉选项已加载
            initializeReturnMaterialSource(returnSource);
                        // 设置选中值
            returnSource.value = item.return_source;
        }
                    
                    // 初始化新行的事件处理
                    initializeRowElements(newRow);
                }
            });
        } else {
            console.error('未找到还料表格元素');
        }
    }
    
    // 创建买料表格（如果有数据）
    if (buyMaterialItems.length > 0) {
        console.log('开始创建买料表格');
        // 添加买料表格
        addRowByType('buy_material');
        
        // 获取新创建的表格行
        const buyMaterialTable = document.querySelector('.transaction-row[data-type="buy_material"] tbody');
        if (buyMaterialTable) {
            // 清空默认创建的行
            buyMaterialTable.innerHTML = '';
            
            // 添加数据行
            buyMaterialItems.forEach(item => {
                const rowHTML = createBuyMaterialRow();
                buyMaterialTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // 获取新行并填充数据
                const newRow = buyMaterialTable.lastElementChild;
                if (newRow) {
                    // 设置值
                    const materialType = newRow.querySelector('[name="buy_material_type[]"]');
                    const buyWeight = newRow.querySelector('[name="buy_weight[]"]');
                    const materialPrice = newRow.querySelector('[name="material_price[]"]');
                    const buyTotal = newRow.querySelector('[name="buy_total[]"]');
                    const note = newRow.querySelector('[name="buy_note[]"]');

        if (buyWeight) buyWeight.value = item.buy_weight || '';
        if (materialPrice) materialPrice.value = item.material_price || '';
        if (buyTotal) buyTotal.value = item.buy_total || item.return_amount || '';
        if (note) note.value = item.note || '';
        
                    // 设置下拉选择框
        if (materialType && item.material_type) {
            console.log(`设置买料的旧料类型: ${item.material_type}`);
            materialType.setAttribute('data-original-value', item.material_type);
            // 检查数据是否已加载，如果已加载则立即设置
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                updateOldMaterialSelect(materialType, item.material_type);
                materialType.value = item.material_type;
                console.log(`买料旧料类型设置完成: ${materialType.value}`);
            } else {
                updateOldMaterialSelect(materialType, item.material_type);
                materialType.value = item.material_type;
            }
        }
                    
                    // 初始化新行的事件处理
                    initializeRowElements(newRow);
            }
        });
    } else {
            console.error('未找到买料表格元素');
        }
    }
    
    // 创建寄料表格（仅当有实际寄料数据时）
    if (depositMaterialItems.length > 0) {
        // 检查是否有真实数据，而不仅仅是空字段
        const hasRealDepositData = depositMaterialItems.some(item => 
            parseFloat(item.deposit_weight || 0) > 0 || 
            parseFloat(item.actual_deposit_weight || 0) > 0
        );
        
        if (hasRealDepositData) {
            console.log('开始创建寄料表格（有实际数据）');
        // 添加寄料表格
        addRowByType('deposit_material');
        
        // 获取新创建的表格行
        const depositMaterialTable = document.querySelector('.transaction-row[data-type="deposit_material"] tbody');
        if (depositMaterialTable) {
            // 清空默认创建的行
            depositMaterialTable.innerHTML = '';
            
            // 添加数据行
            depositMaterialItems.forEach(item => {
                const rowHTML = createDepositMaterialRow();
                depositMaterialTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // 获取新行并填充数据
                const newRow = depositMaterialTable.lastElementChild;
                if (newRow) {
                    // 设置值
                    const depositMaterialType = newRow.querySelector('[name="deposit_material_type[]"]');
                    const depositWeight = newRow.querySelector('[name="deposit_weight[]"]');
                    const actualDepositWeight = newRow.querySelector('[name="actual_deposit_weight[]"]');
                    const depositLoss = newRow.querySelector('[name="deposit_loss[]"]');
                    const note = newRow.querySelector('[name="material_note[]"]');
        
                    // 确保转换为数值
        const depositWeightValue = parseFloat(item.deposit_weight || 0);
        const actualDepositWeightValue = parseFloat(item.actual_deposit_weight || 0);
        const depositLossValue = parseFloat(item.deposit_loss || 0);
        
                    // 显式设置为字符串形式的数值
        if (depositWeight) depositWeight.value = depositWeightValue.toString();
        if (actualDepositWeight) actualDepositWeight.value = actualDepositWeightValue.toString();
        if (depositLoss) depositLoss.value = depositLossValue.toString();
        if (note) note.value = item.note || '';
        
                    // 设置下拉选择框
        if (depositMaterialType && item.deposit_material_type) {
            console.log(`设置寄料的旧料类型: ${item.deposit_material_type}`);
            depositMaterialType.setAttribute('data-original-value', item.deposit_material_type);
            // 检查数据是否已加载，如果已加载则立即设置
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                updateOldMaterialSelect(depositMaterialType, item.deposit_material_type);
                depositMaterialType.value = item.deposit_material_type;
                console.log(`寄料旧料类型设置完成: ${depositMaterialType.value}`);
            } else {
                updateOldMaterialSelect(depositMaterialType, item.deposit_material_type);
                depositMaterialType.value = item.deposit_material_type;
            }
        }
                    
                    // 初始化新行的事件处理
                    initializeRowElements(newRow);
                }
            });
        } else {
            console.error('未找到寄料表格元素');
        }
        } else {
            console.log('寄料数据不包含实际数值，不创建寄料表格');
        }
    } else {
        console.log('没有寄料数据，不创建寄料表格');
    }
    
    // 创建存料表格（如果有数据）
    if (storeMaterialItems.length > 0) {
        console.log('开始创建存料表格，共有 ' + storeMaterialItems.length + ' 项数据');
        
        // 确保金料供应商数据已加载
        if (!window.goldSuppliers || window.goldSuppliers.length === 0) {
            console.log('存料表格需要金料供应商数据，但数据尚未加载');
            // 使用默认供应商数据
            useDefaultSuppliers();
        }
        
        // 添加存料表格
        addRowByType('store_material');
        
        // 获取新创建的表格行
        const storeMaterialTable = document.querySelector('.transaction-row[data-type="store_material"] tbody');
        if (storeMaterialTable) {
            // 清空默认创建的行
            storeMaterialTable.innerHTML = '';
            
            // 添加数据行
            storeMaterialItems.forEach((item, index) => {
                const rowHTML = createStoreMaterialRow();
                storeMaterialTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // 获取新行并填充数据
                const newRow = storeMaterialTable.lastElementChild;
                if (newRow) {
                    // 获取表单元素
                    const storeMaterialType = newRow.querySelector('[name="store_material_type[]"]');
                    const storeSource = newRow.querySelector('[name="store_material_source[]"]');
                    const storeWeight = newRow.querySelector('[name="store_weight[]"]');
                    const note = newRow.querySelector('[name="material_note[]"]');
        
                    console.log(`处理存料项 #${index + 1}:`, item);
        
                    // 先初始化旧料选择框
        if (storeMaterialType && window.oldMaterials) {
            const materialType = item.store_material_type || item.material_type || '';
            console.log(`设置存料的旧料类型: ${materialType}`);
            storeMaterialType.setAttribute('data-original-value', materialType);
            // 检查数据是否已加载，如果已加载则立即设置
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                updateOldMaterialSelect(storeMaterialType, materialType);
                storeMaterialType.value = materialType;
                console.log(`存料旧料类型设置完成: ${storeMaterialType.value}`);
                // 重要：设置完旧料类型后，重新计算本期数据
                updateCalculations();
                console.log(`存料旧料类型设置后重新计算完成`);
            } else {
                updateOldMaterialSelect(storeMaterialType, materialType);
                storeMaterialType.value = materialType;
                updateCalculations();
            }
        }
        
                    // 专门处理存料来源
        if (storeSource) {
                        console.log(`初始化存料来源选择框，当前项来源:`, item.store_source || item.material_source || '');
                        
                        // 确保下拉选项已加载
            initializeStoreMaterialSource(storeSource);
            
                        // 设置选中值
                        if (item.store_source || item.material_source) {
                            const sourceValue = item.store_source || item.material_source;
                            
                // 检查选项是否存在
                let sourceExists = false;
                for (let i = 0; i < storeSource.options.length; i++) {
                    if (storeSource.options[i].value === sourceValue) {
                        storeSource.selectedIndex = i;
                        sourceExists = true;
                                    console.log(`找到并选中存料来源: ${sourceValue}`);
                        break;
                    }
                }
                
                // 如果选项不存在，添加它
                if (!sourceExists && sourceValue !== '') {
                    const option = document.createElement('option');
                    option.value = sourceValue;
                    option.textContent = sourceValue;
                    storeSource.appendChild(option);
                    storeSource.value = sourceValue;
                                console.log(`添加并选中存料来源: ${sourceValue}`);
            }
        }
    }
    
                    // 设置其他值
                    if (storeWeight) storeWeight.value = item.store_weight || '';
                    if (note) note.value = item.note || '';
                }
            });
            
            // 立即更新所有下拉框
            const storeSourceSelects = storeMaterialTable.querySelectorAll('[name="store_material_source[]"]');
            console.log(`更新 ${storeSourceSelects.length} 个存料来源选择框`);
            storeSourceSelects.forEach(select => initializeStoreMaterialSource(select));
        } else {
            console.error('未找到存料表格元素');
        }
    }
    
    // 创建还款表格（如果有数据）
    if (returnMoneyItems.length > 0) {
        console.log('开始创建还款表格');
        // 添加还款表格
        addRowByType('return_money');
        
        // 获取新创建的表格行
        const returnMoneyTable = document.querySelector('.transaction-row[data-type="return_money"] tbody');
        if (returnMoneyTable) {
            // 清空默认创建的行
            returnMoneyTable.innerHTML = '';
            
            // 添加数据行
            returnMoneyItems.forEach(item => {
                const rowHTML = createReturnMoneyRow();
                returnMoneyTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // 获取新行并填充数据
                const newRow = returnMoneyTable.lastElementChild;
                if (newRow) {
                    // 设置值
                    const returnAmount = newRow.querySelector('[name="return_amount[]"]');
                    const returnSource = newRow.querySelector('[name="return_money_source[]"]');
                    const returnPurpose = newRow.querySelector('[name="return_purpose[]"]');
                    const note = newRow.querySelector('[name="money_note[]"]');
        
                    // 初始化下拉框
                    if (returnSource) {
                        // 确保下拉选项已加载
                        initializeReturnMoneySource(returnSource);
                    }
                    
                    if (returnPurpose) {
                        // 确保下拉选项已加载
                        initializeReturnMoneyPurpose(returnPurpose);
                    }
                    
                    // 设置值
        if (returnAmount) returnAmount.value = item.return_amount || '';
        if (note) note.value = item.note || '';
        
                    // 设置下拉选择框
        if (returnSource && item.return_source) {
            console.log('设置还款来源:', item.return_source);
            // 检查是否有对应选项
            let hasOption = false;
            for (let i = 0; i < returnSource.options.length; i++) {
                if (returnSource.options[i].value === item.return_source) {
                    hasOption = true;
                    break;
                }
            }

            // 如果没有找到选项，添加一个
            if (!hasOption) {
                const option = document.createElement('option');
                option.value = item.return_source;
                option.textContent = item.return_source;
                returnSource.appendChild(option);
                console.log(`为还款来源添加了未知值: ${item.return_source}`);
            }

            // 设置选中值
            returnSource.value = item.return_source;
        }
        
        if (returnPurpose && item.return_purpose) {
            // 检查是否有对应选项
            let hasOption = false;
            for (let i = 0; i < returnPurpose.options.length; i++) {
                if (returnPurpose.options[i].value === item.return_purpose) {
                    hasOption = true;
                    break;
                }
            }

            // 如果没有找到选项，添加一个
            if (!hasOption) {
                const option = document.createElement('option');
                option.value = item.return_purpose;
                option.textContent = item.return_purpose;
                returnPurpose.appendChild(option);
                console.log(`为还款用途添加了未知值: ${item.return_purpose}`);
            }

            // 设置选中值
            returnPurpose.value = item.return_purpose;
        }
    }
            });
        } else {
            console.error('未找到还款表格元素');
        }
    }
    
    // 创建存款表格（如果有数据）
    if (depositMoneyItems.length > 0) {
        console.log('开始创建存款表格');
        // 添加存款表格
        addRowByType('deposit_money');
        
        // 获取新创建的表格行
        const depositMoneyTable = document.querySelector('.transaction-row[data-type="deposit_money"] tbody');
        if (depositMoneyTable) {
            // 清空默认创建的行
            depositMoneyTable.innerHTML = '';
            
            // 添加数据行
            depositMoneyItems.forEach(item => {
                const rowHTML = createDepositMoneyRow();
                depositMoneyTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // 获取新行并填充数据
                const newRow = depositMoneyTable.lastElementChild;
                if (newRow) {
                    // 设置值
                    const storeAmount = newRow.querySelector('[name="deposit_amount[]"]');
                    const depositType = newRow.querySelector('[name="deposit_source[]"]');
                    const note = newRow.querySelector('[name="money_note[]"]');
        
                    // 初始化下拉框
                    if (depositType) {
                        // 确保下拉选项已加载
                        initializeDepositMoneySource(depositType);
                    }
                    
                    // 设置值
        if (storeAmount) storeAmount.value = item.store_amount || '';
        if (note) note.value = item.note || '';
        
                    // 设置下拉选择框
        if (depositType && item.store_source) {
            console.log('设置存款来源:', item.store_source);
                            depositType.value = item.store_source;
                        }
            }
        });
    } else {
            console.error('未找到存款表格元素');
                    }
                }
                
    // 只执行一次计算和一次选择框更新
    console.log('编辑数据加载完成，执行最终更新...');

    // 隐藏加载状态，显示最终内容
    const loadingPlaceholder = document.getElementById('loading-placeholder');
    if (loadingPlaceholder) {
        loadingPlaceholder.style.display = 'none';
    }

    // 防止中间刷新闪烁，添加初始化标记
    window.isLoadingEditData = true;

    // 由于数据已经在加载过程中正确设置，延迟执行最终计算
    setTimeout(() => {
        window.isLoadingEditData = false;
        // 只有在有交易数据时才需要计算
        if (document.querySelectorAll('.transaction-row tbody tr').length > 0) {
            updateCalculations();
        }
    }, 100);

    if (!window.allSelectsUpdated) {
        window.allSelectsUpdated = true;
        console.log('执行选择框最终更新');
        updateAllSelects();
    }
    } catch (error) {
        handleError(error, '加载编辑数据');

        // 尝试基本的错误恢复
        try {
            // 至少确保基本的UI元素可用
            updateCalculations();
        } catch (recoveryError) {
            console.error('错误恢复失败:', recoveryError);
        }
    }
}

// 更新所有选择框函数优化
function updateAllSelects() {
    console.log('更新所有下拉选择框...');
    
    // 防止重复更新
    if (window.allSelectsUpdated) {
        console.log('所有选择框已经更新过，跳过重复更新');
        return;
    }
    window.allSelectsUpdated = true;
    
    // 更新旧料选择框
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        const oldMaterialSelects = document.querySelectorAll('.old-material-select');
        oldMaterialSelects.forEach(select => {
            // 保存当前值
            const currentValue = select.value;
            
            // 清空选项保留第一个
            while (select.options.length > 1) {
                select.remove(1);
            }
            
            // 添加旧料选项
            window.oldMaterials.forEach(material => {
                const option = document.createElement('option');
                option.value = material.name;
                option.textContent = material.name;
                select.appendChild(option);
            });
            
            // 恢复选中值
            if (currentValue) {
                select.value = currentValue;
            }
        });
        // console.log(`已更新 ${oldMaterialSelects.length} 个旧料选择框`); // 生产环境中注释掉
    }

    // 更新还料来源选择框（仅在有元素时）
    const returnMaterialSources = document.querySelectorAll('.return-material-source');
    if (returnMaterialSources.length > 0) {
        returnMaterialSources.forEach(select => {
            initializeReturnMaterialSource(select);
        });
        // console.log(`已更新 ${returnMaterialSources.length} 个还料来源选择框`); // 生产环境中注释掉
    }

    // 更新存料来源选择框（仅在有元素时）
    const storeMaterialSources = document.querySelectorAll('.store-material-source');
    if (storeMaterialSources.length > 0) {
        storeMaterialSources.forEach(select => {
            initializeStoreMaterialSource(select);
        });
        // console.log(`已更新 ${storeMaterialSources.length} 个存料来源选择框`); // 生产环境中注释掉
    }

    // 更新还款来源选择框（仅在有元素时）
    const returnMoneySources = document.querySelectorAll('.return-money-source');
    if (returnMoneySources.length > 0) {
        returnMoneySources.forEach(select => {
            initializeReturnMoneySource(select);
        });
        // console.log(`已更新 ${returnMoneySources.length} 个还款来源选择框`); // 生产环境中注释掉
    }

    // 更新存款来源选择框（仅在有元素时）
    const depositSources = document.querySelectorAll('[name="deposit_source[]"]');
    if (depositSources.length > 0) {
        depositSources.forEach(select => {
            initializeDepositMoneySource(select);
        });
        // console.log(`已更新 ${depositSources.length} 个存款来源选择框`); // 生产环境中注释掉
    }

    // 更新还款用途选择框（仅在有元素时）
    const returnPurposes = document.querySelectorAll('[name="return_purpose[]"]');
    if (returnPurposes.length > 0) {
        returnPurposes.forEach(select => {
            initializeReturnMoneyPurpose(select);
        });
        // console.log(`已更新 ${returnPurposes.length} 个还款用途选择框`); // 生产环境中注释掉
    }
}

// 添加物料行
function addMaterialRow() {
    const materialTable = document.getElementById('material-table').querySelector('tbody');
    if (!materialTable) return;
    
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>
            <input type="number" class="form-control form-control-sm return-weight" name="return_weight[]" step="0.01" value="" oninput="handleReturnInputChange(this)">
        </td>
        <td>
            <select class="form-control form-control-sm old-material-select" name="return_material_type[]" onchange="updateCalculations()">
                <option value="">请选择</option>
            </select>
        </td>
        <td>
            <select class="form-control form-control-sm return-material-source" name="return_material_source[]" onchange="handleReturnSourceChange(this)">
                <option value="">请选择</option>
                <option value="料部库存">料部库存</option>
                <option value="存料抵扣">存料抵扣</option>
            </select>
        </td>
        <td>
            <!-- actual_return_weight字段已移除 -->
        </td>
        <td>
            <input type="number" class="form-control form-control-sm wastage-loss" name="wastage_loss[]" step="0.01" value="" readonly>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm store-weight" name="store_weight[]" step="0.01" value="" oninput="updateCalculations()">
        </td>
        <td>
            <select class="form-control form-control-sm old-material-select" name="store_material_type[]" onchange="updateCalculations()">
                <option value="">请选择</option>
            </select>
        </td>
        <td>
            <select class="form-control form-control-sm store-material-source" name="store_material_source[]" onchange="updateCalculations()">
                <option value="">请选择</option>
                <option value="料部库存">料部库存</option>
                <option value="存料抵扣">存料抵扣</option>
            </select>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;
    materialTable.appendChild(newRow);
    
    // 更新旧料选择框
    const materialSelects = newRow.querySelectorAll('.old-material-select');
    updateOldMaterialSelects(materialSelects);
    
    // 更新旧料来源选择框
    const sourceSelects = newRow.querySelectorAll('.return-material-source, .store-material-source');
    sourceSelects.forEach(select => {
        if (select.classList.contains('return-material-source')) {
            initializeReturnMaterialSource(select);
        } else if (select.classList.contains('store-material-source')) {
            initializeStoreMaterialSource(select);
        }
    });
    
    return newRow;
}

// 添加款项行
function addMoneyRow() {
    const moneyTable = document.getElementById('money-table').querySelector('tbody');
    if (!moneyTable) return;
    
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>
            <input type="number" class="form-control form-control-sm return-amount" name="return_amount[]" step="1" value="" oninput="updateCalculations()">
        </td>
        <td>
            <select class="form-control form-control-sm return-money-source" name="return_money_source[]" onchange="updateCalculations()">
                <option value="">请选择</option>
            </select>
        </td>
        <td>
            <select class="form-control form-control-sm" name="return_purpose[]" onchange="updateCalculations()">
                <option value="还工费">还工费</option>
                <option value="买料">买料</option>
            </select>
        </td>
        <td></td>
        <td></td>
        <td>
            <input type="number" class="form-control form-control-sm store-amount" name="store_amount[]" step="1" value="" oninput="updateCalculations()">
        </td>
        <td>
            <select class="form-control form-control-sm" name="store_money_source[]" onchange="updateCalculations()">
                <option value="">请选择</option>
                <option value="现金">现金</option>
                <option value="银行转账">银行转账</option>
                <option value="支付宝">支付宝</option>
                <option value="微信">微信</option>
            </select>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeMoneyRow(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;
    moneyTable.appendChild(newRow);
    
    // 更新旧料选择框
    const materialSelects = newRow.querySelectorAll('.old-material-select');
    updateOldMaterialSelects(materialSelects);
    
    // 更新资金来源选择框
    const moneySourceSelect = newRow.querySelector('.return-money-source');
    if (moneySourceSelect) {
        // 添加存款抵扣选项
        const offsetOption = document.createElement('option');
        offsetOption.value = '存款抵扣';
        offsetOption.textContent = '存款抵扣';
        moneySourceSelect.appendChild(offsetOption);
        
        // 添加常用账户选项
        ['现金', '银行转账', '支付宝', '微信'].forEach(account => {
            const option = document.createElement('option');
            option.value = account;
            option.textContent = account;
            moneySourceSelect.appendChild(option);
        });
    }
    
    // 更新存款来源选择框
    const storeMoneySourceSelect = newRow.querySelector('[name="store_money_source[]"]');
    if (storeMoneySourceSelect) {
        // 添加常用账户选项
        ['现金', '银行转账', '支付宝', '微信'].forEach(account => {
            const option = document.createElement('option');
            option.value = account;
            option.textContent = account;
            storeMoneySourceSelect.appendChild(option);
        });
    }
    
    return newRow;
}

// 移除物料行
function removeMaterialRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
    updateCalculations();
    }
}

// 移除款项行
function removeMoneyRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
        updateCalculations();
    }
}

// 删除切换买料字段的逻辑

// 添加编辑模式数据加载函数

// 添加updateDepositLoss函数，计算寄料损耗
function updateDepositLoss(input) {
    const row = input.closest('tr');
    if (!row) {
        console.error('updateDepositLoss: 找不到父行元素');
        return;
    }
    
    // 获取输入字段并进行显式转换
    const depositWeightInput = row.querySelector('[name="deposit_weight[]"]');
    const actualDepositWeightInput = row.querySelector('[name="actual_deposit_weight[]"]');
    const depositLossInput = row.querySelector('[name="deposit_loss[]"]');
    
    if (!depositWeightInput || !actualDepositWeightInput || !depositLossInput) {
        console.error('updateDepositLoss: 找不到必要的输入字段', {
            depositWeightInput: !!depositWeightInput,
            actualDepositWeightInput: !!actualDepositWeightInput,
            depositLossInput: !!depositLossInput
        });
        return;
    }
    
    // 确保输入值为数字
    const depositWeight = parseFloat(depositWeightInput.value) || 0;
    const actualDepositWeight = parseFloat(actualDepositWeightInput.value) || 0;
    
    console.log('计算寄料损耗:', {
        depositWeight: depositWeight,
        actualDepositWeight: actualDepositWeight
    });
    
    // 计算损耗 = 寄料克重 - 实际到料克重
    const depositLoss = depositWeight - actualDepositWeight;
    
    // 设置损耗值，保留两位小数
    depositLossInput.value = depositLoss.toFixed(2);
    
    console.log('计算结果 - 损耗:', depositLoss.toFixed(2));
    
    // 更新总计算
    updateCalculations();
}

// 检查 addRowByType 函数是否存在，如果不存在，添加它

// 删除重复的函数定义，使用下面统一的版本

// 初始化存款资金来源下拉选项
function initializeDepositMoneySource(selectElement) {
    if (!selectElement) return;
    
    // 保存当前值（如果有）
    const currentValue = selectElement.value;
    
    // 清空现有选项，保留第一个空选项
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // 只添加账户信息表中的账户选项
    if (window.accounts && window.accounts.length > 0) {
        window.accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.name;
                option.textContent = account.name;
                selectElement.appendChild(option);
        });
        console.log(`已添加 ${window.accounts.length} 个账户选项到存款来源`);
    } else {
        console.warn('没有找到账户数据，存款来源下拉框将为空');
    }
    
    // 如果有之前的值，尝试恢复
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// 初始化还料来源选项（包括存料抵扣和金料供应商）
function initializeReturnMaterialSource(selectElement) {
    if (!selectElement) return;
    
    // 保存当前值（如果有）
    const currentValue = selectElement.value;
    
    // 清空现有选项，保留第一个空选项
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // 添加存料抵扣选项（如果不存在）
    let hasOffsetOption = false;
    for (let i = 0; i < selectElement.options.length; i++) {
        if (selectElement.options[i].value === '存料抵扣') {
            hasOffsetOption = true;
            break;
        }
    }
    
    if (!hasOffsetOption) {
        const offsetOption = document.createElement('option');
        offsetOption.value = '存料抵扣';
        offsetOption.textContent = '存料抵扣';
        selectElement.appendChild(offsetOption);
    }
    
    // 添加金料供应商选项
    if (window.goldSuppliers && window.goldSuppliers.length > 0) {
        window.goldSuppliers.forEach(supplier => {
            const option = document.createElement('option');
            // 使用供应商名称作为值和显示文本
            option.value = supplier.name;
            option.textContent = supplier.name;
            selectElement.appendChild(option);
        });
        console.log(`已添加 ${window.goldSuppliers.length} 个金料供应商选项到还料来源`);
    } else {
        console.warn('没有找到金料供应商数据');
    }
    
    // 如果有之前的值，尝试恢复
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// 添加初始化还款用途下拉框的函数
function initializeReturnMoneyPurpose(selectElement) {
    if (!selectElement) return;
    
    // 保存当前值（如果有）
    const currentValue = selectElement.value;
    
    // 清空现有选项
    while (selectElement.options.length > 0) {
        selectElement.remove(0);
    }
    
    // 添加标准选项
    const options = [
        { value: '', text: '请选择还款用途' },
        { value: '还工费', text: '还工费' },
        { value: '买料', text: '买料' }
    ];
    
    options.forEach(opt => {
        const option = document.createElement('option');
        option.value = opt.value;
        option.textContent = opt.text;
        selectElement.appendChild(option);
    });
    
    // 如果有之前的值，尝试恢复
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// 添加初始化还款来源下拉框的函数
function initializeReturnMoneySource(selectElement) {
    if (!selectElement) return;
    
    // 保存当前值（如果有）
    const currentValue = selectElement.value;
    
    // 清空现有选项，保留第一个空选项
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // 添加存款抵扣固定选项
    const offsetOption = document.createElement('option');
    offsetOption.value = '存款抵扣';
    offsetOption.textContent = '存款抵扣';
    selectElement.appendChild(offsetOption);
    
    // 只添加账户信息表中的账户选项
    if (window.accounts && window.accounts.length > 0) {
        window.accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.name;
            option.textContent = account.name;
            selectElement.appendChild(option);
        });
        console.log(`已添加 ${window.accounts.length} 个账户选项到还款来源`);
    } else {
        console.warn('没有找到账户数据，还款来源下拉框只有存款抵扣选项');
    }
    
    // 如果有之前的值，尝试恢复
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// 删除重复的fetchGoldSuppliers函数，使用下面的优化版本

// 更新金料供应商选择框
function updateGoldSupplierSelects() {
    // console.log('开始更新还料来源选择框...'); // 生产环境中注释掉
    
    const returnSourceSelects = document.querySelectorAll('.return-material-source');
    let updatedCount = 0;
    
    returnSourceSelects.forEach(select => {
        // 保存当前值
        const currentValue = select.value;
        
        // 初始化选择框
        initializeReturnMaterialSource(select);
        
        // 如果有之前的值，尝试恢复
        if (currentValue && Array.from(select.options).some(opt => opt.value === currentValue)) {
            select.value = currentValue;
            updatedCount++;
        }
    });
    
    console.log(`更新了 ${returnSourceSelects.length} 个还料来源选择框，恢复了 ${updatedCount} 个之前的值`);
}

// 初始化存料来源选项
function initializeStoreMaterialSource(selectElement) {
    if (!selectElement) {
        console.warn('初始化存料来源失败：选择框元素为空');
        return;
    }
    
    console.log('开始初始化存料来源选择框');
    
    // 保存当前值（如果有）
    const currentValue = selectElement.value;
    console.log('当前存料来源值:', currentValue);
    
    // 清空现有选项，保留第一个空选项
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // 添加存料抵扣选项
    const offsetOption = document.createElement('option');
    offsetOption.value = '存料抵扣';
    offsetOption.textContent = '存料抵扣';
    selectElement.appendChild(offsetOption);
    
    // 添加金料供应商选项
    if (window.goldSuppliers && window.goldSuppliers.length > 0) {
        window.goldSuppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.name;
            option.textContent = supplier.name;
            selectElement.appendChild(option);
        });
        console.log(`已添加 ${window.goldSuppliers.length} 个金料供应商选项到存料来源`);
    } else {
        console.warn('没有找到金料供应商数据');
    }
    
    // 如果有之前的值，尝试恢复
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// 从 money_transactions 中提取数据
if (Array.isArray(transactionData.money_transactions) && transactionData.money_transactions.length > 0) {
    transactionData.money_transactions.forEach(item => {
        console.log('处理款项交易项:', item);
        if (item.return_amount > 0) {
            // 删除买料相关的逻辑，所有还款都加入还款列表
            returnMoneyItems.push(item);
        } else if (item.store_amount > 0) {
            depositMoneyItems.push(item);
        }
    });
}
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // 添加金料供应商选项
    if (window.goldSuppliers && window.goldSuppliers.length > 0) {
        console.log(`从 window.goldSuppliers 添加 ${window.goldSuppliers.length} 个供应商选项`);
        window.goldSuppliers.forEach(supplier => {
            const option = document.createElement('option');
            // 使用供应商名称作为值和显示文本
            option.value = supplier.name;
            option.textContent = supplier.name;
            selectElement.appendChild(option);
        });
    } else {
        console.warn('没有找到金料供应商数据，请检查API接口');
    }
    
    // 如果有之前的值，尝试恢复
    if (currentValue) {
        let optionExists = false;
        // 检查选项是否存在
        for (let i = 0; i < selectElement.options.length; i++) {
            if (selectElement.options[i].value === currentValue) {
                selectElement.selectedIndex = i;
                optionExists = true;
                console.log(`恢复存料来源选择框的值: ${currentValue}`);
                break;
            }
        }
        
        // 如果选项不存在，添加它
        if (!optionExists && currentValue !== '') {
            const option = document.createElement('option');
            option.value = currentValue;
            option.textContent = currentValue;
            selectElement.appendChild(option);
            selectElement.value = currentValue;
            console.log(`添加并选中之前的值: ${currentValue}`);
        }
    }
    
    console.log(`完成初始化存料来源选择框，现有 ${selectElement.options.length} 个选项`);
}

// 添加输出表格数据到控制台的函数
function logTableData() {
    // console.log('===== 表格数据 =====');
    const allData = {
        return_material: [],
        buy_material: [],
        store_material: [],
        return_money: [],
        deposit_money: []
    };
    
    // 获取所有行
    const allRows = document.querySelectorAll('.transaction-row tbody tr');
    
    allRows.forEach(tr => {
        const container = tr.closest('.transaction-row');
        if (!container) return;
        
        const type = container.getAttribute('data-type');
        
        // 处理还料行
        if (type === 'return_material') {
            const rowData = {
                material_type: tr.querySelector('[name="return_material_type[]"]')?.value || '',
                material_source: tr.querySelector('[name="return_material_source[]"]')?.value || '',
                return_weight: parseFloat(tr.querySelector('[name="return_weight[]"]')?.value) || 0,
                note: tr.querySelector('[name="material_note[]"]')?.value || ''
            };
            allData.return_material.push(rowData);
        }
        // 处理买料行
        else if (type === 'buy_material') {
            const rowData = {
                material_type: tr.querySelector('[name="buy_material_type[]"]')?.value || '',
                buy_weight: parseFloat(tr.querySelector('[name="buy_weight[]"]')?.value) || 0,
                material_price: parseFloat(tr.querySelector('[name="material_price[]"]')?.value) || 0,
                buy_amount: parseFloat(tr.querySelector('[name="buy_amount[]"]')?.value) || 0,
                note: tr.querySelector('[name="money_note[]"]')?.value || ''
            };
            allData.buy_material.push(rowData);
        }
        // 处理存料行
        else if (type === 'store_material') {
            const rowData = {
                material_type: tr.querySelector('[name="store_material_type[]"]')?.value || '',
                material_source: tr.querySelector('[name="store_material_source[]"]')?.value || '',
                store_weight: parseFloat(tr.querySelector('[name="store_weight[]"]')?.value) || 0,
                note: tr.querySelector('[name="material_note[]"]')?.value || ''
            };
            allData.store_material.push(rowData);
        }
        // 处理还款行
        else if (type === 'return_money') {
            const rowData = {
                return_amount: parseFloat(tr.querySelector('[name="return_amount[]"]')?.value) || 0,
                return_money_source: tr.querySelector('[name="return_money_source[]"]')?.value || '',
                return_purpose: tr.querySelector('[name="return_purpose[]"]')?.value || '',
                note: tr.querySelector('[name="money_note[]"]')?.value || ''
            };
            allData.return_money.push(rowData);
        }
        // 处理存款行
        else if (type === 'deposit_money') {
            const rowData = {
                store_amount: parseFloat(tr.querySelector('[name="deposit_amount[]"]')?.value) || 0,
                store_source: tr.querySelector('[name="deposit_source[]"]')?.value || '',
                note: tr.querySelector('[name="money_note[]"]')?.value || ''
            };
            allData.deposit_money.push(rowData);
        }
    });
    
    // console.log(allData);
    return allData;
}

// 更新存料来源选择框
function updateStoreMaterialSelects() {
    // console.log('开始更新所有存料来源选择框...'); // 生产环境中注释掉
    
    // 确保金料供应商数据已加载
    if (!window.goldSuppliers || window.goldSuppliers.length === 0) {
        console.log('金料供应商数据未加载，使用默认数据');
        useDefaultSuppliers();
        return; // 等待下一次调用
    }
    
    const storeSourceSelects = document.querySelectorAll('.store-material-source');
    console.log(`找到 ${storeSourceSelects.length} 个存料来源选择框`);
    
    let updatedCount = 0;
    
    storeSourceSelects.forEach((select, index) => {
        // 保存当前值
        const currentValue = select.value;
        console.log(`更新存料来源选择框 #${index + 1}，当前值: "${currentValue}"`);
        
        try {
            // 初始化选择框
            initializeStoreMaterialSource(select);
            
            // 如果有之前的值，尝试恢复
            if (currentValue) {
                // 检查选项是否存在
                let optionExists = false;
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].value === currentValue) {
                        select.selectedIndex = i;
                        optionExists = true;
                        updatedCount++;
                        console.log(`找到并选中存料来源: ${currentValue}`);
                        break;
                    }
                }
                
                // 如果选项不存在，添加它
                if (!optionExists && currentValue !== '') {
                    const option = document.createElement('option');
                    option.value = currentValue;
                    option.textContent = currentValue;
                    select.appendChild(option);
                    select.value = currentValue;
                    updatedCount++;
                    console.log(`添加并选中存料来源: ${currentValue}`);
                }
            }
        } catch (error) {
            console.error(`更新存料来源选择框 #${index + 1} 出错:`, error);
        }
    });
    
    console.log(`已更新 ${storeSourceSelects.length} 个存料来源选择框，恢复了 ${updatedCount} 个之前的值`);
    
    // 如果找不到任何存料来源选择框，可能是它们尚未创建
    if (storeSourceSelects.length === 0) {
        console.log('未找到任何存料来源选择框，可能是它们尚未创建');
    }
}

// 删除重复的updateAllSelects函数，使用上面优化版本的函数

// 专门用来修复存料来源选择框的函数
function fixStoreMaterialSources() {
    console.log('开始修复存料来源选择框...');
    
    // 获取所有存料来源选择框
    const storeSourceSelects = document.querySelectorAll('.store-material-source');
    if (storeSourceSelects.length === 0) {
        console.log('未找到任何存料来源选择框，可能是尚未创建任何存料表格行');
        return;
    }
    
    console.log(`找到 ${storeSourceSelects.length} 个存料来源选择框需要修复`);
    
    // 确保有供应商数据可用
    if (!window.goldSuppliers || window.goldSuppliers.length === 0) {
        console.warn('金料供应商数据未加载，请检查API接口');
        return; // 没有数据就不更新
    }
    
    // 对每个选择框进行修复
    storeSourceSelects.forEach((select, index) => {
        // 保存当前值
        const currentValue = select.value;
        console.log(`修复存料来源选择框 #${index + 1}，当前值: "${currentValue}"`);
        
        try {
            // 清空现有选项，保留第一个空选项
            while (select.options.length > 1) {
                select.remove(1);
            }
            
            // 添加所有供应商选项
            window.goldSuppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.name;
                option.textContent = supplier.name;
                select.appendChild(option);
            });
            
            // 添加额外常用选项
            ['料部库存', '现金', '银行转账', '支付宝', '微信'].forEach(value => {
                // 检查选项是否已存在
                let exists = false;
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].value === value) {
                        exists = true;
                        break;
                    }
                }
                
                if (!exists) {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = value;
                    select.appendChild(option);
                }
            });
            
            // 如果有之前的值，尝试恢复或添加
            if (currentValue && currentValue !== '') {
                // 检查选项是否存在
                let exists = false;
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].value === currentValue) {
                        select.selectedIndex = i;
                        exists = true;
                        console.log(`已恢复选中值: ${currentValue}`);
                        break;
                    }
                }
                
                // 如果不存在，添加它
                if (!exists) {
                    const option = document.createElement('option');
                    option.value = currentValue;
                    option.textContent = currentValue;
                    select.appendChild(option);
                    select.value = currentValue;
                    console.log(`已添加并选中未知值: ${currentValue}`);
                }
            }
            
            console.log(`选择框 #${index + 1} 现在有 ${select.options.length} 个选项`);
        } catch (error) {
            console.error(`修复存料来源选择框 #${index + 1} 出错:`, error);
        }
    });
    
    console.log(`已完成 ${storeSourceSelects.length} 个存料来源选择框的修复`);
}

// 添加页面加载完成后的处理
window.addEventListener('load', function() {
    // console.log('页面完全加载完成，开始最终修复...'); // 生产环境中注释掉

    // 立即执行最终的选择框修复
    // console.log('开始最终的选择框修复'); // 生产环境中注释掉
    fixStoreMaterialSources();
    // updateAllSelects(); // 已通过防重复机制在数据加载完成后执行过，这里不需要重复调用

    // 检查并确保显示寄料表格
    const isEditMode = window.location.href.includes('/edit');
    if (isEditMode) {
        ensureDepositMaterialTableExists();
    }
});

// 添加确保寄料表格存在的函数
function ensureDepositMaterialTableExists() {
    console.log('正在检查寄料表格是否存在...');
    const depositMaterialTable = document.querySelector('.transaction-row[data-type="deposit_material"]');
    
    if (!depositMaterialTable) {
        console.log('未找到寄料表格，尝试自动创建...');
        // 设置自动创建标记
        window.isAutoCreating = true;
        addRowByType('deposit_material');
        // 重置自动创建标记
        window.isAutoCreating = false;
        
        // 初始化新创建的表格
        const newTable = document.querySelector('.transaction-row[data-type="deposit_material"]');
        if (newTable) {
            const tbody = newTable.querySelector('tbody');
            if (tbody && tbody.children.length > 0) {
                Array.from(tbody.children).forEach(row => {
                    initializeRowElements(row);
                });
            }
            console.log('寄料表格创建完成并初始化');
        } else {
            console.log('自动创建寄料表格被阻止 - 这是正常的');
        }
    } else {
        console.log('寄料表格已存在，检查数据是否正确加载');
        
        // 检查寄料表格中的数据是否正确加载
        const tbody = depositMaterialTable.querySelector('tbody');
        if (tbody && tbody.children.length > 0) {
            Array.from(tbody.children).forEach((row, index) => {
                const depositWeight = row.querySelector('[name="deposit_weight[]"]');
                const actualDepositWeight = row.querySelector('[name="actual_deposit_weight[]"]');
                
                // 如果任一字段为空或为0，尝试填充默认值
                if (!depositWeight.value || depositWeight.value === '0') {
                    console.log(`寄料表格行 #${index+1} 的寄料克重为空或0，设置默认值`);
                    depositWeight.value = '0';
                }
                
                if (!actualDepositWeight.value || actualDepositWeight.value === '0') {
                    console.log(`寄料表格行 #${index+1} 的实际到料为空或0，设置默认值`);
                    actualDepositWeight.value = '0';
                }
                
                // 重新触发损耗计算
                updateDepositLoss(depositWeight);
            });
        }
    }
    
    // 立即触发所有寄料表格的更新
    const allDepositWeights = document.querySelectorAll('[name="deposit_weight[]"]');
    console.log(`找到 ${allDepositWeights.length} 个寄料克重字段，触发更新`);
    allDepositWeights.forEach(el => updateDepositLoss(el));
}

// 添加全局标记，禁止自动创建寄料表格
window.noAutoCreateDepositMaterial = true;

// 重写addRowByType函数，拦截寄料表格的创建请求
const originalAddRowByType = window.addRowByType;
if (originalAddRowByType) {
    window.addRowByType = function(type) {
        // 只在自动创建时拦截寄料表格创建请求，允许手动点击按钮创建
        if (type === 'deposit_material' && window.noAutoCreateDepositMaterial && window.isAutoCreating) {
            console.log('拦截到自动寄料表格创建请求');
            
            // 检查是否有实际的寄料数据
            const hasRealDepositData = window.hasValidDepositData || false;
            
            if (!hasRealDepositData) {
                console.log('阻止自动寄料表格创建 - 没有有效数据');
                window.isAutoCreating = false;
                return false; // 阻止创建
            }
        }
        
        // 继续原始函数
        return originalAddRowByType.apply(this, arguments);
    };
}

// 不自动创建寄料表格
const isEditMode = window.location.href.includes('/edit');
if (isEditMode) {
    console.log('编辑模式 - 寄料表格只在有实际数据时显示');
}

// 移除拦截寄料表格创建的代码
console.log('已允许手动添加寄料表格，用户可以正常使用寄料表格按钮');

// 添加数据修改监听函数
function setupDataChangeListeners() {
    // 监听表单输入变化
    const formInputs = document.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => {
        input.addEventListener('change', () => {
            console.log('检测到表单数据变化');
            window.dataModified = true;
        });
        
        if (input.tagName === 'INPUT' && input.type === 'text' || input.type === 'number') {
            input.addEventListener('input', () => {
                console.log('检测到表单数据输入');
                window.dataModified = true;
            });
        }
    });

    // 监听动态添加的行
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0) {
                console.log('检测到DOM变化');
                window.dataModified = true;
                
                // 为新添加的元素设置监听器
                const newInputs = document.querySelectorAll('input, select, textarea');
                newInputs.forEach(input => {
                    if (!input.hasChangeListener) {
                        input.addEventListener('change', () => {
                            console.log('检测到新添加元素数据变化');
                            window.dataModified = true;
                        });
                        
                        if (input.tagName === 'INPUT' && (input.type === 'text' || input.type === 'number')) {
                            input.addEventListener('input', () => {
                                console.log('检测到新添加元素数据输入');
                                window.dataModified = true;
                            });
                        }
                        
                        input.hasChangeListener = true;
                    }
                });
            }
        });
    });
    
    observer.observe(document.getElementById('transactions-container'), {
        childList: true,
        subtree: true
    });
    
    console.log('数据修改监听器已设置');
}

// 主要的页面初始化处理器 - 已合并重复的DOMContentLoaded事件
// 其他重复的DOMContentLoaded事件处理器已被移除以避免重复执行

// 保存按钮事件处理逻辑已合并到主要的DOMContentLoaded处理器中

// 修改 submitForm 函数，增加数据回滚逻辑
function submitForm() {
    const isEditMode = document.getElementById('transaction-id').value !== '';
    
    // 如果是编辑模式，需要添加原始数据回滚信息
    if (isEditMode && window.originalFormData) {
        // 在这里，我们将原始数据一并发送给后端，后端会先回滚原始数据，再应用新数据
        // 这样可以确保数据计算的正确性
        
        // 获取并记录所有表格数据
        const formData = logTableData();
        
        // 获取其他必要信息
        const supplierId = document.getElementById('supplier_id').value;
        const businessDate = document.getElementById('business_date').value;
        const note = document.getElementById('notes').value;
        
        // 获取历史余额数据
        const previousOwedGoldElement = document.getElementById('previous-owed-gold');
        const previousDepositGoldElement = document.getElementById('previous-deposit-gold');
        const previousOwedAmountElement = document.getElementById('previous-owed-amount');
        const previousDepositAmountElement = document.getElementById('previous-deposit-amount');

        // === 不再收集和发送上期数据，统一使用API动态计算 ===
        console.log('不再收集上期数据，统一使用API动态计算');

        // 创建提交数据对象
        const data = {
            supplier_id: supplierId,
            business_date: businessDate,
            notes: note,
            material_transactions: [],
            money_transactions: [],
            is_edit_mode: true,
            original_data: window.originalFormData, // 添加原始数据
            // === 不再发送上期数据，统一使用API动态计算 ===

            // 添加表格数据
            material_transactions: [
                ...formData.return_material.map(item => ({
                    return_weight: item.return_weight,
                    return_material_type: item.material_type,
                    return_source: item.material_source,
                    wastage_loss: item.wastage_loss,
                    note: item.note
                })),
                ...formData.store_material.map(item => ({
                    store_weight: item.store_weight,
                    store_material_type: item.material_type,
                    store_source: item.material_source,
                    note: item.note
                })),
                ...formData.deposit_material.map(item => ({
                    deposit_weight: item.deposit_weight,
                    deposit_material_type: item.material_type,
                    actual_deposit_weight: item.actual_deposit_weight,
                    deposit_loss: item.deposit_loss,
                    note: item.note
                }))
            ],
            money_transactions: [
                ...formData.return_money.map(item => ({
                    return_amount: item.return_amount,
                    return_source: item.return_money_source,
                    return_purpose: item.return_purpose || '还款',
                    note: item.note
                })),
                ...formData.deposit_money.map(item => ({
                    store_amount: item.store_amount,
                    store_source: item.store_source,
                    note: item.note
                })),
                ...formData.buy_material.map(item => ({
                    return_amount: item.buy_total,
                    return_source: '',
                    return_purpose: '买料',
                    return_material_type: item.material_type,
                    return_weight: item.buy_weight,
                    note: item.note
                }))
            ]
        };
        
        // 发送数据到服务器
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const url = `/supplier_transactions/${document.getElementById('transaction-id').value}/edit`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: '保存成功',
                    text: '供应商往来记录已更新'
                }).then(() => {
                    window.location.href = '/supplier_transactions';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '保存失败',
                    text: data.message || '发生未知错误'
                });
            }
        })
        .catch(error => {
            console.error('提交错误:', error);
            Swal.fire({
                icon: 'error',
                title: '提交错误',
                text: error.message
            });
        });
    } else {
        // 常规模式，使用原有逻辑
        // 获取并记录所有表格数据
        const formData = logTableData();
        
        // 获取其他必要信息
        const supplierId = document.getElementById('supplier_id').value;
        const businessDate = document.getElementById('business_date').value;
        const note = document.getElementById('notes').value;
        
        // 创建提交数据对象
        const data = {
            supplier_id: supplierId,
            business_date: businessDate,
            notes: note,
            material_transactions: [],
            money_transactions: [],
            
            // 添加表格数据
            material_transactions: [
                ...formData.return_material.map(item => ({
                    return_weight: item.return_weight,
                    return_material_type: item.material_type,
                    return_source: item.material_source,
                    wastage_loss: item.wastage_loss,
                    note: item.note
                })),
                ...formData.store_material.map(item => ({
                    store_weight: item.store_weight,
                    store_material_type: item.material_type,
                    store_source: item.material_source,
                    note: item.note
                })),
                ...formData.deposit_material.map(item => ({
                    deposit_weight: item.deposit_weight,
                    deposit_material_type: item.material_type,
                    actual_deposit_weight: item.actual_deposit_weight,
                    deposit_loss: item.deposit_loss,
                    note: item.note
                }))
            ],
            money_transactions: [
                ...formData.return_money.map(item => ({
                    return_amount: item.return_amount,
                    return_source: item.return_money_source,
                    return_purpose: item.return_purpose || '还款',
                    note: item.note
                })),
                ...formData.deposit_money.map(item => ({
                    store_amount: item.store_amount,
                    store_source: item.store_source,
                    note: item.note
                })),
                ...formData.buy_material.map(item => ({
                    return_amount: item.buy_total,
                    return_source: '',
                    return_purpose: '买料',
                    return_material_type: item.material_type,
                    return_weight: item.buy_weight,
                    note: item.note
                }))
            ]
        };
        
        // 发送数据到服务器
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const url = '/supplier_transactions/add';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: '保存成功',
                    text: '供应商往来记录已保存'
                }).then(() => {
                    window.location.href = '/supplier_transactions';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '保存失败',
                    text: data.message || '发生未知错误'
                });
            }
        })
        .catch(error => {
            console.error('提交错误:', error);
            Swal.fire({
                icon: 'error',
                title: '提交错误',
                text: error.message
            });
        });
    }
    
    return false; // 阻止表单默认提交
}

// 使用安全的拼音转换器
function getChineseInitials(text) {
    // 检查拼音转换器是否可用
    if (window.PinyinConverter && typeof window.PinyinConverter.getInitials === 'function') {
        return window.PinyinConverter.getInitials(text);
    }

    // 如果转换器不可用，使用简单的备用方案
    console.warn('PinyinConverter不可用，使用备用方案');
    let initials = '';
    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        if (/[a-zA-Z0-9]/.test(char)) {
            initials += char.toLowerCase();
        } else {
            // 简单的中文字符处理
            const code = char.charCodeAt(0);
            if (code >= 0x4E00 && code <= 0x9FFF) {
                const letterIndex = Math.floor((code - 0x4E00) / 800) % 26;
                initials += String.fromCharCode(97 + letterIndex);
            }
        }
    }
    return initials;
}

// 使用安全的拼音转换器进行搜索匹配
function smartMatch(text, query) {
    // 检查拼音转换器是否可用
    if (window.PinyinConverter && typeof window.PinyinConverter.smartMatch === 'function') {
        return window.PinyinConverter.smartMatch(text, query);
    }

    // 如果转换器不可用，使用简单的备用匹配
    console.warn('PinyinConverter不可用，使用备用搜索方案');
    const lowerText = text.toLowerCase();
    const lowerQuery = query.toLowerCase();

    // 简单的直接匹配
    return lowerText.includes(lowerQuery);
}



// 初始化供应商搜索功能
function initializeSupplierSearch() {
    // 使用全局供应商数据
    const suppliersData = window.suppliersData || [];
    console.log('初始化供应商搜索功能，供应商数据:', suppliersData ? suppliersData.length : 0);

    // 如果没有供应商数据，提前返回
    if (!suppliersData || !Array.isArray(suppliersData) || suppliersData.length === 0) {
        console.warn('没有供应商数据，跳过搜索功能初始化');
        return;
    }

    const searchInput = document.getElementById('supplier-search');
    const hiddenInput = document.getElementById('supplier-id');
    const resultsContainer = document.querySelector('.supplier-search-results');

    console.log('搜索元素检查:', {
        searchInput: !!searchInput,
        hiddenInput: !!hiddenInput,
        resultsContainer: !!resultsContainer
    });

    // 搜索输入事件
    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        console.log('供应商搜索输入:', query);

        if (query.length === 0) {
            resultsContainer.style.display = 'none';
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });
            hiddenInput.value = '';
            updateSupplierInfo();
            return;
        }

        // 优化的供应商搜索逻辑 - 使用数据库拼音数据
        const filteredSuppliers = suppliersData.filter(function(supplier) {
            const name = supplier.name.toLowerCase();
            const pinyin = (supplier.pinyin || '').toLowerCase();
            const initials = (supplier.pinyin_initials || '').toLowerCase();

            // 1. 名称直接包含匹配（最高优先级）
            if (name.includes(query)) {
                return true;
            }

            // 2. 首字母精确开头匹配 - 例如 z 显示 z 开头的结果，zh 显示 zh 开头的结果
            if (initials.startsWith(query)) {
                return true;
            }

            // 3. 完整拼音开头匹配
            if (pinyin.startsWith(query)) {
                return true;
            }

            // 4. 拼音部分匹配 - 支持拼音中任意音节开头匹配
            if (pinyin.length > 0) {
                // 分割拼音为音节（如chenzhongdong -> ['chen', 'zhong', 'dong']）
                const pinyinParts = pinyin.match(/[a-z]+/g) || [];
                for (let j = 0; j < pinyinParts.length; j++) {
                    if (pinyinParts[j].startsWith(query)) {
                        return true;
                    }
                }
            }

            // 5. 首字母序列匹配 - 支持跨字符组合匹配（如 cz 匹配 chenzhongdong）
            if (query.length > 1 && initials.length > 0) {
                const queryChars = query.split('');
                const initialsChars = initials.split('');

                // 检查查询字符是否在首字母中按顺序出现
                let queryIndex = 0;
                for (let i = 0; i < initialsChars.length && queryIndex < queryChars.length; i++) {
                    if (initialsChars[i] === queryChars[queryIndex]) {
                        queryIndex++;
                    }
                }

                // 如果所有查询字符都按顺序找到了，则匹配
                if (queryIndex === queryChars.length) {
                    return true;
                }
            }

            // 6. 单字符首字母匹配
            if (query.length === 1 && initials.includes(query)) {
                return true;
            }

            // 7. 动态拼音转换器作为最后备用方案
            if (window.PinyinConverter && typeof window.PinyinConverter.smartMatch === 'function') {
                return window.PinyinConverter.smartMatch(supplier.name, query);
            }

            return false;
        });

        console.log('过滤后的供应商数量:', filteredSuppliers.length);

        // 显示搜索结果
        if (filteredSuppliers.length > 0) {
            var html = '';
            filteredSuppliers.forEach(function(supplier) {
                html += '<div class="search-result-item" data-supplier-id="' + supplier.id + '" ' +
                       'style="padding: 6px 12px; cursor: pointer; border-bottom: 1px solid #eee; background: white; font-size: 14px; line-height: 1.4;" ' +
                       'onmouseover="this.style.backgroundColor=\'#f8f9fa\'" onmouseout="this.style.backgroundColor=\'white\'" ' +
                       'data-info=\'' + JSON.stringify({
                           name: supplier.name,
                           supplier_type: supplier.supplier_type,
                           contact_person: supplier.contact_person,
                           phone: supplier.phone,
                           owed_amount: supplier.owed_amount || 0,
                           owed_gold: supplier.owed_gold || 0,
                           deposit_amount: supplier.deposit_amount || 0,
                           deposit_gold: supplier.deposit_gold || 0
                       }) + '\'>' +
                       supplier.name +
                       '</div>';
            });
            resultsContainer.innerHTML = html;

            // 将搜索结果添加到body中，避免容器限制（与采购单录入页完全一致）
            var inputElement = searchInput;
            var rect = inputElement.getBoundingClientRect();

            // 移除现有的搜索结果容器
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });

            // 创建新的搜索结果容器并添加到body
            var globalResults = document.createElement('div');
            globalResults.className = 'supplier-search-results-global';
            globalResults.innerHTML = html;
            document.body.appendChild(globalResults);

            console.log('搜索框定位信息:', {
                rect: rect,
                resultTop: rect.bottom + 1,
                resultLeft: rect.left,
                resultWidth: rect.width
            });

            // 设置样式（与采购单录入页完全一致）
            globalResults.style.position = 'fixed';
            globalResults.style.top = (rect.bottom + 1) + 'px';
            globalResults.style.left = rect.left + 'px';
            globalResults.style.width = rect.width + 'px';
            globalResults.style.maxHeight = '200px';
            globalResults.style.overflowY = 'auto';
            globalResults.style.zIndex = '999999';
            globalResults.style.background = 'white';
            globalResults.style.border = '1px solid #ced4da';
            globalResults.style.borderRadius = '4px';
            globalResults.style.boxShadow = '0 4px 12px rgba(0,0,0,0.25)';
            globalResults.style.marginTop = '0px';
            globalResults.style.display = 'block';

            // 隐藏原有的搜索结果容器
            resultsContainer.style.display = 'none';
        } else {
            resultsContainer.style.display = 'none';
            // 移除全局搜索结果
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });
        }
    });

    // 点击搜索结果（与采购单录入页一致）
    document.addEventListener('click', function(e) {
        if (e.target.closest('.search-result-item')) {
            const item = e.target.closest('.search-result-item');
            const supplierId = item.getAttribute('data-supplier-id');
            const supplierInfo = JSON.parse(item.getAttribute('data-info'));

            console.log('选择供应商:', supplierInfo.name, 'ID:', supplierId);

            searchInput.value = supplierInfo.name;
            hiddenInput.value = supplierId;

            // 隐藏所有搜索结果
            resultsContainer.style.display = 'none';
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });

            // 触发供应商信息更新
            updateSupplierInfo();
        }
    });

    // 点击其他地方隐藏搜索结果（与采购单录入页一致）
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.supplier-search-container') && !e.target.closest('.supplier-search-results-global')) {
            resultsContainer.style.display = 'none';
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });
        }
    });

    // 窗口滚动或调整大小时隐藏搜索结果（与采购单录入页一致）
    window.addEventListener('scroll', function() {
        document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
            el.remove();
        });
        resultsContainer.style.display = 'none';
    });

    window.addEventListener('resize', function() {
        document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
            el.remove();
        });
        resultsContainer.style.display = 'none';
    });
}

// 删除重复的updateSupplierInfo函数，使用第一个版本

// 安全初始化全局变量
function initializeGlobalVariables() {
    if (!window.goldSuppliers) {
        window.goldSuppliers = [];
    }
    if (!window.oldMaterials) {
        window.oldMaterials = [];
    }
    if (!window.accounts) {
        window.accounts = [];
    }
    console.log('全局变量初始化完成');
}

// 统一的页面功能初始化函数
function initializePageFeatures() {
    // 初始化基本功能
    initializeBasicFeatures();

    // 初始化业务日期监听器
    initializeBusinessDateListener();

    // 初始化高级功能
    initializeAdvancedFeatures();
}

// 初始化供应商搜索和其他高级功能
function initializeAdvancedFeatures() {
    // 安全地初始化供应商搜索功能
    try {
        initializeSupplierSearch();
    } catch (searchError) {
        console.error('初始化供应商搜索功能失败:', searchError);
    }

    // 如果是编辑模式且已选择供应商，触发信息更新
    const supplierIdInput = document.getElementById('supplier-id');
    if (supplierIdInput && supplierIdInput.value) {
        try {
            updateSupplierInfo();
        } catch (updateError) {
            console.error('更新供应商信息失败:', updateError);
        }
    }
}

// 删除重复的removeTableRow函数

// 强制设置表格列宽和间距的函数
function forceTableColumnWidths() {
    console.log('开始强制设置表格列宽和间距...');

    // 首先处理表格行容器的间距
    const transactionRows = document.querySelectorAll('#transactions-container .transaction-row');
    // 删除强制样式设置，让CSS自然生效

    // 查找所有表格
    const tables = document.querySelectorAll('#transactions-container table.table');

    tables.forEach((table, index) => {
        console.log(`处理表格 ${index + 1}:`, table);

        // 删除强制样式设置，让CSS自然生效

        console.log(`表格 ${index + 1} 列宽设置完成`);
    });

    // 强制设置容器样式
    // 删除容器强制样式设置

    console.log('所有表格列宽和间距设置完成');
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行以确保所有样式都已加载
    setTimeout(forceTableColumnWidths, 100);

    // 监听表格内容变化
    const observer = new MutationObserver(function(mutations) {
        let shouldUpdate = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                for (let node of mutation.addedNodes) {
                    if (node.nodeType === 1 && (node.tagName === 'TABLE' || node.querySelector('table'))) {
                        shouldUpdate = true;
                        break;
                    }
                }
            }
        });

        if (shouldUpdate) {
            setTimeout(forceTableColumnWidths, 50);
        }
    });

    const container = document.getElementById('transactions-container');
    if (container) {
        observer.observe(container, {
            childList: true,
            subtree: true
        });
    }
});
</script>

<!-- 引入自定义的alert替代品 -->
<script src="{{ url_for('static', filename='js/custom_alert.js') }}"></script>
<!-- 引入pinyin-pro库用于准确的中文转拼音首字母 -->
<script src="https://unpkg.com/pinyin-pro@3.19.6/dist/index.js"></script>
<!-- 引入安全的拼音转换模块 -->
<script src="{{ url_for('static', filename='js/pinyin_converter.js') }}"></script>
{% endblock %}
