{"name": "@npmcli/disparity-colors", "version": "3.0.0", "main": "lib/index.js", "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "description": "Colorizes unified diff output", "repository": {"type": "git", "url": "https://github.com/npm/disparity-colors.git"}, "keywords": ["disparity", "npm", "npmcli", "diff", "char", "unified", "multiline", "string", "color", "ansi", "terminal", "cli", "tty"], "author": "GitHub Inc.", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "license": "ISC", "scripts": {"lint": "eslint \"**/*.js\"", "pretest": "npm run lint", "test": "tap", "snap": "tap", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.5.1", "tap": "^16.0.1"}, "dependencies": {"ansi-styles": "^4.3.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.5.1"}}