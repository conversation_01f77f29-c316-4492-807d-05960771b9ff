{% extends "base.html" %}

{% block title %}显示设置{% endblock %}

{% block page_title %}显示设置{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-display"></i> 分辨率与显示设置
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>提示：</strong>根据您的屏幕分辨率调整系统界面的缩放比例，以获得最佳的显示效果。
                    <br><small>当前屏幕分辨率：<span id="screenResolution">检测中...</span></small>
                </div>

                <form id="displaySettingsForm">
                    <div class="mb-4">
                        <label for="scaleRange" class="form-label">
                            <strong>界面缩放比例</strong>
                            <span class="text-muted">（当前：<span id="currentScale">100%</span>）</span>
                        </label>
                        <input type="range" class="form-range" id="scaleRange" 
                               min="0.5" max="2.0" step="0.1" value="1.0">
                        <div class="d-flex justify-content-between text-muted small mt-1">
                            <span>50%</span>
                            <span>75%</span>
                            <span>100%</span>
                            <span>125%</span>
                            <span>150%</span>
                            <span>200%</span>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label"><strong>预设分辨率</strong></label>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 preset-btn" data-scale="0.8">
                                    <i class="bi bi-laptop"></i> 1366x768 (80%)
                                </button>
                            </div>
                            <div class="col-md-6 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 preset-btn" data-scale="1.0">
                                    <i class="bi bi-display"></i> 1920x1080 (100%)
                                </button>
                            </div>
                            <div class="col-md-6 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 preset-btn" data-scale="1.2">
                                    <i class="bi bi-tv"></i> 2560x1440 (120%)
                                </button>
                            </div>
                            <div class="col-md-6 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 preset-btn" data-scale="1.5">
                                    <i class="bi bi-display-fill"></i> 4K显示器 (150%)
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label"><strong>快速调整</strong></label>
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-secondary quick-btn" data-action="decrease">
                                <i class="bi bi-zoom-out"></i> 缩小
                            </button>
                            <button type="button" class="btn btn-outline-secondary quick-btn" data-action="reset">
                                <i class="bi bi-arrow-clockwise"></i> 重置
                            </button>
                            <button type="button" class="btn btn-outline-secondary quick-btn" data-action="increase">
                                <i class="bi bi-zoom-in"></i> 放大
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary w-100" id="saveSettings">
                                <i class="bi bi-check-circle"></i> 保存设置
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-secondary w-100" id="resetSettings">
                                <i class="bi bi-arrow-counterclockwise"></i> 恢复默认
                            </button>
                        </div>
                    </div>
                </form>

                <hr class="my-4">

                <div class="alert alert-light">
                    <h6><i class="bi bi-lightbulb"></i> 使用建议：</h6>
                    <ul class="mb-3">
                        <li><strong>小屏幕（1366x768）：</strong>建议使用80%缩放</li>
                        <li><strong>标准屏幕（1920x1080）：</strong>建议使用100%缩放</li>
                        <li><strong>高分辨率屏幕（2K/4K）：</strong>建议使用120%-150%缩放</li>
                        <li><strong>触摸屏设备：</strong>建议使用较大的缩放比例以便操作</li>
                    </ul>
                    <h6><i class="bi bi-keyboard"></i> 键盘快捷键：</h6>
                    <ul class="mb-0">
                        <li><kbd>Ctrl</kbd> + <kbd>+</kbd>：放大界面</li>
                        <li><kbd>Ctrl</kbd> + <kbd>-</kbd>：缩小界面</li>
                        <li><kbd>Ctrl</kbd> + <kbd>0</kbd>：重置为100%</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const scaleRange = document.getElementById('scaleRange');
    const currentScaleSpan = document.getElementById('currentScale');
    const presetBtns = document.querySelectorAll('.preset-btn');
    const quickBtns = document.querySelectorAll('.quick-btn');
    const saveBtn = document.getElementById('saveSettings');
    const resetBtn = document.getElementById('resetSettings');
    const screenResolutionSpan = document.getElementById('screenResolution');

    // 检测屏幕分辨率
    function detectScreenResolution() {
        const width = screen.width;
        const height = screen.height;
        const pixelRatio = window.devicePixelRatio || 1;
        screenResolutionSpan.textContent = `${width}x${height} (像素比: ${pixelRatio})`;

        // 根据分辨率推荐缩放比例
        let recommendedScale = 1.0;
        if (width <= 1366) {
            recommendedScale = 0.8;
        } else if (width >= 2560) {
            recommendedScale = 1.2;
        } else if (width >= 3840) {
            recommendedScale = 1.5;
        }

        // 如果没有保存的设置，使用推荐值
        if (!localStorage.getItem('display-scale')) {
            applyScale(recommendedScale);
        }
    }

    // 初始化当前缩放值
    function initCurrentScale() {
        const savedScale = localStorage.getItem('display-scale') || '1.0';
        const scale = parseFloat(savedScale);
        scaleRange.value = scale;
        updateScaleDisplay(scale);
    }

    // 更新缩放显示
    function updateScaleDisplay(scale) {
        currentScaleSpan.textContent = Math.round(scale * 100) + '%';
        
        // 高亮当前预设按钮
        presetBtns.forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-primary');
            if (Math.abs(parseFloat(btn.dataset.scale) - scale) < 0.05) {
                btn.classList.remove('btn-outline-primary');
                btn.classList.add('btn-primary');
            }
        });
    }

    // 应用缩放
    function applyScale(scale) {
        if (window.setDisplayScale) {
            window.setDisplayScale(scale);
        }
        updateScaleDisplay(scale);
        scaleRange.value = scale;
    }

    // 滑块变化事件
    scaleRange.addEventListener('input', function() {
        const scale = parseFloat(this.value);
        applyScale(scale);
    });

    // 预设按钮点击事件
    presetBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const scale = parseFloat(this.dataset.scale);
            applyScale(scale);
        });
    });

    // 快速调整按钮
    quickBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.dataset.action;
            let currentScale = parseFloat(scaleRange.value);
            
            switch(action) {
                case 'decrease':
                    currentScale = Math.max(0.5, currentScale - 0.1);
                    break;
                case 'increase':
                    currentScale = Math.min(2.0, currentScale + 0.1);
                    break;
                case 'reset':
                    currentScale = 1.0;
                    break;
            }
            
            applyScale(currentScale);
        });
    });

    // 保存设置
    saveBtn.addEventListener('click', function() {
        const scale = parseFloat(scaleRange.value);
        localStorage.setItem('display-scale', scale);
        
        // 显示成功消息
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show mt-3';
        alert.innerHTML = `
            <i class="bi bi-check-circle"></i> 显示设置已保存！缩放比例：${Math.round(scale * 100)}%
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        this.parentNode.parentNode.appendChild(alert);
        
        // 3秒后自动关闭
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    });

    // 重置设置
    resetBtn.addEventListener('click', function() {
        if (confirm('确定要重置为默认显示设置吗？')) {
            localStorage.removeItem('display-scale');
            applyScale(1.0);
            
            const alert = document.createElement('div');
            alert.className = 'alert alert-info alert-dismissible fade show mt-3';
            alert.innerHTML = `
                <i class="bi bi-info-circle"></i> 显示设置已重置为默认值
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            this.parentNode.parentNode.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
    });

    // 初始化
    detectScreenResolution();
    initCurrentScale();
});
</script>
{% endblock %}
