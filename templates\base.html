<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - 金色ERP系统{% else %}金色ERP系统{% endif %}</title>
    <!-- 引入字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap 图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <!-- 自定义CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">

    <!-- CSRF令牌 -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        /* 分辨率缩放CSS变量 */
        :root {
            --scale-factor: 1.0;
            --font-scale: 1.0;
        }

        /* 使用zoom属性进行缩放（更兼容的方式） */
        body {
            zoom: var(--scale-factor);
        }

        /* 备用transform方案 */
        @supports not (zoom: 1) {
            body {
                transform: scale(var(--scale-factor));
                transform-origin: top left;
                width: calc(100% / var(--scale-factor));
                height: calc(100vh / var(--scale-factor));
            }
        }
        /* 侧边栏基本样式 */
        .sidebar {
            overflow-x: hidden;
        }
        
        /* 主菜单标题样式 */
        .sidebar-heading {
            cursor: pointer;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-size: 1rem;
            text-align: left;
            padding: 0.4rem 0.5rem;
            font-weight: 600;
            letter-spacing: 0.05rem;
            position: relative;
            width: 100%;
        }
        .sidebar-heading i {
            position: absolute;
            right: 10px;
            font-size: 0.75rem;
        }
        .sidebar-heading.collapsed i.bi-chevron-down {
            transform: rotate(-90deg);
        }
        
        /* 子菜单容器样式 */
        .sidebar-submenu {
            padding-left: 0.1rem;
            max-height: 0;
            overflow: hidden;
            margin-left: 0;
            position: relative;
            width: 100%;
        }
        .sidebar-submenu.show {
            max-height: 500px;
        }
        
        /* 防止折叠菜单时的位移 */
        .sidebar-heading[aria-expanded="true"], 
        .sidebar-heading[aria-expanded="false"] {
            margin-left: 0 !important;
            padding-left: 0.5rem !important;
            width: 100%;
            transform: none !important;
        }
        
        /* 菜单链接通用样式 */
        .nav-link {
            padding: 0.25rem 0.5rem;
            font-size: 0.7rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: none;
        }
        .nav-link.text-center {
            text-align: center !important;
        }
        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        /* 子菜单链接样式 */
        .submenu-link {
            text-align: left;
            padding-left: 0.5rem !important;
            font-size: 0.8rem;
            margin-bottom: 0.15rem;
            width: 100%;
            display: block;
        }
        
        /* 针对collapse组件的特殊处理 */
        .collapse {
            transform: none !important;
        }
        .collapsing {
            transform: none !important;
        }
        
        /* 响应式布局调整 */
        @media (max-width: 992px) {
            .nav-link span.menu-text {
                display: none;
            }
            .sidebar-heading span.menu-title {
                display: none;
            }
            .sidebar-submenu {
                padding-left: 0.3rem;
            }
        }
        
        /* 在小屏幕上调整图标位置 */
        @media (max-width: 768px) {
            .nav-link {
                text-align: center;
            }
            .sidebar-submenu {
                padding-left: 0;
            }
        }
        
        /* 删除可能影响表格的样式 */

        
        /* 主内容区域调整 */
        main.col-md-10.col-lg-10 {
        }

        /* 页面容器样式 - 防止页面滚轮 */
        .page-container {
            height: calc(100vh - 200px);
            max-height: calc(100vh - 200px);
            overflow: hidden;
        }

        .page-container .card {
            height: 100%;
            max-height: 100%;
            overflow: hidden;
        }

        .page-container .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding: 1.5rem;
        }

        /* 删除table-responsive影响 */

        /* 删除page-container table影响 */

        /* 全局按钮字体加粗样式 */
        .btn, button, input[type="button"], input[type="submit"], input[type="reset"] {
            font-weight: bold !important;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-1 col-lg-1 d-md-block bg-dark sidebar collapse" style="min-height: 100vh;">
                <div class="position-sticky pt-5">
                    <div class="d-flex align-items-center mb-4 px-2 justify-content-center">
                        <i class="bi bi-gem text-warning" style="font-size: 1.2rem;"></i>
                        <h1 class="h6 text-light mb-0 ms-1 d-none d-lg-block">金色ERP</h1>
                    </div>
                    
                    <ul class="nav flex-column mb-4">
                        
                        <!-- 基本信息中心 -->
                        {% set has_basic_info = current_user.is_boss() or current_user.has_menu_permission('products') or current_user.has_menu_permission('customers') or current_user.has_menu_permission('old_materials') or current_user.has_menu_permission('suppliers') or current_user.has_menu_permission('employees') or current_user.has_menu_permission('accounts') %}
                        {% if has_basic_info %}
                        <li class="nav-item mt-3 mb-2">
                            <a class="nav-link text-white {% if request.endpoint == 'data_management' %}active{% endif %}" href="{{ url_for('data_management') }}" title="基本信息中心" style="font-size: 1rem; font-weight: 600; letter-spacing: 0.05rem; text-transform: uppercase; transition: none;">
                                <span class="menu-text">基本信息中心</span>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- 单据管理 -->
                        {% set has_documents = current_user.is_boss() or current_user.has_menu_permission('orders') or current_user.has_menu_permission('purchases') or current_user.has_menu_permission('supplier_transactions') or current_user.has_menu_permission('customer_transactions') or current_user.has_menu_permission('material_adjustments') %}
                        {% if has_documents %}
                        <li class="nav-item mt-3 mb-2">
                            <h6 class="sidebar-heading text-uppercase text-light mb-2" data-bs-toggle="collapse" data-bs-target="#documentsSubmenu" aria-expanded="false" title="单据管理" style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="menu-title" style="margin-left: 8px;">单据管理</span> <i class="bi bi-chevron-down"></i>
                            </h6>
                            <div id="documentsSubmenu" class="sidebar-submenu collapse">
                                {% if current_user.is_boss() or current_user.has_menu_permission('orders') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'orders' %}active{% endif %}" href="{{ url_for('orders') }}" title="开单录入">
                                    <i class="bi bi-file-earmark-plus"></i> <span class="menu-text ms-1">开单录入</span>
                                </a>
                                {% endif %}
                                {% if current_user.is_boss() or current_user.has_menu_permission('purchases') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'purchases' %}active{% endif %}" href="{{ url_for('purchases') }}" title="采购录入">
                                    <i class="bi bi-cart-plus"></i> <span class="menu-text ms-1">采购录入</span>
                                </a>
                                {% endif %}
                                {% if current_user.is_boss() or current_user.has_menu_permission('supplier_transactions') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'supplier_transactions' %}active{% endif %}" href="{{ url_for('supplier_transactions') }}" title="供应商往来录入">
                                    <i class="bi bi-arrow-down-up"></i> <span class="menu-text ms-1">供应商往来录入</span>
                                </a>
                                {% endif %}
                                {% if current_user.is_boss() or current_user.has_menu_permission('customer_transactions') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'customer_transactions' %}active{% endif %}" href="{{ url_for('customer_transactions') }}" title="其他项目往来">
                                    <i class="bi bi-credit-card"></i> <span class="menu-text ms-1">其他项目往来</span>
                                </a>
                                {% endif %}
                                {% if current_user.is_boss() or current_user.has_menu_permission('material_adjustments') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'material_adjustments' %}active{% endif %}" href="{{ url_for('material_adjustments') }}" title="货料调整录入">
                                    <i class="bi bi-arrow-repeat"></i> <span class="menu-text ms-1">货料调整录入</span>
                                </a>
                                {% endif %}
                            </div>
                        </li>
                        {% endif %}
                        
                        <!-- 报表中心 -->
                        {% set has_reports = current_user.is_boss() or current_user.has_menu_permission('daily_reports') or current_user.has_menu_permission('customer_reports') or current_user.has_menu_permission('supplier_reports') or current_user.has_menu_permission('inventory_reports') or current_user.has_menu_permission('account_reports') %}
                        {% if has_reports %}
                        <li class="nav-item mt-3 mb-2">
                            <h6 class="sidebar-heading text-uppercase text-light mb-2" data-bs-toggle="collapse" data-bs-target="#reportsSubmenu" aria-expanded="false" title="报表中心" style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="menu-title" style="margin-left: 8px;">报表中心</span> <i class="bi bi-chevron-down"></i>
                            </h6>
                            <div id="reportsSubmenu" class="sidebar-submenu collapse">
                                {% if current_user.is_boss() or current_user.has_menu_permission('daily_reports') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'daily_reports' %}active{% endif %}" href="{{ url_for('daily_reports') }}" title="每日报表查询">
                                    <i class="bi bi-calendar-date"></i> <span class="menu-text ms-1">每日报表查询</span>
                                </a>
                                {% endif %}
                                {% if current_user.is_boss() or current_user.has_menu_permission('customer_reports') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'customer_reports' %}active{% endif %}" href="{{ url_for('customer_reports') }}" title="客户往来查询">
                                    <i class="bi bi-people-fill"></i> <span class="menu-text ms-1">客户往来查询</span>
                                </a>
                                {% endif %}
                                {% if current_user.is_boss() or current_user.has_menu_permission('supplier_reports') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'supplier_reports' %}active{% endif %}" href="{{ url_for('supplier_reports') }}" title="供应商往来查询">
                                    <i class="bi bi-truck"></i> <span class="menu-text ms-1">供应商往来查询</span>
                                </a>
                                {% endif %}
                                {% if current_user.is_boss() or current_user.has_menu_permission('inventory_reports') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'inventory_reports' %}active{% endif %}" href="{{ url_for('inventory_reports') }}" title="库存查询">
                                    <i class="bi bi-boxes"></i> <span class="menu-text ms-1">库存查询</span>
                                </a>
                                {% endif %}
                                {% if current_user.is_boss() or current_user.has_menu_permission('account_reports') %}
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'account_reports' %}active{% endif %}" href="{{ url_for('account_reports') }}" title="账户查询">
                                    <i class="bi bi-cash-stack"></i> <span class="menu-text ms-1">账户查询</span>
                                </a>
                                {% endif %}
                            </div>
                        </li>
                        {% endif %}
                        
                        <!-- 系统管理 -->
                        <li class="nav-item mt-3 mb-2">
                            <h6 class="sidebar-heading text-uppercase text-light mb-2" data-bs-toggle="collapse" data-bs-target="#systemSubmenu" aria-expanded="false" title="系统管理" style="display: flex; justify-content: space-between; align-items: center;">
                                <span class="menu-title" style="margin-left: 8px;">系统管理</span> <i class="bi bi-chevron-down"></i>
                            </h6>
                            <div id="systemSubmenu" class="sidebar-submenu collapse">
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'user.users' %}active{% endif %}" href="{{ url_for('user.users') }}" title="用户管理">
                                    <i class="bi bi-people-fill"></i> <span class="menu-text ms-1">用户管理</span>
                                </a>
                                <a class="nav-link submenu-link text-white {% if request.endpoint == 'display_settings' %}active{% endif %}" href="{{ url_for('display_settings') }}" title="显示设置">
                                    <i class="bi bi-display"></i> <span class="menu-text ms-1">显示设置</span>
                                </a>

                            </div>
                        </li>
                    </ul>
                    
                    <div class="border-top my-2 pt-1 px-1">
                        <div class="d-flex flex-column align-items-center mb-1">
                            <div class="rounded-circle bg-light text-dark d-flex align-items-center justify-content-center mb-1" style="width: 22px; height: 22px;">
                                <i class="bi bi-person" style="font-size: 0.7rem;"></i>
                            </div>
                            <div class="text-white text-center">
                                <div class="fw-bold" style="font-size: 0.65rem;">{{ current_user.name }}</div>
                                <div class="text-light opacity-75" style="font-size: 0.6rem;">{{ current_user.role }}</div>
                            </div>
                        </div>
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm w-100 mt-1" style="font-size: 0.65rem; padding: 0.15rem 0.25rem;">
                            <i class="bi bi-box-arrow-right"></i> <span class="menu-text ms-1">退出</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <main class="col-md-11 ms-sm-auto col-lg-11" style="background-color: #f9f7f2; padding: 0; height: 100vh; overflow: auto;">

                <!-- 页面内容 -->
                <div class="content-wrapper" style="flex: 1; overflow: hidden; display: flex; flex-direction: column;">
                    <div class="d-flex justify-content-between align-items-center page-header" style="flex-shrink: 0; padding: 1rem;">
                        <h2 class="page-title">{% if title %}{{ title }}{% else %}{% block page_title %}{% endblock %}{% endif %}</h2>
                        <div>
                            {% block page_actions %}{% endblock %}
                        </div>
                    </div>
                
                    <!-- 消息提醒 -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                {% if '删除' in message %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert" style="font-size: 1.1rem; font-weight: bold; opacity: 0.95;">
                                    <i class="bi bi-check-circle-fill me-2"></i> {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                                {% else %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <!-- 页面主体内容 -->
                    <div class="page-content" style="flex: 1; overflow: hidden;">
                        {% block content %}{% endblock %}
                    </div>
                </div>
                
                <!-- 页脚 - 隐藏以节省空间 -->
                <!-- <footer class="text-center text-muted mb-2">
                    <div class="container">
                        <p class="mb-0">&copy; {{ now.year }} 金色ERP系统 - 版本 1.0</p>
                    </div>
                </footer> -->
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 表格标题对齐修复脚本 -->
    <script src="{{ url_for('static', filename='js/table-header-alignment.js') }}"></script>
    <!-- 自定义JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化工具提示
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
            
            // 自动关闭警告
            var alertList = document.querySelectorAll('.alert')
            alertList.forEach(function (alert) {
                // 获取消息文本
                const alertText = alert.textContent || '';
                

            });
            

            
            // 使用Bootstrap的原生折叠功能
            document.querySelectorAll('.sidebar-heading').forEach(function(heading) {
                heading.setAttribute('data-bs-toggle', 'collapse');
                
                // 添加图标旋转效果
                heading.addEventListener('click', function() {
                    const isExpanded = this.getAttribute('aria-expanded') === 'true';
                    const icon = this.querySelector('i.bi-chevron-down');
                    
                    if (icon) {
                        if (isExpanded) {
                            icon.style.transform = 'rotate(0deg)';
                        } else {
                            icon.style.transform = 'rotate(-90deg)';
                        }
                    }
                });
            });
            

            
            // 为子功能菜单项添加点击事件，防止收缩
            document.querySelectorAll('.sidebar-submenu .nav-link').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                });
            });
            




        });





        // 强制设置特定标题栏为黑色 - 优化版本，避免重复操作
        let blackHeadersProcessed = new Set();
        function forceBlackHeaders() {
            // 需要设置为黑色的标题文本
            const blackHeaderTexts = [
                '新货出库统计',
                '结算信息'
            ];

            blackHeaderTexts.forEach(text => {
                // 查找包含指定文本的标题元素
                const headers = document.querySelectorAll('.card-header h6, .card-header h5, .card-header .mb-0, .card-header .fw-bold, .card-header .card-title');
                headers.forEach(header => {
                    if (header.textContent.trim().includes(text)) {
                        const cardHeader = header.closest('.card-header');
                        if (cardHeader && !blackHeadersProcessed.has(cardHeader)) {
                            // 标记为已处理，避免重复操作
                            blackHeadersProcessed.add(cardHeader);

                            // 移除所有可能的背景类
                            cardHeader.classList.remove('bg-light', 'bg-primary', 'bg-secondary', 'bg-success', 'bg-info', 'bg-warning', 'bg-danger');

                            // 添加强制黑色样式类
                            cardHeader.classList.add('force-black-header');

                            // 同时使用内联样式作为双重保险
                            cardHeader.style.setProperty('background-color', '#000000', 'important');
                            cardHeader.style.setProperty('background-image', 'none', 'important');
                            cardHeader.style.setProperty('background', '#000000', 'important');
                            cardHeader.style.setProperty('color', '#ffffff', 'important');

                            // 确保所有子元素也是白色文字（移除强制字体修改，避免闪烁）
                            const allChildren = cardHeader.querySelectorAll('*');
                            allChildren.forEach(child => {
                                child.style.setProperty('color', '#ffffff', 'important');
                            });

                            console.log(`已设置标题栏为黑色: ${text}`);
                        }
                    }
                });
            });
        }

        // 页面加载完成后执行
        window.addEventListener('load', () => {
            forceBlackHeaders();
        });

        // 监听DOM变化，处理动态加载的内容 - 优化版本，减少不必要的调用
        let observerTimeout;
        const observer = new MutationObserver(function(mutations) {
            let shouldUpdate = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查是否添加了包含card-header的元素
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE &&
                            (node.classList.contains('card-header') || node.querySelector('.card-header'))) {
                            shouldUpdate = true;
                            break;
                        }
                    }
                }
            });
            if (shouldUpdate) {
                forceBlackHeaders();
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 分辨率设置功能
        function initDisplaySettings() {
            // 从localStorage读取保存的缩放比例
            const savedScale = localStorage.getItem('display-scale');
            if (savedScale) {
                applyScale(parseFloat(savedScale));
            }
        }

        function applyScale(scale) {
            // 设置CSS变量
            document.documentElement.style.setProperty('--scale-factor', scale);

            // 保存到localStorage
            localStorage.setItem('display-scale', scale);

            // 触发窗口resize事件，确保组件重新计算尺寸
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        }

        // 页面加载时初始化显示设置
        initDisplaySettings();

        // 全局函数，供显示设置页面调用
        window.setDisplayScale = applyScale;

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl + 加号：放大
            if (e.ctrlKey && (e.key === '=' || e.key === '+')) {
                e.preventDefault();
                const currentScale = parseFloat(localStorage.getItem('display-scale') || '1.0');
                const newScale = Math.min(2.0, currentScale + 0.1);
                applyScale(newScale);
            }
            // Ctrl + 减号：缩小
            else if (e.ctrlKey && e.key === '-') {
                e.preventDefault();
                const currentScale = parseFloat(localStorage.getItem('display-scale') || '1.0');
                const newScale = Math.max(0.5, currentScale - 0.1);
                applyScale(newScale);
            }
            // Ctrl + 0：重置
            else if (e.ctrlKey && e.key === '0') {
                e.preventDefault();
                applyScale(1.0);
            }
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html> 
