<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-hook</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="npm-hook">npm-hook</h1>
<span class="description">Manage registry hooks</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#example">Example</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#registry"><code>registry</code></a></li><li><a href="#otp"><code>otp</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm hook add &lt;pkg&gt; &lt;url&gt; &lt;secret&gt; [--type=&lt;type&gt;]
npm hook ls [pkg]
npm hook rm &lt;id&gt;
npm hook update &lt;id&gt; &lt;url&gt; &lt;secret&gt;
</code></pre>
<p>Note: This command is unaware of workspaces.</p>
<h3 id="description">Description</h3>
<p>Allows you to manage <a href="https://blog.npmjs.org/post/145260155635/introducing-hooks-get-notifications-of-npm">npm
hooks</a>,
including adding, removing, listing, and updating.</p>
<p>Hooks allow you to configure URL endpoints that will be notified whenever a
change happens to any of the supported entity types. Three different types
of entities can be watched by hooks: packages, owners, and scopes.</p>
<p>To create a package hook, simply reference the package name.</p>
<p>To create an owner hook, prefix the owner name with <code>~</code> (as in,
<code>~youruser</code>).</p>
<p>To create a scope hook, prefix the scope name with <code>@</code> (as in,
<code>@yourscope</code>).</p>
<p>The hook <code>id</code> used by <code>update</code> and <code>rm</code> are the IDs listed in <code>npm hook ls</code>
for that particular hook.</p>
<p>The shared secret will be sent along to the URL endpoint so you can verify
the request came from your own configured hook.</p>
<h3 id="example">Example</h3>
<p>Add a hook to watch a package for changes:</p>
<pre><code class="language-bash">$ npm hook add lodash https://example.com/ my-shared-secret
</code></pre>
<p>Add a hook to watch packages belonging to the user <code>substack</code>:</p>
<pre><code class="language-bash">$ npm hook add ~substack https://example.com/ my-shared-secret
</code></pre>
<p>Add a hook to watch packages in the scope <code>@npm</code></p>
<pre><code class="language-bash">$ npm hook add @npm https://example.com/ my-shared-secret
</code></pre>
<p>List all your active hooks:</p>
<pre><code class="language-bash">$ npm hook ls
</code></pre>
<p>List your active hooks for the <code>lodash</code> package:</p>
<pre><code class="language-bash">$ npm hook ls lodash
</code></pre>
<p>Update an existing hook's url:</p>
<pre><code class="language-bash">$ npm hook update id-deadbeef https://my-new-website.here/
</code></pre>
<p>Remove a hook:</p>
<pre><code class="language-bash">$ npm hook rm id-deadbeef
</code></pre>
<h3 id="configuration">Configuration</h3>
<h4 id="registry"><code>registry</code></h4>
<ul>
<li>Default: "<a href="https://registry.npmjs.org/">https://registry.npmjs.org/</a>"</li>
<li>Type: URL</li>
</ul>
<p>The base URL of the npm registry.</p>
<h4 id="otp"><code>otp</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or String</li>
</ul>
<p>This is a one-time password from a two-factor authenticator. It's needed
when publishing or changing package permissions with <code>npm access</code>.</p>
<p>If not set, and a registry response fails with a challenge for a one-time
password, npm will prompt on the command line for one.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="https://blog.npmjs.org/post/145260155635/introducing-hooks-get-notifications-of-npm">"Introducing Hooks" blog post</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-hook.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>