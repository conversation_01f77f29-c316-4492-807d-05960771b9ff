﻿from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, send_file, Response, abort, g
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from sqlalchemy import func, or_, and_, desc, asc, text, Float, cast, not_, Integer, extract
from sqlalchemy import inspect
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, timedelta, date, time
import os
import json
import uuid
import random
import csv
import io
import logging
import traceback
import pandas as pd
from flask_wtf.csrf import CSRFProtect
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from decimal_utils import DecimalUtils, safe_round_2, safe_round_3, clean_precision, safe_add, safe_subtract
from flask_wtf import FlaskForm
from flask_migrate import Migrate
from flask_paginate import get_page_parameter
from logging.handlers import RotatingFileHandler

from config import config
from models import db, User, Product, Customer, Supplier, SupplierMaterialBalance, OldMaterial, CustomField, Order, OrderItem, Employee, Account, Document, Payment, Purchase, PurchaseItem, Transaction, TransactionItem, SupplierTransaction, MaterialTransaction, MoneyTransaction, MaterialSource, AccountTransaction, MaterialAdjustment, MaterialAdjustmentItem
from forms import ProductForm, CustomerForm, PurchaseForm
from utils import ExcelImporter
from pypinyin import lazy_pinyin, Style
# from init_db import init_app_data, init_products, init_suppliers

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, 'app.log')),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('app')


def calculate_current_period_data(transaction):
    """计算当前交易的本期数据"""
    current_data = {
        'current_owed_gold': 0.0,
        'return_owed_gold': 0.0,
        'current_deposit_gold': 0.0,
        'withdraw_deposit_gold': 0.0,
        'current_owed_amount': 0.0,
        'return_owed_amount': 0.0,
        'current_deposit_amount': 0.0,
        'withdraw_deposit_amount': 0.0
    }

    try:
        # 计算物料交易的影响
        for mt in transaction.material_transactions:
            # 还料：减少欠料
            if mt.return_weight and mt.return_weight > 0 and mt.return_material_type == '旧料足金':
                current_data['return_owed_gold'] += float(mt.return_weight)

            # 存料：增加存料
            if mt.store_weight and mt.store_weight > 0 and mt.store_material_type == '旧料足金':
                current_data['current_deposit_gold'] += float(mt.store_weight)

            # 寄料：减少存料
            if mt.deposit_weight and mt.deposit_weight > 0 and mt.deposit_material_type == '旧料足金':
                current_data['withdraw_deposit_gold'] += float(mt.deposit_weight)

        # 计算款项交易的影响
        for money_tx in transaction.money_transactions:
            # 还款：减少欠款（只计算纯还款，不包括买料）
            if money_tx.return_amount and money_tx.return_amount > 0 and (not money_tx.return_weight or money_tx.return_weight == 0):
                current_data['return_owed_amount'] += float(money_tx.return_amount)

            # 存款：增加存款
            if money_tx.store_amount and money_tx.store_amount > 0:
                current_data['current_deposit_amount'] += float(money_tx.store_amount)

            # 买料：增加欠料和欠款（有return_weight字段的记录）
            if money_tx.return_amount and money_tx.return_amount > 0 and money_tx.return_weight and money_tx.return_weight > 0:
                # 买料增加欠料克重
                if money_tx.return_material_type == '旧料足金':
                    current_data['current_owed_gold'] += float(money_tx.return_weight)
                # 买料增加欠款金额
                current_data['current_owed_amount'] += float(money_tx.return_amount)

        return current_data

    except Exception as e:
        logger.error(f"计算本期数据失败: {str(e)}")
        return current_data


def calculate_total_balance_data(previous_balance, current_period):
    """计算总计数据"""
    return {
        'total_owed_gold': previous_balance['previous_owed_gold'] + current_period['current_owed_gold'] - current_period['return_owed_gold'],
        'total_deposit_gold': previous_balance['previous_deposit_gold'] + current_period['current_deposit_gold'] - current_period['withdraw_deposit_gold'],
        'total_owed_amount': previous_balance['previous_owed_amount'] + current_period['current_owed_amount'] - current_period['return_owed_amount'],
        'total_deposit_amount': previous_balance['previous_deposit_amount'] + current_period['current_deposit_amount'] - current_period['withdraw_deposit_amount']
    }


# 创建应用
app = Flask(__name__)
app.config.from_object(config['development'])
config['development'].init_app(app)

# 初始化 CSRF 保护
csrf = CSRFProtect(app)

# 初始化数据库
db.init_app(app)

# 初始化 Flask-Migrate
migrate = Migrate(app, db)

# 初始化登录管理器
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录'

# 添加全局模板变量
@app.context_processor
def inject_now():
    return {'now': datetime.now()}

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 设置上传文件夹
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# 确保上传文件夹存在
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# 导入宏文件并注册到Jinja2环境中
# 不再需要这个上下文处理器，因为我们在模板中直接导入宏
@app.context_processor
def inject_macros():
    return {}  # 返回空字典，保留函数以避免修改其他代码

# 自定义模板过滤器：确保数值格式与保存的数据一致
@app.template_filter('format_decimal')
def format_decimal(value, decimal_places=2):
    """
    格式化数值，确保小数位数与保存的数据一致
    """
    if value is None:
        return '0.' + '0' * decimal_places

    try:
        # 将值转换为浮点数
        float_value = float(value)
        # 格式化为指定小数位数
        return f"{float_value:.{decimal_places}f}"
    except (ValueError, TypeError):
        return '0.' + '0' * decimal_places

# 首页 - 重定向到订单管理页
@app.route('/')
@login_required
def index():
    # 直接重定向到订单管理页面
    return redirect(url_for('orders'))

# 登录
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    # 创建一个FlaskForm实例用于CSRF保护
    form = FlaskForm()
    
    if request.method == 'POST' and form.validate_on_submit():
        username = request.form.get('username')
        password = request.form.get('password')
        remember = request.form.get('remember', False) == 'on'
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.verify_password(password):
            login_user(user, remember=remember)
            user.last_login = datetime.now()
            db.session.commit()
            
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('index'))
        else:
            flash('用户名或密码错误', 'danger')
    
    return render_template('auth/login.html', form=form)

# 登出
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('您已成功登出', 'success')
    return redirect(url_for('login'))

# 产品列表
@app.route('/products')
@login_required
def products():
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    query = Product.query

    if search:
        query = query.filter(
            Product.name.contains(search) |
            Product.code.contains(search) |
            Product.description.contains(search)
        )

    if category:
        query = query.filter(Product.category == category)

    # 按照编号排序
    pagination = query.order_by(Product.code).paginate(page=page, per_page=per_page)
    products_raw = pagination.items

    # 为每个产品添加初始库存和创建日期信息
    products = []
    for product in products_raw:
        products.append({
            'id': product.id,
            'code': product.code,
            'name': product.name,
            'category': product.category,
            'wholesale_labor_fee': product.wholesale_labor_fee,
            'wholesale_premium_fee': product.wholesale_premium_fee,
            'retail_labor_fee': product.retail_labor_fee,
            'retail_premium_fee': product.retail_premium_fee,
            'initial_stock': float(product.initial_stock or 0),
            'created_at': product.created_at
        })

    return render_template('products/new_list.html',
                          products=products,
                          pagination=pagination,
                          search=search)

# 客户列表
@app.route('/customers')
@login_required
def customers():
    search = request.args.get('search', '')
    customer_type = request.args.get('type', '')
    
    query = Customer.query
    
    if search:
        query = query.filter(Customer.name.contains(search) | Customer.phone.contains(search))
    
    if customer_type:
        query = query.filter(Customer.customer_type == customer_type)
    
    customers = query.all()
    return render_template('customers/index.html', customers=customers)

# 供应商列表
@app.route('/suppliers')
@login_required
def suppliers():
    search = request.args.get('search', '')
    supplier_type = request.args.get('supplier_type', '')

    query = Supplier.query

    if search:
        query = query.filter(Supplier.name.contains(search) | Supplier.contact_person.contains(search))

    if supplier_type:
        query = query.filter(Supplier.supplier_type == supplier_type)

    suppliers_raw = query.all()

    # 为每个供应商计算款料明细
    suppliers = []
    for supplier in suppliers_raw:
        # 从供应商款料明细表计算余额
        from routes.report_routes import calculate_supplier_balances
        balances = calculate_supplier_balances(supplier.id)

        suppliers.append({
            'id': supplier.id,
            'name': supplier.name,
            'code': supplier.code,
            'supplier_type': supplier.supplier_type,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'address': supplier.address,
            'email': supplier.email,
            'owed_gold': balances.get('owed_gold', 0),
            'deposit_gold': balances.get('deposit_gold', 0),
            'owed_amount': balances.get('owed_amount', 0),
            'deposit_amount': balances.get('deposit_amount', 0),
            'created_at': supplier.created_at,
            'is_active': supplier.is_active
        })

    return render_template('suppliers/index.html', suppliers=suppliers)

# 旧料管理
@app.route('/old_materials')
@login_required
def old_materials():
    from routes.report_routes import calculate_old_material_inventory_at_date
    from datetime import date

    materials = OldMaterial.query.order_by(OldMaterial.code).all()

    # 为每个旧料计算当前库存
    materials_with_stock = []
    for material in materials:
        current_stock = calculate_old_material_inventory_at_date(material, date.today())
        materials_with_stock.append({
            'id': material.id,
            'code': material.code,
            'name': material.name,
            'initial_stock': float(material.initial_stock or 0),
            'department_stock': float(current_stock),
            'is_active': material.is_active,
            'created_at': material.created_at
        })

    return render_template('old_materials/index.html', materials=materials_with_stock)

# 添加旧料删除路由
@app.route('/old_material/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def old_material_delete(id):
    old_material = OldMaterial.query.get_or_404(id)

    try:
        # 检查是否有关联的订单项（旧料项目）
        order_items = OrderItem.query.filter_by(
            item_type='old', material_name=old_material.name
        ).all()
        if order_items:
            order_count = len(order_items)
            flash(f'该旧料已有关联的{order_count}个订单项目，不能删除', 'danger')
            app.logger.info(f"旧料 {old_material.name} 有 {order_count} 个关联订单项目")
            return redirect(url_for('old_materials'))

        # 检查是否有关联的供应商往来记录
        material_transactions = MaterialTransaction.query.filter(
            (MaterialTransaction.return_material_type == old_material.name) |
            (MaterialTransaction.store_material_type == old_material.name) |
            (MaterialTransaction.deposit_material_type == old_material.name)
        ).all()

        # 清理孤立的MaterialTransaction记录（transaction_id指向不存在的SupplierTransaction）
        orphaned_transactions = []
        valid_transactions = []

        for trans in material_transactions:
            supplier_trans = SupplierTransaction.query.get(trans.transaction_id)
            if supplier_trans:
                valid_transactions.append(trans)
                app.logger.info(f"有效关联记录ID: {trans.id}, 供应商往来编号: {supplier_trans.transaction_no}")
            else:
                orphaned_transactions.append(trans)
                app.logger.info(f"发现孤立记录ID: {trans.id}, transaction_id: {trans.transaction_id} (对应的SupplierTransaction不存在)")

        # 删除孤立记录
        if orphaned_transactions:
            for trans in orphaned_transactions:
                app.logger.info(f"删除孤立的MaterialTransaction记录: ID={trans.id}")
                db.session.delete(trans)
            db.session.commit()
            app.logger.info(f"已清理 {len(orphaned_transactions)} 个孤立的MaterialTransaction记录")

        # 检查是否还有有效的关联记录
        if valid_transactions:
            transaction_count = len(valid_transactions)
            flash(f'该旧料已有关联的{transaction_count}个供应商往来记录，不能删除', 'danger')
            app.logger.info(f"旧料 {old_material.name} 有 {transaction_count} 个有效关联供应商往来记录")
            return redirect(url_for('old_materials'))

        # 检查是否有关联的货料调整记录
        material_adjustments = MaterialAdjustmentItem.query.filter_by(
            target_type='old_material', target_name=old_material.name
        ).all()
        if material_adjustments:
            adjustment_count = len(material_adjustments)
            flash(f'该旧料已有关联的{adjustment_count}个货料调整记录，不能删除', 'danger')
            app.logger.info(f"旧料 {old_material.name} 有 {adjustment_count} 个关联货料调整记录")
            return redirect(url_for('old_materials'))

        # 如果没有任何关联记录，则可以删除
        app.logger.info(f"删除旧料: {id}")
        db.session.delete(old_material)
        db.session.commit()
        flash('旧料已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除旧料失败: {str(e)}")
        flash(f'删除旧料失败: {str(e)}', 'danger')

    return redirect(url_for('old_materials'))

@app.route('/old_material/add', methods=['GET', 'POST'])
@login_required
def old_material_add():
    if request.method == 'POST':
        try:
            name = request.form['name']
            if not name:
                flash('旧料名称不能为空', 'danger')
                return redirect(url_for('old_material_add'))
            
            code = request.form.get('code', '')
            if not code:
                # 如果code为空，生成一个唯一的code
                import random
                code = f"OM{random.randint(10000, 99999)}"
                # 确保code唯一
                while OldMaterial.query.filter_by(code=code).first() is not None:
                    code = f"OM{random.randint(10000, 99999)}"
            else:
                # 检查code是否已存在
                if OldMaterial.query.filter_by(code=code).first() is not None:
                    flash('旧料编号已存在，请使用其他编号', 'danger')
                    return redirect(url_for('old_material_add'))

            # 检查名称是否重复
            if OldMaterial.query.filter_by(name=name).first() is not None:
                flash('旧料名称已存在，请使用其他名称', 'danger')
                return redirect(url_for('old_material_add'))
            
            initial_stock_str = request.form.get('initial_stock', '0')  # 修改为正确的字段名
            initial_stock = float(initial_stock_str) if initial_stock_str else 0

            # 创建旧料记录
            new_material = OldMaterial(
                name=name,
                code=code,
                initial_stock=initial_stock,  # 料部初始库存（固定值）
                is_active=True
            )
            
            db.session.add(new_material)
            db.session.commit()
            
            flash('旧料添加成功', 'success')
            return redirect(url_for('old_materials'))
        except Exception as e:
            db.session.rollback()
            flash(f'添加旧料失败: {str(e)}', 'danger')
            return redirect(url_for('old_material_add'))
    
    return render_template('old_materials/add.html')



# 用户管理路由已移至 routes/user_routes.py

# 基本信息中心
@app.route('/data_management')
@login_required
def data_management():
    """基本信息中心页面"""
    return render_template('data_management/index.html')

# 系统设置
@app.route('/settings')
@login_required
def settings():
    if not current_user.is_admin():
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('index'))

    return render_template('settings/index.html')

# 显示设置
@app.route('/display_settings')
@login_required
def display_settings():
    """显示设置页面"""
    return render_template('system/display_settings.html')

# 自定义字段管理
@app.route('/custom_fields')
@login_required
def custom_fields():
    if not current_user.is_admin():
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('index'))
    
    fields = CustomField.query.all()
    return render_template('settings/custom_fields.html', fields=fields)

# 订单管理
@app.route('/orders')
@login_required
def orders():
    # 获取搜索参数
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    
    # 获取当前日期
    today = datetime.now().date()
    today_str = today.strftime('%Y-%m-%d')
    
    # 从URL参数获取日期范围
    start_date = request.args.get('start_date', today_str)
    end_date = request.args.get('end_date', start_date)
    
    # 分页参数
    page = request.args.get('page', 1, type=int)
    per_page = 100
    
    # 构建基本查询，使用join预加载客户信息以避免N+1查询
    query = Order.query.join(Customer, Order.customer_id == Customer.id, isouter=True)

    # 过滤未删除的订单
    query = query.filter(Order.is_deleted == False)
    
    # 搜索条件（优化后的查询，使用已join的表）
    if search:
        search_term = f'%{search}%'
        query = query.filter(or_(
            Order.order_no.ilike(search_term),
            Customer.name.ilike(search_term)
        ))
    
    # 状态条件
    if status:
        query = query.filter(Order.status == status)
    
    # 日期筛选 - 使用业务时间order_date字段
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(Order.order_date >= start_date_obj)
        except Exception as e:
            app.logger.error(f"无效的开始日期: {start_date}, 错误: {e}")

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
            end_date_obj = datetime.combine(end_date_obj.date(), time.max)  # 设置为当天的最后一秒
            query = query.filter(Order.order_date <= end_date_obj)
        except Exception as e:
            app.logger.error(f"无效的结束日期: {end_date}, 错误: {e}")
            
    # 按业务时间排序（降序，最新的在前面）
    query = query.order_by(Order.order_date.desc(), Order.id.desc())
    
    # 查询并分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    orders = pagination.items
    
    # 计算订单序号（基于业务时间）
    all_orders = query.all()
    orders_by_date = {}
    for order in all_orders:
        # 使用业务时间的日期部分
        order_date = order.order_date.date() if order.order_date else order.created_at.date()
        if order_date not in orders_by_date:
            orders_by_date[order_date] = []
        orders_by_date[order_date].append(order)

    order_numbers = {}
    for date, date_orders in orders_by_date.items():
        # 按业务时间排序同一天的订单
        date_orders.sort(key=lambda x: x.order_date if x.order_date else x.created_at)
        for i, order in enumerate(date_orders, 1):
            order_numbers[order.id] = i
    
    # 计算今日统计数据
    today_start = datetime.combine(today, time.min)
    today_end = datetime.combine(today, time.max)
    
    today_orders_query = Order.query.filter(
        and_(
            Order.created_at >= today_start,
            Order.created_at <= today_end,
            Order.is_deleted == False
        )
    )
    
    today_orders = today_orders_query.all()
    
    stats = {
        'order_count': len(today_orders),
        'today_sales': sum(order.total_amount for order in today_orders if order.total_amount),
        'today_gold_out': sum(order.gold_out for order in today_orders if order.gold_out),
        'today_gold_return': sum(order.gold_return for order in today_orders if order.gold_return),
        'today_gold_recycle': sum(order.gold_recycle for order in today_orders if order.gold_recycle),
        'today_unpaid_count': sum(1 for order in today_orders if not order.is_paid)
    }
    
    return render_template('orders/index.html', 
                         orders=orders,
                         order_numbers=order_numbers,
                         pagination=pagination,
                         search=search,
                         start_date=start_date,
                         end_date=end_date,
                         stats=stats)

@app.route('/admin/order_chain_management')
@login_required
def order_chain_management():
    """订单链管理页面"""
    return render_template('admin/order_chain_management.html')

@app.route('/orders/<int:id>')
@login_required
def order_view(id):
    """查看订单详情"""
    return redirect(url_for('order_view_edit', id=id))



# 创建客户
@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def customer_add():
    form = CustomerForm()
    if request.method == 'POST':
        try:
            # 生成客户编号
            last_customer = Customer.query.order_by(Customer.id.desc()).first()
            if last_customer:
                last_code = last_customer.code
                if last_code and last_code.startswith('C'):
                    code_number = int(last_code[1:])
                    new_code = f'C{code_number + 1:04d}'
                else:
                    new_code = 'C0001'
            else:
                new_code = 'C0001'

            # 检查客户名称是否重复（只检查活跃客户）
            customer_name = request.form['name']
            if Customer.query.filter_by(name=customer_name, is_active=True).first():
                flash('客户名称已存在，请使用其他名称', 'danger')
                return render_template('customers/add.html', form=form)

            # 获取初始款项金额
            initial_owed_amount = float(request.form.get('balance', 0))
            initial_deposit_amount = float(request.form.get('deposit_amount', 0))

            # 创建新客户（包含初始款项字段）
            customer = Customer(
                code=new_code,
                name=customer_name,
                customer_type=request.form.get('customer_type', 'retail'),
                phone=request.form.get('phone', ''),
                address=request.form.get('address', ''),
                initial_owed_amount=initial_owed_amount,
                initial_deposit_amount=initial_deposit_amount
            )

            # 更新拼音字段
            try:
                from pypinyin import lazy_pinyin, Style

                # 获取全拼
                pinyin_list = lazy_pinyin(customer.name)
                customer.pinyin = ''.join(pinyin_list)

                # 获取首字母
                pinyin_initials = lazy_pinyin(customer.name, style=Style.FIRST_LETTER)
                customer.pinyin_initials = ''.join(pinyin_initials)
            except Exception as e:
                print(f"更新客户拼音字段时出错: {e}")

            db.session.add(customer)
            db.session.flush()  # 获取customer.id

            # 处理所有旧料的初始值（包括旧料足金）
            from models import CustomerMaterialBalance, OldMaterial

            # 获取所有旧料
            all_materials = OldMaterial.query.filter_by(is_active=True).all()

            for material in all_materials:
                # 所有旧料（包括旧料足金）都从表单获取初始值
                owed_key = f'material_{material.name}_owed'
                stored_key = f'material_{material.name}_stored'
                owed_weight = float(request.form.get(owed_key, 0))
                stored_weight = float(request.form.get(stored_key, 0))

                # 为所有旧料类型创建记录，即使值为0（用于客户款料明细显示）
                balance = CustomerMaterialBalance(
                    customer_id=customer.id,
                    material_name=material.name,
                    owed_weight=owed_weight,
                    stored_weight=stored_weight
                )
                db.session.add(balance)

            db.session.commit()
            flash('客户添加成功！', 'success')
            return redirect(url_for('customers'))
        except Exception as e:
            db.session.rollback()
            flash(f'添加客户时出错: {str(e)}', 'danger')
    
    return render_template('customers/add.html', form=form)



# 创建产品
@app.route('/products/create', methods=['GET', 'POST'])
@login_required
def create_product():
    form = ProductForm()

    if form.validate_on_submit():
        # 检查编号是否重复
        existing_product_by_code = Product.query.filter_by(code=form.code.data).first()
        if existing_product_by_code:
            flash('产品编号已存在，请使用其他编号', 'danger')
            return render_template('products/create.html', form=form)

        # 检查名称是否重复
        existing_product_by_name = Product.query.filter_by(name=form.name.data).first()
        if existing_product_by_name:
            flash('产品名称已存在，请使用其他名称', 'danger')
            return render_template('products/create.html', form=form)

        product = Product(
            code=form.code.data,
            name=form.name.data,
            category=form.category.data,
            unit=form.unit.data,
            wholesale_labor_fee=form.wholesale_labor_fee.data,
            wholesale_premium_fee=form.wholesale_premium_fee.data,
            retail_labor_fee=form.retail_labor_fee.data,
            retail_premium_fee=form.retail_premium_fee.data,
            stock=form.initial_stock.data,  # 设置当前库存等于初始库存
            initial_stock=form.initial_stock.data  # 设置初始库存
        )

        try:
            db.session.add(product)
            db.session.commit()
            flash('产品创建成功！', 'success')
            return redirect(url_for('products'))
        except Exception as e:
            db.session.rollback()
            app.logger.error(f"创建产品失败: {str(e)}")
            flash(f'创建产品失败: {str(e)}', 'danger')

    return render_template('products/create.html', form=form)



@app.route('/products/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_product(id):
    product = Product.query.get_or_404(id)
    form = ProductForm()

    # 手动预填充表单数据
    if not form.is_submitted():
        form.code.data = product.code
        form.name.data = product.name
        form.category.data = product.category
        form.unit.data = product.unit
        form.wholesale_labor_fee.data = product.wholesale_labor_fee
        form.wholesale_premium_fee.data = product.wholesale_premium_fee
        form.retail_labor_fee.data = product.retail_labor_fee
        form.retail_premium_fee.data = product.retail_premium_fee
        form.initial_stock.data = product.initial_stock

    if form.validate_on_submit():
        # 只允许修改工费相关字段，其他字段保持不变
        # 不允许修改：code, name, category, unit, initial_stock
        product.wholesale_labor_fee = form.wholesale_labor_fee.data
        product.wholesale_premium_fee = form.wholesale_premium_fee.data
        product.retail_labor_fee = form.retail_labor_fee.data
        product.retail_premium_fee = form.retail_premium_fee.data
        product.updated_at = datetime.now()

        # 注意：以下字段不允许修改，保持原值
        # - product.code (产品编号)
        # - product.name (产品名称)
        # - product.category (分类)
        # - product.unit (单位)
        # - product.initial_stock (初始库存)
        # - product.stock (当前库存，由系统计算)

        try:
            db.session.commit()
            flash('产品更新成功！', 'success')
            return redirect(url_for('products'))
        except Exception as e:
            db.session.rollback()
            app.logger.error(f"更新产品失败: {str(e)}")
            flash(f'更新产品失败: {str(e)}', 'danger')

    return render_template('products/edit.html', form=form, product=product)

@app.route('/products/<int:id>/delete', methods=['GET', 'POST'])
@login_required
def delete_product(id):
    """删除产品"""
    try:
        product = Product.query.get_or_404(id)

        # 检查是否有关联的订单项
        has_order_items = OrderItem.query.filter_by(product_id=id).first() is not None
        if has_order_items:
            flash('该产品已有关联的订单，不能删除', 'danger')
            return redirect(url_for('products'))

        # 检查是否有关联的采购单据
        has_purchase_items = PurchaseItem.query.filter_by(product_id=id).first() is not None
        if has_purchase_items:
            flash('该产品已有关联的采购单据，不能删除', 'danger')
            return redirect(url_for('products'))

        # 检查是否有关联的货料调整记录
        has_material_adjustments = MaterialAdjustmentItem.query.filter_by(
            target_type='product', target_name=product.name
        ).first() is not None
        if has_material_adjustments:
            flash('该产品已有关联的货料调整记录，不能删除', 'danger')
            return redirect(url_for('products'))

        # 如果没有任何关联记录，则可以删除
        db.session.delete(product)
        db.session.commit()
        flash('产品已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除产品失败: {str(e)}', 'danger')

    return redirect(url_for('products'))

@app.route('/products/import', methods=['POST'])
@login_required
def import_products():
    if 'file' not in request.files:
        flash('没有选择文件', 'danger')
        return redirect(url_for('products'))
    
    file = request.files['file']
    
    if file.filename == '':
        flash('没有选择文件', 'danger')
        return redirect(url_for('products'))
    
    importer = ExcelImporter(file, ALLOWED_EXTENSIONS)
    
    if not importer.allowed_file():
        flash('不支持的文件类型，请上传Excel文件(.xlsx, .xls)', 'danger')
        return redirect(url_for('products'))
    
    try:
        file_path = importer.save_file(app.config['UPLOAD_FOLDER'])
        
        if not importer.read_excel(file_path):
            flash(f'读取Excel文件失败: {", ".join(importer.errors)}', 'danger')
            return redirect(url_for('products'))
        
        required_headers = ['产品编号', '产品名称', '分类', '库存']
        
        if not importer.validate_headers(required_headers):
            flash(f'Excel文件格式不正确: {", ".join(importer.errors)}', 'danger')
            return redirect(url_for('products'))
        
        def process_product(row):
            # 检查产品编号是否已存在
            existing_product = Product.query.filter_by(code=row['产品编号']).first()
            
            if existing_product:
                # 更新现有产品（不再更新库存，库存由系统正向计算）
                existing_product.name = row['产品名称']
                existing_product.category = row['分类']
                existing_product.unit = row.get('单位', '')
                existing_product.wholesale_labor_fee = float(row.get('批发工费', 0))
                existing_product.wholesale_premium_fee = float(row.get('批发精品费', 0))
                existing_product.retail_labor_fee = float(row.get('零售工费', 0))
                existing_product.retail_premium_fee = float(row.get('零售精品费', 0))
                # 如果导入数据包含初始库存，则更新初始库存
                if '初始库存' in row:
                    existing_product.initial_stock = float(row['初始库存'])
                existing_product.updated_at = datetime.now()
            else:
                # 创建新产品
                initial_stock = float(row.get('初始库存', row.get('库存', 0)))
                new_product = Product(
                    code=row['产品编号'],
                    name=row['产品名称'],
                    category=row['分类'],
                    unit=row.get('单位', ''),
                    wholesale_labor_fee=float(row.get('批发工费', 0)),
                    wholesale_premium_fee=float(row.get('批发精品费', 0)),
                    retail_labor_fee=float(row.get('零售工费', 0)),
                    retail_premium_fee=float(row.get('零售精品费', 0)),
                    stock=initial_stock,  # 设置当前库存等于初始库存
                    initial_stock=initial_stock  # 设置初始库存
                )
                db.session.add(new_product)
        
        importer.process_data(process_product)
        result = importer.get_result()
        
        if result['success']:
            db.session.commit()
            flash(f'成功导入 {result["success_count"]} 个产品，失败 {result["error_count"]} 个', 'success')
        else:
            db.session.rollback()
            error_msg = ", ".join(result['errors'][:5])
            if len(result['errors']) > 5:
                error_msg += f"...等共 {len(result['errors'])} 个错误"
            flash(f'导入失败: {error_msg}', 'danger')
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"导入产品失败: {str(e)}")
        flash(f'导入产品失败: {str(e)}', 'danger')
    
    return redirect(url_for('products'))

@app.route('/products/export')
@login_required
def export_products():
    # 获取查询参数
    search = request.args.get('search', '')
    
    # 构建查询
    query = Product.query
    
    if search:
        query = query.filter(
            (Product.name.ilike(f'%{search}%')) | 
            (Product.code.ilike(f'%{search}%'))
        )
    
    # 获取所有产品
    products = query.order_by(Product.updated_at.desc()).all()
    
    # 创建DataFrame
    data = []
    for product in products:
        data.append({
            '产品编号': product.code,
            '产品名称': product.name,
            '分类': product.category,
            '单位': product.unit,
            '批发工费': product.wholesale_labor_fee,
            '批发精品费': product.wholesale_premium_fee,
            '零售工费': product.retail_labor_fee,
            '零售精品费': product.retail_premium_fee,
            '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            '更新时间': product.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    df = pd.DataFrame(data)
    
    # 创建Excel文件
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='产品列表')
        worksheet = writer.sheets['产品列表']
        
        # 调整列宽
        for i, col in enumerate(df.columns):
            max_len = max(df[col].astype(str).map(len).max(), len(col)) + 2
            worksheet.set_column(i, i, max_len)
    
    output.seek(0)
    
    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f"products_export_{timestamp}.xlsx"
    
    return send_file(
        output,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@app.route('/download_import_template')
@login_required
def download_import_template():
    """下载包含现有数据的导入模板"""
    import pandas as pd
    import io
    from datetime import datetime

    # 创建Excel文件
    output = io.BytesIO()

    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        # 导出现有产品数据
        products = Product.query.filter_by(is_active=True).order_by(Product.code).all()
        products_data = []
        for product in products:
            products_data.append({
                '产品编号': product.code,
                '产品名称': product.name,
                '分类': product.category or '',
                '单位': product.unit or '',
                '库存': product.stock or 0,
                '批发工费': product.wholesale_labor_fee or 0,
                '批发精品费': product.wholesale_premium_fee or 0,
                '零售工费': product.retail_labor_fee or 0,
                '零售精品费': product.retail_premium_fee or 0
            })
        pd.DataFrame(products_data).to_excel(writer, index=False, sheet_name='产品')

        # 导出现有客户数据
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.code).all()
        customers_data = []
        for customer in customers:
            customers_data.append({
                '客户编号': customer.code,
                '客户名称': customer.name,
                '客户类别': customer.customer_type or '',
                '联系电话': customer.phone or '',
                '地址': customer.address or '',
                '邮箱': customer.email or ''
            })
        pd.DataFrame(customers_data).to_excel(writer, index=False, sheet_name='客户')

        # 导出现有旧料数据
        materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code).all()
        materials_data = []
        for material in materials:
            materials_data.append({
                '旧料编号': material.code,
                '旧料名称': material.name,
                '初始库存': material.initial_stock or 0,
                '描述': material.description or ''
            })
        pd.DataFrame(materials_data).to_excel(writer, index=False, sheet_name='旧料')

        # 导出现有供应商数据
        suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.code).all()
        suppliers_data = []
        for supplier in suppliers:
            suppliers_data.append({
                '供应商编号': supplier.code,
                '供应商名称': supplier.name,
                '供应商类别': supplier.supplier_type or '',
                '联系人': supplier.contact_person or '',
                '联系电话': supplier.phone or '',
                '地址': supplier.address or '',
                '邮箱': supplier.email or ''
            })
        pd.DataFrame(suppliers_data).to_excel(writer, index=False, sheet_name='供应商')

        # 导出现有员工数据
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.name).all()
        employees_data = []
        for employee in employees:
            employees_data.append({
                '员工姓名': employee.name,
                '性别': employee.gender or '',
                '联系电话': employee.phone or '',
                '职位': employee.position or '',
                '薪资': employee.salary or 0,
                '入职日期': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '',
                '地址': employee.address or '',
                '备注': employee.notes or ''
            })
        pd.DataFrame(employees_data).to_excel(writer, index=False, sheet_name='员工')

        # 导出现有账户数据
        accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
        accounts_data = []
        for account in accounts:
            accounts_data.append({
                '账户名称': account.name,
                '账户类型': account.account_type or '',
                '银行名称': account.bank_name or '',
                '账户号码': account.account_number or '',
                '初始余额': account.initial_balance or 0,
                '当前余额': account.balance or 0,
                '备注': account.notes or ''
            })
        pd.DataFrame(accounts_data).to_excel(writer, index=False, sheet_name='账户')

    output.seek(0)

    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f"data_template_{timestamp}.xlsx"

    return send_file(
        output,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@app.route('/export_all_data')
@login_required
def export_all_data():
    """导出所有基础信息数据"""
    import pandas as pd
    import io
    from datetime import datetime

    # 创建Excel文件
    output = io.BytesIO()

    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        # 导出产品数据
        products = Product.query.filter_by(is_active=True).order_by(Product.code).all()
        products_data = []
        for product in products:
            products_data.append({
                '产品编号': product.code,
                '产品名称': product.name,
                '分类': product.category,
                '单位': product.unit,
                '库存': product.stock,
                '批发工费': product.wholesale_labor_fee,
                '批发精品费': product.wholesale_premium_fee,
                '零售工费': product.retail_labor_fee,
                '零售精品费': product.retail_premium_fee,
                '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                '更新时间': product.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        pd.DataFrame(products_data).to_excel(writer, index=False, sheet_name='产品')

        # 导出客户数据
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.code).all()
        customers_data = []
        for customer in customers:
            customers_data.append({
                '客户编号': customer.code,
                '客户名称': customer.name,
                '客户类别': customer.customer_type,
                '联系电话': customer.phone,
                '地址': customer.address,
                '邮箱': customer.email,
                '创建时间': customer.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                '更新时间': customer.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        pd.DataFrame(customers_data).to_excel(writer, index=False, sheet_name='客户')

        # 导出旧料数据
        materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code).all()
        materials_data = []
        for material in materials:
            materials_data.append({
                '旧料编号': material.code,
                '旧料名称': material.name,
                '初始库存': material.initial_stock,
                '描述': material.description,
                '创建时间': material.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                '更新时间': material.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        pd.DataFrame(materials_data).to_excel(writer, index=False, sheet_name='旧料')

        # 导出供应商数据
        suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.code).all()
        suppliers_data = []
        for supplier in suppliers:
            suppliers_data.append({
                '供应商编号': supplier.code,
                '供应商名称': supplier.name,
                '供应商类别': supplier.supplier_type,
                '联系人': supplier.contact_person,
                '联系电话': supplier.phone,
                '地址': supplier.address,
                '邮箱': supplier.email,
                '创建时间': supplier.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                '更新时间': supplier.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        pd.DataFrame(suppliers_data).to_excel(writer, index=False, sheet_name='供应商')

        # 导出员工数据
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.name).all()
        employees_data = []
        for employee in employees:
            employees_data.append({
                '员工姓名': employee.name,
                '性别': employee.gender or '',
                '联系电话': employee.phone or '',
                '职位': employee.position or '',
                '薪资': employee.salary or 0,
                '入职日期': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '',
                '地址': employee.address or '',
                '备注': employee.notes or '',
                '创建时间': employee.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                '更新时间': employee.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        pd.DataFrame(employees_data).to_excel(writer, index=False, sheet_name='员工')

        # 导出账户数据
        accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
        accounts_data = []
        for account in accounts:
            accounts_data.append({
                '账户名称': account.name,
                '账户类型': account.account_type or '',
                '银行名称': account.bank_name or '',
                '账户号码': account.account_number or '',
                '初始余额': account.initial_balance or 0,
                '当前余额': account.balance or 0,
                '备注': account.notes or '',
                '创建时间': account.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                '更新时间': account.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        pd.DataFrame(accounts_data).to_excel(writer, index=False, sheet_name='账户')

    output.seek(0)

    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f"all_data_export_{timestamp}.xlsx"

    return send_file(
        output,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

# 仪表盘（作为index的别名）
@app.route('/dashboard')
@login_required
def dashboard():
    # 重定向到首页
    return redirect(url_for('index'))

# 报表路由
@app.route('/reports')
@login_required
def reports():
    flash('报表功能正在开发中', 'info')
    return redirect(url_for('index'))

# 员工信息
@app.route('/employees')
@login_required
def employees():
    search = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)
    
    query = Employee.query
    
    if search:
        query = query.filter(
            Employee.name.contains(search) | 
            Employee.phone.contains(search) | 
            Employee.position.contains(search)
        )
    
    pagination = query.paginate(page=page, per_page=20, error_out=False)
    employees = pagination.items
    
    return render_template('employees/index.html', employees=employees, pagination=pagination)

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def employee_add():
    if request.method == 'POST':
        try:
            name = request.form['name']
            if not name:
                flash('员工姓名不能为空', 'danger')
                return redirect(url_for('employee_add'))

            # 检查员工姓名是否重复
            if Employee.query.filter_by(name=name).first():
                flash('员工姓名已存在，请使用其他姓名', 'danger')
                return redirect(url_for('employee_add'))

            # 获取基本信息
            gender = request.form.get('gender', 'male')
            phone = request.form.get('phone', '')
            position = request.form.get('position', '')
            address = request.form.get('address', '')
            
            # 处理数值字段
            salary_str = request.form.get('salary', '0')
            salary = float(salary_str) if salary_str else 0
            
            # 处理日期字段
            hire_date_str = request.form.get('hire_date', '')
            hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d') if hire_date_str else datetime.now()
            
            leave_date_str = request.form.get('leave_date', '')
            leave_date = datetime.strptime(leave_date_str, '%Y-%m-%d') if leave_date_str else None
            
            # 创建员工记录
            employee = Employee(
                name=name,
                gender=gender,
                phone=phone,
                position=position,
                salary=salary,
                hire_date=hire_date,
                leave_date=leave_date,
                address=address,
                is_active=True
            )
            
            db.session.add(employee)
            db.session.commit()

            # 自动为员工创建用户账户
            from routes.user_routes import create_user_for_employee
            create_user_for_employee(employee)

            flash('员工添加成功，已自动创建对应用户账户', 'success')
            return redirect(url_for('employees'))
        except ValueError as e:
            db.session.rollback()
            if "does not match format" in str(e):
                flash('日期格式错误，请使用YYYY-MM-DD格式', 'danger')
            else:
                flash(f'添加员工失败: {str(e)}', 'danger')
            return redirect(url_for('employee_add'))
        except Exception as e:
            db.session.rollback()
            flash(f'添加员工失败: {str(e)}', 'danger')
            return redirect(url_for('employee_add'))
    
    return render_template('employees/add.html')

@app.route('/employees/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def employee_edit(id):
    employee = Employee.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            employee_name = request.form['name']

            # 检查员工姓名是否重复（排除当前员工）
            existing_employee = Employee.query.filter(
                Employee.name == employee_name,
                Employee.id != employee.id
            ).first()
            if existing_employee:
                flash('员工姓名已存在，请使用其他姓名', 'danger')
                return render_template('employees/edit.html', employee=employee)

            employee.name = employee_name
            employee.gender = request.form.get('gender', 'male')
            employee.phone = request.form.get('phone', '')
            employee.position = request.form.get('position', '')
            employee.address = request.form.get('address', '')
            
            # 处理数值字段
            salary_str = request.form.get('salary', '0')
            employee.salary = float(salary_str) if salary_str else 0
            
            # 处理日期字段
            hire_date_str = request.form.get('hire_date', '')
            employee.hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d') if hire_date_str else employee.hire_date
            
            leave_date_str = request.form.get('leave_date', '')
            employee.leave_date = datetime.strptime(leave_date_str, '%Y-%m-%d') if leave_date_str else None
            
            db.session.commit()
            flash('员工信息已更新', 'success')
            return redirect(url_for('employees'))
        except ValueError as e:
            db.session.rollback()
            if "does not match format" in str(e):
                flash('日期格式错误，请使用YYYY-MM-DD格式', 'danger')
            else:
                flash(f'更新员工失败: {str(e)}', 'danger')
            return redirect(url_for('employee_edit', id=id))
        except Exception as e:
            db.session.rollback()
            flash(f'更新员工失败: {str(e)}', 'danger')
            return redirect(url_for('employee_edit', id=id))
    
    return render_template('employees/edit.html', employee=employee)

@app.route('/employees/<int:id>/delete', methods=['GET', 'POST'])
@login_required
def employee_delete(id):
    """删除员工"""
    try:
        employee = Employee.query.get_or_404(id)
        
        db.session.delete(employee)
        db.session.commit()
        flash('员工已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除员工失败: {str(e)}', 'danger')
    
    return redirect(url_for('employees'))

# 账户信息
@app.route('/accounts')
@login_required
def accounts():
    search = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)

    query = Account.query

    if search:
        query = query.filter(
            Account.name.contains(search) |
            Account.account_number.contains(search) |
            Account.bank_name.contains(search)
        )

    pagination = query.paginate(page=page, per_page=20, error_out=False)
    accounts_raw = pagination.items

    # 使用账户余额概览的计算方法获取当前余额
    from datetime import datetime
    today = datetime.now().strftime('%Y-%m-%d')
    account_balances = calculate_period_account_balances(today, today, accounts_raw)

    # 创建账户ID到期末余额的映射
    balance_map = {acc_data['id']: acc_data['final_balance'] for acc_data in account_balances}

    # 为每个账户添加计算出的当前余额
    accounts = []
    for account in accounts_raw:
        # 创建一个新的账户对象副本，添加计算出的余额
        account_dict = {
            'id': account.id,
            'name': account.name,
            'bank_name': account.bank_name,
            'account_number': account.account_number,
            'initial_balance': account.initial_balance,
            'balance': balance_map.get(account.id, 0),  # 使用计算出的期末余额作为当前余额
            'notes': account.notes,
            'is_active': account.is_active,
            'created_at': account.created_at
        }
        accounts.append(type('Account', (), account_dict)())

    return render_template('accounts/index.html', accounts=accounts, pagination=pagination)

@app.route('/api/check_missing_account_transactions')
@login_required
def check_missing_account_transactions():
    """检查缺失的账户交易记录"""
    try:
        missing_records = []

        # 1. 检查客户订单的支付记录
        from models import Payment, Order, Customer
        payments = db.session.query(Payment, Order, Customer).join(
            Order, Payment.order_id == Order.id
        ).join(
            Customer, Order.customer_id == Customer.id
        ).all()

        for payment, order, customer in payments:
            # 检查是否有对应的AccountTransaction记录
            account_trans = AccountTransaction.query.filter_by(
                order_id=order.id,
                account_id=payment.account_id
            ).first()

            if not account_trans:
                missing_records.append({
                    'type': '客户订单支付',
                    'document_no': order.order_no,
                    'customer_name': customer.name,
                    'amount': payment.amount,
                    'account_id': payment.account_id,
                    'date': order.created_at.strftime('%Y-%m-%d') if order.created_at else '未知'
                })

        # 2. 检查供应商往来单据
        supplier_transactions = SupplierTransaction.query.all()
        for st in supplier_transactions:
            # 检查是否有对应的AccountTransaction记录
            account_trans = AccountTransaction.query.filter_by(
                supplier_transaction_id=st.id
            ).first()

            if not account_trans:
                # 检查是否有涉及账户的交易
                money_trans = MoneyTransaction.query.filter_by(transaction_id=st.id).all()
                for mt in money_trans:
                    if mt.account_name:
                        missing_records.append({
                            'type': '供应商往来',
                            'document_no': st.transaction_no,
                            'supplier_name': st.supplier.name if st.supplier else '未知',
                            'amount': mt.return_amount or 0,
                            'account_name': mt.account_name,
                            'date': st.business_date.strftime('%Y-%m-%d') if st.business_date else '未知'
                        })

        # 3. 检查其他项目往来单据
        other_transactions = Transaction.query.all()
        for ot in other_transactions:
            if ot.account_id:
                # 检查是否有对应的AccountTransaction记录
                account_trans = AccountTransaction.query.filter_by(
                    account_id=ot.account_id,
                    related_document=ot.transaction_no
                ).first()

                if not account_trans:
                    missing_records.append({
                        'type': '其他项目往来',
                        'document_no': ot.transaction_no,
                        'customer_name': ot.customer.name if ot.customer else '未知',
                        'amount': ot.amount or 0,
                        'account_id': ot.account_id,
                        'date': ot.business_time.strftime('%Y-%m-%d') if ot.business_time else '未知'
                    })

        return jsonify({
            'success': True,
            'missing_count': len(missing_records),
            'missing_records': missing_records[:50]  # 限制返回前50条
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查失败: {str(e)}'
        })

@app.route('/test_fixes')
@login_required
def test_fixes():
    """测试修复功能页面"""
    from models import Customer
    customers = Customer.query.filter_by(is_active=True).all()
    return render_template('test_fixes.html', customers=customers)

@app.route('/test_api_frontend')
@login_required
def test_api_frontend():
    """测试API页面"""
    from flask import send_from_directory
    return send_from_directory('.', 'test_api_frontend.html')

@app.route('/api/fix_missing_account_transactions', methods=['POST'])
@login_required
def fix_missing_account_transactions():
    """修复缺失的账户交易记录"""
    try:
        fixed_count = 0

        # 1. 修复客户订单的支付记录
        from models import Payment, Order, Customer
        payments = db.session.query(Payment, Order, Customer).join(
            Order, Payment.order_id == Order.id
        ).join(
            Customer, Order.customer_id == Customer.id
        ).all()

        for payment, order, customer in payments:
            # 检查是否已有对应的AccountTransaction记录
            existing = AccountTransaction.query.filter_by(
                order_id=order.id,
                account_id=payment.account_id
            ).first()

            if not existing and payment.account_id:
                # 创建缺失的账户交易记录
                account_transaction = AccountTransaction(
                    account_id=payment.account_id,
                    transaction_date=order.created_at.date() if order.created_at else datetime.now().date(),
                    amount=payment.amount,
                    transaction_type='income',
                    purpose='销售收款',
                    related_party=customer.name,
                    related_document=order.order_no,
                    notes=order.notes or '',
                    order_id=order.id,
                    customer_id=customer.id
                )
                db.session.add(account_transaction)
                fixed_count += 1

        # 2. 修复供应商往来单据的账户记录
        supplier_transactions = SupplierTransaction.query.all()
        for st in supplier_transactions:
            # 检查MoneyTransaction中的账户交易
            money_trans = MoneyTransaction.query.filter_by(transaction_id=st.id).all()
            for mt in money_trans:
                if mt.account_name and mt.return_amount and mt.return_amount > 0:
                    # 检查是否已有记录
                    existing = AccountTransaction.query.filter_by(
                        supplier_transaction_id=st.id,
                        amount=mt.return_amount
                    ).first()

                    if not existing:
                        # 查找账户
                        account = Account.query.filter_by(name=mt.account_name).first()
                        if account:
                            # 确定交易类型和用途
                            transaction_type = 'expense'  # 供应商还款通常是支出
                            purpose = '供应商还款'

                            account_transaction = AccountTransaction(
                                account_id=account.id,
                                transaction_date=st.business_date if st.business_date else datetime.now().date(),
                                amount=mt.return_amount,
                                transaction_type=transaction_type,
                                purpose=purpose,
                                related_party=st.supplier.name if st.supplier else '未知供应商',
                                related_document=st.transaction_no,
                                notes=st.notes or '',
                                supplier_id=st.supplier_id,
                                supplier_transaction_id=st.id
                            )
                            db.session.add(account_transaction)
                            fixed_count += 1

        # 3. 修复其他项目往来单据的账户记录
        other_transactions = Transaction.query.all()
        for ot in other_transactions:
            if ot.account_id and ot.amount and ot.amount > 0:
                # 检查是否已有记录
                existing = AccountTransaction.query.filter_by(
                    account_id=ot.account_id,
                    related_document=ot.transaction_no
                ).first()

                if not existing:
                    transaction_type = 'income' if ot.is_income else 'expense'
                    purpose = '其他项目'

                    account_transaction = AccountTransaction(
                        account_id=ot.account_id,
                        transaction_date=ot.business_time.date() if ot.business_time else datetime.now().date(),
                        amount=ot.amount,
                        transaction_type=transaction_type,
                        purpose=purpose,
                        related_party=ot.customer.name if ot.customer else '未知',
                        related_document=ot.transaction_no,
                        notes=ot.notes or '',
                        customer_id=ot.customer_id
                    )
                    db.session.add(account_transaction)
                    fixed_count += 1

        # 4. 修复采购单据的账户记录
        from models import Purchase
        purchases = Purchase.query.all()
        for purchase in purchases:
            if hasattr(purchase, 'account_id') and purchase.account_id and hasattr(purchase, 'settlement_amount') and purchase.settlement_amount and purchase.settlement_amount > 0:
                # 检查是否已有记录
                existing = AccountTransaction.query.filter_by(
                    purchase_id=purchase.id
                ).first()

                if not existing:
                    account_transaction = AccountTransaction(
                        account_id=purchase.account_id,
                        transaction_date=purchase.purchase_date if hasattr(purchase, 'purchase_date') and purchase.purchase_date else datetime.now().date(),
                        amount=purchase.settlement_amount,
                        transaction_type='expense',
                        purpose='采购付款',
                        related_party=purchase.supplier_name if hasattr(purchase, 'supplier_name') else '未知供应商',
                        related_document=purchase.purchase_no if hasattr(purchase, 'purchase_no') else f'PUR{purchase.id:06d}',
                        notes=purchase.notes if hasattr(purchase, 'notes') else '',
                        purchase_id=purchase.id
                    )
                    db.session.add(account_transaction)
                    fixed_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功修复 {fixed_count} 条缺失的账户交易记录'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'修复失败: {str(e)}'
        })

@app.route('/api/customer_material_balances/<int:customer_id>')
@login_required
def get_customer_material_balances(customer_id):
    """获取客户的旧料余额数据"""
    try:
        from models import CustomerMaterialBalance

        balances = CustomerMaterialBalance.query.filter_by(customer_id=customer_id).all()

        balances_data = []
        for balance in balances:
            balances_data.append({
                'material_name': balance.material_name,
                'owed_weight': balance.owed_weight,
                'stored_weight': balance.stored_weight
            })

        return jsonify({
            'status': 'success',
            'data': balances_data
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取客户旧料余额失败: {str(e)}'
        })

@app.route('/api/customer_sales_statistics')
@login_required
def get_customer_sales_statistics():
    """获取客户销售统计数据"""
    try:
        from routes.report_routes import api_customer_sales_statistics
        return api_customer_sales_statistics()
    except Exception as e:
        app.logger.error(f"获取客户销售统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取客户销售统计失败: {str(e)}'
        }), 500

@app.route('/api/products')
@login_required
def get_products_api():
    """获取产品列表API"""
    try:
        from models import Product
        # 按照产品编号排序，与产品管理页面保持一致
        products = Product.query.filter_by(is_active=True).order_by(Product.code).all()

        products_data = []
        for product in products:
            products_data.append({
                'id': product.id,
                'code': product.code,
                'name': product.name,
                'category': product.category
            })

        return jsonify({
            'success': True,
            'products': products_data
        })
    except Exception as e:
        app.logger.error(f"获取产品列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取产品列表失败: {str(e)}'
        }), 500

@app.route('/api/old_materials')
@login_required
def get_old_materials_api():
    """获取旧料列表API - 统一接口支持多种格式"""
    try:
        from models import OldMaterial
        from routes.report_routes import calculate_old_material_inventory_at_date
        from datetime import date

        # 获取参数
        include_stock = request.args.get('include_stock', 'false').lower() == 'true'
        format_type = request.args.get('format', 'standard')  # standard, dropdown

        # 按照旧料编号排序
        old_materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code).all()

        materials_data = []
        for material in old_materials:
            material_info = {
                'id': material.id,
                'code': material.code,
                'name': material.name
            }

            # 如果需要包含库存信息
            if include_stock:
                # 从料部库存查询计算当前库存
                current_stock = calculate_old_material_inventory_at_date(material, date.today())
                material_info['department_stock'] = float(current_stock)
                material_info['initial_stock'] = float(material.initial_stock or 0)

            materials_data.append(material_info)

        # 根据格式类型返回不同的响应格式
        if format_type == 'dropdown':
            return jsonify({
                'status': 'success',
                'materials': [{'name': m['name'], 'id': m['id']} for m in materials_data]
            })
        else:
            return jsonify({
                'success': True,
                'materials': materials_data
            })
    except Exception as e:
        app.logger.error(f"获取旧料列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取旧料列表失败: {str(e)}'
        }), 500

@app.route('/accounts/add', methods=['GET', 'POST'])
@login_required
def account_add():
    if request.method == 'POST':
        account_name = request.form['name']
        account_number = request.form['account_number']

        # 检查账户名称是否重复
        if Account.query.filter_by(name=account_name).first():
            flash('账户名称已存在，请使用其他名称', 'danger')
            return render_template('accounts/add.html')

        # 检查账户号码是否重复
        if Account.query.filter_by(account_number=account_number).first():
            flash('账户号码已存在，请使用其他号码', 'danger')
            return render_template('accounts/add.html')

        initial_balance = float(request.form['initial_balance']) if request.form['initial_balance'] else 0
        account = Account(
            name=account_name,
            bank_name=request.form['bank_name'],
            account_number=account_number,
            initial_balance=initial_balance,
            balance=initial_balance,  # 初始时当前余额等于初始余额
            notes=request.form.get('notes', '')
        )
        db.session.add(account)
        db.session.commit()
        flash('账户已添加', 'success')
        return redirect(url_for('accounts'))

    return render_template('accounts/add.html')

@app.route('/accounts/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def account_edit(id):
    account = Account.query.get_or_404(id)

    if request.method == 'POST':
        account_name = request.form['name']
        account_number = request.form['account_number']

        # 检查账户名称是否重复（排除当前账户）
        existing_account_by_name = Account.query.filter(
            Account.name == account_name,
            Account.id != account.id
        ).first()
        if existing_account_by_name:
            flash('账户名称已存在，请使用其他名称', 'danger')
            return render_template('accounts/edit.html', account=account)

        # 检查账户号码是否重复（排除当前账户）
        existing_account_by_number = Account.query.filter(
            Account.account_number == account_number,
            Account.id != account.id
        ).first()
        if existing_account_by_number:
            flash('账户号码已存在，请使用其他号码', 'danger')
            return render_template('accounts/edit.html', account=account)

        account.name = account_name
        account.bank_name = request.form['bank_name']
        account.account_number = account_number
        account.initial_balance = float(request.form['initial_balance']) if request.form['initial_balance'] else 0
        account.notes = request.form.get('notes', '')
        # 注意：当前余额字段不允许修改，保持原值

        db.session.commit()
        flash('账户信息已更新', 'success')
        return redirect(url_for('accounts'))

    return render_template('accounts/edit.html', account=account)

@app.route('/accounts/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def account_delete(id):
    account = Account.query.get_or_404(id)
    
    # 检查是否为默认账户
    if account.name == '默认账户':
        flash('默认账户不能删除', 'danger')
        return redirect(url_for('accounts'))
    
    # 检查是否有关联的订单
    has_orders = Order.query.filter_by(account_id=id).first() is not None
    if has_orders:
        flash('该账户已有关联的订单，不能删除', 'danger')
        return redirect(url_for('accounts'))

    # 检查是否有关联的账户交易记录
    has_account_transactions = AccountTransaction.query.filter_by(account_id=id).first() is not None
    if has_account_transactions:
        flash('该账户已有关联的账户交易记录，不能删除', 'danger')
        return redirect(url_for('accounts'))

    # 检查是否有关联的其他项目往来记录
    has_transactions = Transaction.query.filter_by(account_id=id).first() is not None
    if has_transactions:
        flash('该账户已有关联的其他项目往来记录，不能删除', 'danger')
        return redirect(url_for('accounts'))

    # 检查是否有关联的采购单据
    has_purchases = Purchase.query.join(PurchaseItem).filter(
        PurchaseItem.settlement_method == '欠款'
    ).first() is not None
    if has_purchases:
        # 这里需要更精确的检查，但由于采购单据没有直接的account_id字段，
        # 我们暂时跳过这个检查，或者可以通过其他方式关联
        pass

    # 如果没有任何关联记录，则可以删除
    try:
        app.logger.info(f"删除账户: {id}")
        db.session.delete(account)
        db.session.commit()
        flash('账户已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除账户失败: {str(e)}")
        flash(f'删除账户失败: {str(e)}', 'danger')
    
    return redirect(url_for('accounts'))

# 采购录入
@app.route('/purchases', methods=['GET'])
@login_required
def purchases():
    """采购列表页面"""
    # 获取查询参数
    search = request.args.get('search', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')


    
    # 分页参数
    page = request.args.get(get_page_parameter(), 1, type=int)
    per_page = 20
    
    # 构建基本查询
    # 修改：重新加入 business_time
    sql_query = f"""
        SELECT 
            p.id, 
            p.purchase_no, 
            p.business_time, -- 重新加入业务日期
            (SELECT COUNT(DISTINCT pi_sc.supplier_id) 
             FROM purchase_items pi_sc 
             WHERE pi_sc.purchase_id = p.id AND pi_sc.is_deleted = 0 AND pi_sc.supplier_id IS NOT NULL
            ) as supplier_count, 
            (SELECT SUM(pi.weight) 
             FROM purchase_items pi 
             JOIN products pr ON pi.product_id = pr.id 
             WHERE pi.purchase_id = p.id 
             AND pi.is_deleted = 0 
             AND pr.category IN ('黄金', '足金', '3D硬金', '5G黄金', '5D黄金')
            ) as gold_weight, 
            (SELECT SUM(pi.labor_fee) 
             FROM purchase_items pi 
             WHERE pi.purchase_id = p.id AND pi.is_deleted = 0
            ) as total_amount,
            (SELECT SUM(pi.final_amount) 
             FROM purchase_items pi 
             WHERE pi.purchase_id = p.id AND pi.is_deleted = 0
            ) as total_final_amount
        FROM purchases p
        WHERE p.is_deleted = 0
    """
    
    # 构建计数查询 (不再需要 JOIN suppliers)
    count_sql = f"""
        SELECT COUNT(*)
        FROM purchases p
        -- LEFT JOIN suppliers s ON p.supplier_id = s.id -- 移除 JOIN
        WHERE p.is_deleted = 0
    """
        
    # 构建查询参数
    params = {}
        
    # 添加搜索条件
    if search:
        # 按采购单号搜索
        sql_query += " AND p.purchase_no LIKE :search"
        count_sql += " AND p.purchase_no LIKE :search"
        params['search'] = f"%{search}%"

    # 添加日期筛选条件
    if start_date:
        sql_query += " AND date(p.business_time) >= :start_date"
        count_sql += " AND date(p.business_time) >= :start_date"
        params['start_date'] = start_date

    if end_date:
        sql_query += " AND date(p.business_time) <= :end_date"
        count_sql += " AND date(p.business_time) <= :end_date"
        params['end_date'] = end_date

    # 添加排序
    sql_query += " ORDER BY p.business_time DESC, p.id DESC"
    
    try:
        # 执行计数查询
        result = db.session.execute(text(count_sql), params).scalar()
        total = result or 0
        
        # 添加分页
        sql_query += " LIMIT :per_page OFFSET :offset"
        params['per_page'] = per_page
        params['offset'] = (page - 1) * per_page
        
        # 执行最终查询
        result = db.session.execute(text(sql_query), params)
        purchases_data = []
        for row in result:
            row_dict = {column: value for column, value in row._mapping.items()}
            # 确保business_time是datetime对象，以便在模板中正确格式化
            if 'business_time' in row_dict and row_dict['business_time']:
                business_time = row_dict['business_time']
                if isinstance(business_time, str):
                    # 如果是字符串，尝试解析为datetime
                    try:
                        from datetime import datetime
                        row_dict['business_time'] = datetime.strptime(business_time, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        try:
                            row_dict['business_time'] = datetime.strptime(business_time, '%Y-%m-%d')
                        except ValueError:
                            pass  # 保持原值
            purchases_data.append(row_dict)
        
        # 使用自定义分页对象 - 从pagination.py导入
        from pagination import CustomPagination
        pagination = CustomPagination(purchases_data, page, per_page, total)
        
        # 获取今日统计数据
        today = datetime.now().date()
        today_start = datetime.combine(today, time.min)
        today_end = datetime.combine(today, time.max)
        
        # 使用原生SQL查询今日采购统计 - 使用business_time而不是created_at，并且只统计未删除的采购单
        # 已修改：添加 products 表的 JOIN 并根据 product.category 筛选黄金
        stats_sql = """
            SELECT 
                COUNT(DISTINCT p.id) as purchase_count,
                (SELECT SUM(pi.weight) 
                 FROM purchase_items pi 
                 JOIN purchases p2 ON pi.purchase_id = p2.id 
                 JOIN products pr ON pi.product_id = pr.id  -- 添加 JOIN products 表
                 WHERE date(p2.business_time) = date(:today)
                 AND pi.is_deleted = 0 AND p2.is_deleted = 0
                 -- 使用 products.category 进行筛选
                 AND pr.category IN ('黄金', '足金', '3D硬金', '5G黄金', '5D黄金') 
                ) as today_gold_in,
                (SELECT SUM(pi.labor_fee) 
                 FROM purchase_items pi 
                 JOIN purchases p2 ON pi.purchase_id = p2.id 
                 WHERE date(p2.business_time) = date(:today)
                 AND pi.is_deleted = 0 AND p2.is_deleted = 0
                ) as today_labor_fee,
                (SELECT SUM(pi.final_amount) 
                 FROM purchase_items pi 
                 JOIN purchases p2 ON pi.purchase_id = p2.id 
                 WHERE date(p2.business_time) = date(:today)
                 AND pi.is_deleted = 0 AND p2.is_deleted = 0
                ) as today_final_amount
            FROM purchases p
            WHERE date(p.business_time) = date(:today)
              AND p.is_deleted = 0
        """
        
        stats_result = db.session.execute(
            text(stats_sql),
            {'today': today}
        ).fetchone()
        
        stats = {
            'purchase_count': stats_result.purchase_count or 0,
            'today_gold_in': stats_result.today_gold_in or 0,
            'today_labor_fee': stats_result.today_labor_fee or 0,
            'today_final_amount': stats_result.today_final_amount or 0
        }
        
        return render_template('purchases/index.html', 
                              purchases=purchases_data,
                              pagination=pagination,
                              search=search,
                              start_date=start_date,
                              end_date=end_date,
                              stats=stats,
                              app_name="金店管理系统")
    
    except Exception as e:
        app.logger.error(f"查询采购单失败: {str(e)}")
        # 创建一个空的分页对象
        from pagination import CustomPagination
        empty_pagination = CustomPagination([], page, per_page, 0)
        
        
        return render_template('purchases/index.html', 
                              purchases=[], 
                              pagination=empty_pagination,
                              search=search,
                              start_date=start_date,
                              end_date=end_date,
                              stats={
                                'purchase_count': 0,
                                'today_gold_in': 0,
                                'today_labor_fee': 0,
                                'today_final_amount': 0
                              },
                              app_name="金店管理系统")

@app.route('/purchases/<int:purchase_id>/restore', methods=['POST'])
@login_required
def purchases_restore(purchase_id):
    """恢复已删除的采购单"""
    try:
        # 获取采购单
        purchase = Purchase.query.get_or_404(purchase_id)
        
        # 检查采购单是否已标记为删除
        if not purchase.is_deleted:
            flash('只能恢复已标记为删除的采购单', 'warning')
            return redirect(url_for('purchases_deleted'))
        
        # 恢复采购单
        purchase.is_deleted = False
        purchase.deleted_at = None

        # 恢复采购单项目
        for item in purchase.items:
            item.is_deleted = False
            item.deleted_at = None

        # 提交更改
        db.session.commit()

        # 重新应用采购单据对供应商信息的影响
        try:
            from purchase_service import PurchaseService
            PurchaseService.apply_purchase_effects(purchase.id)
            db.session.commit()
            app.logger.info(f"采购单 {purchase.id} 的供应商数据联动恢复完成")
        except Exception as e:
            app.logger.error(f"恢复采购单 {purchase.id} 的供应商数据联动时出错: {str(e)}")
        
        flash(f'采购单 {purchase.purchase_no} 已成功恢复', 'success')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"恢复采购单失败: {str(e)}")
        flash(f'恢复采购单失败: {str(e)}', 'danger')
    
    return redirect(url_for('purchases_deleted'))

@app.route('/purchases/delete/<int:purchase_id>', methods=['POST'])
@login_required
def purchases_delete(purchase_id):
    """删除采购单（软删除）"""
    try:
        # 获取采购单
        purchase = Purchase.query.get_or_404(purchase_id)
        
        # 检查采购单是否已经被删除
        if purchase.is_deleted:
            flash('采购单已经被删除', 'warning')
            return jsonify({'success': False, 'message': '采购单已经被删除'}), 400
        
        # 回滚采购单据对供应商信息的影响（只回滚当前有效的项目）
        try:
            from purchase_service import PurchaseService
            PurchaseService.rollback_purchase_effects(purchase.id, only_active=True)
            app.logger.info(f"采购单 {purchase.id} 的供应商数据联动回滚完成")
        except Exception as e:
            app.logger.error(f"回滚采购单 {purchase.id} 的供应商数据联动时出错: {str(e)}")

        # 标记为软删除
        purchase.is_deleted = True
        purchase.deleted_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 标记采购项目为软删除
        for item in purchase.items:
            item.is_deleted = True
            item.deleted_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 提交更改
        db.session.commit()
        
        app.logger.info(f"采购单 {purchase.purchase_no} (ID: {purchase_id}) 已被标记为删除")
        flash(f'采购单 {purchase.purchase_no} 已删除', 'success')
        
        return jsonify({'success': True, 'message': '采购单已删除'})
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除采购单失败: {str(e)}")
        import traceback
        app.logger.error(traceback.format_exc())
        flash(f'删除采购单失败: {str(e)}', 'danger')
        return jsonify({'success': False, 'message': f'删除采购单失败: {str(e)}'}), 500

@app.route('/purchases/deleted', methods=['GET'])
@login_required
def purchases_deleted():
    """查看已删除的采购单列表"""
    # 获取搜索参数
    search = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)
    
    # 创建基础查询
    query = Purchase.query.filter(Purchase.is_deleted == True)
    
    # 添加搜索条件
    if search:
        query = query.filter(or_(
            Purchase.purchase_no.contains(search),
            Purchase.supplier_name.contains(search) if hasattr(Purchase, 'supplier_name') else True,
            Purchase.notes.contains(search) if hasattr(Purchase, 'notes') else True
        ))
    
    # 按删除时间降序排序
    query = query.order_by(Purchase.deleted_at.desc() if hasattr(Purchase, 'deleted_at') else Purchase.id.desc())
    
    # 分页
    pagination = query.paginate(page=page, per_page=10, error_out=False)
    purchases = pagination.items
    
    return render_template('purchases/deleted.html', purchases=purchases, pagination=pagination, search=search)

@app.route('/purchases/deleted/clear', methods=['POST'])
@login_required
def purchases_deleted_clear():
    """清空已删除的采购单列表（永久删除）
    注意：已删除的采购单在删除时已经回滚了数据联动，清空时不需要再次回滚
    """
    try:
        # 查询所有已删除的采购单
        deleted_purchases = Purchase.query.filter(Purchase.is_deleted == True).all()

        if not deleted_purchases:
            flash('没有已删除的采购单需要清空', 'warning')
            return redirect(url_for('purchases_deleted'))

        # 记录删除的采购单数量
        purchase_count = len(deleted_purchases)
        app.logger.info(f"开始清空 {purchase_count} 个已删除采购单")

        # 循环删除每个采购单及其关联数据
        for purchase in deleted_purchases:
            app.logger.info(f"永久删除采购单: {purchase.purchase_no}, ID={purchase.id}")

            # 删除关联的账户交易记录
            account_transactions = AccountTransaction.query.filter_by(purchase_id=purchase.id).all()
            for at in account_transactions:
                app.logger.info(f"删除关联账户交易记录: ID={at.id}")
                db.session.delete(at)

            # 删除关联的采购项
            purchase_items = PurchaseItem.query.filter_by(purchase_id=purchase.id).all()
            for item in purchase_items:
                app.logger.info(f"删除采购项: ID={item.id}")
                db.session.delete(item)

            # 删除采购单本身
            db.session.delete(purchase)

        # 提交事务
        db.session.commit()
        app.logger.info(f"成功清空 {purchase_count} 个已删除的采购单")
        flash(f'成功清空 {purchase_count} 个已删除的采购单', 'success')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"清空已删除采购单失败: {str(e)}")
        flash(f'清空已删除采购单失败: {str(e)}', 'danger')

    return redirect(url_for('purchases_deleted'))

def migrate_purchases_table():
    
    if request.method == 'POST':
        purchase.is_deleted = True
        purchase.deleted_at = datetime.now()
        db.session.commit()
        flash('采购单已删除', 'success')
        return redirect(url_for('purchases'))
    
    return render_template('purchases/delete.html', purchase=purchase)

# 添加采购表迁移函数
def migrate_purchases_table():
    """迁移purchases表，添加is_material_received列以及软删除字段"""
    try:
        # 检查purchases表是否存在
        inspector = inspect(db.engine)
        if 'purchases' not in inspector.get_table_names():
            print("purchases表不存在，将在首次使用时创建")
            return
            
        # 检查字段是否存在
        columns = [column['name'] for column in inspector.get_columns('purchases')]
        
            # 添加is_material_received列
        if 'is_material_received' not in columns:
            if db.engine.name == 'sqlite':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN is_material_received BOOLEAN DEFAULT 0'))
            elif db.engine.name == 'mysql':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN is_material_received TINYINT(1) DEFAULT 0'))
            elif db.engine.name == 'postgresql':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN is_material_received BOOLEAN DEFAULT FALSE'))
            
            db.session.commit()
            print("成功添加is_material_received列到purchases表")
        
        # 添加is_deleted列
        if 'is_deleted' not in columns:
            if db.engine.name == 'sqlite':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN is_deleted BOOLEAN DEFAULT 0'))
            elif db.engine.name == 'mysql':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN is_deleted TINYINT(1) DEFAULT 0'))
            elif db.engine.name == 'postgresql':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE'))
            
            db.session.commit()
            print("成功添加is_deleted列到purchases表")
        
        # 添加deleted_at列
        if 'deleted_at' not in columns:
            if db.engine.name == 'sqlite':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN deleted_at VARCHAR(30)'))
            elif db.engine.name == 'mysql':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN deleted_at VARCHAR(30)'))
            elif db.engine.name == 'postgresql':
                db.session.execute(text('ALTER TABLE purchases ADD COLUMN deleted_at VARCHAR(30)'))
            
            db.session.commit()
            print("成功添加deleted_at列到purchases表")
        
        # 检查purchase_items表是否存在
        if 'purchase_items' not in inspector.get_table_names():
            print("purchase_items表不存在，将在首次使用时创建")
            return
            
        # 检查purchase_items表中的字段
        pi_columns = [column['name'] for column in inspector.get_columns('purchase_items')]
        
        # 添加is_deleted列到purchase_items
        if 'is_deleted' not in pi_columns:
            if db.engine.name == 'sqlite':
                db.session.execute(text('ALTER TABLE purchase_items ADD COLUMN is_deleted BOOLEAN DEFAULT 0'))
            elif db.engine.name == 'mysql':
                db.session.execute(text('ALTER TABLE purchase_items ADD COLUMN is_deleted TINYINT(1) DEFAULT 0'))
            elif db.engine.name == 'postgresql':
                db.session.execute(text('ALTER TABLE purchase_items ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE'))
            
            db.session.commit()
            print("成功添加is_deleted列到purchase_items表")
        
        # 添加deleted_at列到purchase_items
        if 'deleted_at' not in pi_columns:
            if db.engine.name == 'sqlite':
                db.session.execute(text('ALTER TABLE purchase_items ADD COLUMN deleted_at VARCHAR(30)'))
            elif db.engine.name == 'mysql':
                db.session.execute(text('ALTER TABLE purchase_items ADD COLUMN deleted_at VARCHAR(30)'))
            elif db.engine.name == 'postgresql':
                db.session.execute(text('ALTER TABLE purchase_items ADD COLUMN deleted_at VARCHAR(30)'))
            
            db.session.commit()
            print("成功添加deleted_at列到purchase_items表")
            
    except Exception as e:
        db.session.rollback()
        print(f"迁移purchases表时出错: {str(e)}")
        app.logger.error(f"迁移purchases表时出错: {str(e)}", exc_info=True)

# 添加供应商往来表迁移函数
def migrate_supplier_transactions_table():
    """迁移supplier_transactions表"""
    try:
        inspector = inspect(db.engine)
        
        # 检查表是否存在
        if 'supplier_transactions' not in inspector.get_table_names():
            # 创建表
            db.create_all()
            app.logger.info("供应商交易表创建成功")
        return True
    except Exception as e:
        app.logger.error(f"迁移供应商交易表失败: {str(e)}")
        return False

# 修改init_app_data函数
def init_app_context():
    # 在应用上下文中初始化数据
    with app.app_context():
        # 创建所有表
        db.create_all()
        
        # 检查系统标志，看是否需要执行迁移操作
        migrate_purchases_table()
        migrate_supplier_transactions_table()
        
        # 添加对purchase_items表的迁移代码
        migrate_purchases_table()
        
        # 检查是否需要添加默认用户
        user = User.query.filter_by(username='admin').first()
        if not user:
            user = User(username='admin', email='<EMAIL>', name='管理员', role='admin')
            user.password = 'admin123'
            db.session.add(user)
            db.session.commit()

        # 检查并设置打印服务环境
        check_print_service_environment()

# 检查打印服务环境
def check_print_service_environment():
    import os
    import platform
    import subprocess
    import logging
    from pathlib import Path
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, 
                      format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                      filename='print_service.log')
    logger = logging.getLogger('print_service_setup')
    
    logger.info("检查打印服务环境...")
    
    # 获取打印服务脚本路径
    base_dir = Path(__file__).resolve().parent
    print_service_dir = base_dir / 'static' / 'print-service'
    setup_script = print_service_dir / 'setup-embedded-print.bat'
    
    # 检查是否首次运行或需要初始化
    env_flag = print_service_dir / '.env_initialized'
    
    if not env_flag.exists() and platform.system() == 'Windows':
        logger.info("首次运行，初始化打印服务环境...")
        
        try:
            # 静默执行环境设置
            startupinfo = None
            if hasattr(subprocess, 'STARTUPINFO'):
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = 0  # SW_HIDE
            
            # 运行初始化脚本
            result = subprocess.run(
                [str(setup_script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=startupinfo,
                shell=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("打印服务环境设置成功")
                # 创建标记文件
                with open(env_flag, 'w') as f:
                    f.write(f"Initialized at {datetime.now().isoformat()}")
            else:
                logger.error(f"打印服务环境设置失败: {result.stderr}")
        
        except Exception as e:
            logger.error(f"初始化打印服务时出错: {str(e)}")
    else:
        logger.info("打印服务环境已初始化")
    
    # 启动打印服务
    try:
        logger.info("正在启动打印服务...")
        service_script = print_service_dir / 'print-service-launcher.js'
        
        # 确定Node.js路径
        node_exe = 'node'
        embedded_node = print_service_dir.parent / 'node' / 'node.exe'
        if os.path.exists(embedded_node):
            node_exe = str(embedded_node)
        
        # 静默启动打印服务
        startupinfo = None
        if hasattr(subprocess, 'STARTUPINFO'):
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = 0  # SW_HIDE
        
        # 使用subprocess.Popen启动服务而不等待它完成
        process = subprocess.Popen(
            [node_exe, str(service_script)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            startupinfo=startupinfo,
            shell=True,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        logger.info(f"打印服务启动成功，进程ID: {process.pid}")
        
    except Exception as e:
        logger.error(f"启动打印服务失败: {str(e)}")

# 在Flask 2.0+中不再支持before_first_request
# 使用应用上下文直接初始化
# 注释掉此处的初始化，只在__main__中初始化一次
# with app.app_context():
#     init_app_context()

# 客户往来录入
@app.route('/customer_transactions')
@login_required
def customer_transactions():
    # 创建一个空的FlaskForm实例用于CSRF保护
    form = FlaskForm()
    
    # 获取筛选参数
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # 获取筛选参数
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    customer_id = request.args.get('customer_id', type=int)
    
    # 如果没有设置日期筛选，默认使用当天
    if not start_date and not end_date:
        today = datetime.now().strftime('%Y-%m-%d')
        start_date = today
        end_date = today
    elif start_date and not end_date:
        end_date = start_date
    
    # 构建查询
    query = Transaction.query
    
    # 应用筛选条件
    if start_date:
        query = query.filter(db.func.date(Transaction.business_time) >= start_date)
    if end_date:
        query = query.filter(db.func.date(Transaction.business_time) <= end_date)
    if customer_id:
        query = query.filter(Transaction.customer_id == customer_id)
    
    # 按业务时间倒序排序
    query = query.order_by(Transaction.business_time.desc())
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page)
    transactions = pagination.items
    
    # 为每个事务加载客户和账户信息
    for transaction in transactions:
        if transaction.customer_id:
            customer = Customer.query.get(transaction.customer_id)
            if customer:
                transaction.customer = customer
            else:
                transaction.customer = None
        else:
            transaction.customer = None
        
        if transaction.account_id:
            account = Account.query.get(transaction.account_id)
            if account:
                transaction.account = account
            else:
                transaction.account = None
    
    # 获取所有客户列表供筛选使用
    customers = Customer.query.order_by(Customer.name).all()
    
    return render_template('transactions/index.html', 
                         transactions=transactions, 
                         pagination=pagination,
                         start_date=start_date,
                         end_date=end_date,
                         customer_id=customer_id,
                         customers=customers,
                         form=form,
                         title='其他项目往来')

@app.route('/customer_transactions/add', methods=['GET', 'POST'])
@login_required
def customer_transaction_add():
    form = FlaskForm()
    if request.method == 'POST':
        if form.validate_on_submit():
            try:
                # 获取表单数据
                data = request.get_json()
                
                # 获取第一个往来项的信息
                first_item = data['items'][0]
                
                # 创建新的交易记录
                transaction = Transaction(
                    transaction_no=data['transaction_no'],
                    business_time=datetime.strptime(first_item['paymentDate'], '%Y-%m-%d'),
                    customer_id=first_item['customerId'],
                    amount=first_item['amount'],
                    account_id=first_item['accountId'],
                    is_income=data.get('is_income', True),
                    notes=first_item.get('itemNotes', '')
                )
                
                # 保存交易记录
                db.session.add(transaction)
                db.session.commit()

                # 应用其他项目往来单据对账户信息的影响
                try:
                    from other_transaction_service import OtherTransactionService
                    OtherTransactionService.apply_transaction_effects(transaction.id)
                    db.session.commit()
                    app.logger.info(f"其他项目往来单据 {transaction.id} 的账户数据联动处理完成")
                except Exception as e:
                    app.logger.error(f"处理其他项目往来单据 {transaction.id} 的账户数据联动时出错: {str(e)}")

                return jsonify({'success': True, 'message': '交易记录添加成功'})
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': False, 'message': f'添加失败：{str(e)}'})
    
    # GET请求，渲染添加页面
    # 获取所有客户和供应商
    customers = Customer.query.all()
    suppliers = Supplier.query.all()
    
    entities_list = []
    
    # 添加客户到列表
    for customer in customers:
        entities_list.append({
            'id': customer.id,
            'name': customer.name,
            'type': 'customer',
            'phone': customer.phone,
            'pinyin': customer.pinyin if hasattr(customer, 'pinyin') else '',
            'pinyin_initials': customer.pinyin_initials if hasattr(customer, 'pinyin_initials') else ''
        })
    
    # 添加供应商到列表
    for supplier in suppliers:
        entities_list.append({
            'id': supplier.id,
            'name': supplier.name,
            'type': 'supplier',
            'phone': supplier.phone if hasattr(supplier, 'phone') else '',
            'pinyin': supplier.pinyin if hasattr(supplier, 'pinyin') else '',
            'pinyin_initials': supplier.pinyin_initials if hasattr(supplier, 'pinyin_initials') else ''
        })
    
    # 获取所有账户
    accounts = Account.query.filter_by(is_active=True).all()
    accounts_list = []
    for account in accounts:
        accounts_list.append({
            'id': account.id,
            'name': account.name,
            'type': account.account_type
        })
    
    # 生成往来单号：WL + 年月日 + 4位随机数
    today = datetime.now().strftime('%y%m%d')
    random_num = random.randint(1000, 9999)
    transaction_no = f"WL{today}{random_num}"
    
    # 确保单号唯一
    while Transaction.query.filter_by(transaction_no=transaction_no).first():
        random_num = random.randint(1000, 9999)
        transaction_no = f"WL{today}{random_num}"
    
    return render_template('transactions/add.html',
                         form=form,
                         customers=entities_list,
                         accounts=accounts_list,
                         transaction_no=transaction_no,
                         is_edit=False,
                         title='其他项目往来录入')

@app.route('/customer_transactions/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def customer_transaction_edit(id):
    form = FlaskForm()
    transaction = Transaction.query.get_or_404(id)
    
    if request.method == 'POST':
        if form.validate_on_submit():
            try:
                # 获取JSON数据
                data = request.get_json()
                
                # 获取第一个往来项的信息
                if 'items' in data and len(data['items']) > 0:
                    first_item = data['items'][0]
                    
                    # 更新交易记录
                    # 支持datetime-local格式
                    payment_date_str = first_item['paymentDate']
                    try:
                        if 'T' in payment_date_str:
                            # datetime-local格式：2025-07-11T16:30
                            transaction.business_time = datetime.strptime(payment_date_str, '%Y-%m-%dT%H:%M')
                        else:
                            # 只有日期格式：2025-07-11
                            transaction.business_time = datetime.strptime(payment_date_str, '%Y-%m-%d')
                    except (ValueError, TypeError) as e:
                        app.logger.error(f'解析其他项目往来时间出错: {str(e)}, 原始值: {payment_date_str}')
                        transaction.business_time = datetime.now()
                    transaction.customer_id = first_item['customerId']
                    transaction.amount = float(first_item['amount'])
                    transaction.account_id = int(first_item['accountId'])
                    transaction.is_income = first_item['isIncome']
                    transaction.notes = first_item.get('itemNotes', '')
                    
                    db.session.commit()
                    return jsonify({'success': True, 'message': '交易记录更新成功'})
                else:
                    return jsonify({'success': False, 'message': '没有提供有效的往来项'})
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': False, 'message': f'更新失败：{str(e)}'})
    
    # 获取所有账户
    accounts = Account.query.filter_by(is_active=True).all()
    
    # 获取客户完整信息
    customer_data = None
    if transaction.customer_id:
        customer = Customer.query.get(transaction.customer_id)
        if customer:
            customer_data = {
                'id': customer.id,
                'name': customer.name,
                'type': customer.customer_type,
                'owed_gold': customer.owed_gold,
                'deposit_gold': customer.deposit_gold
            }
    
    # 获取所有客户和供应商，用于编辑页面的选择
    customers = Customer.query.all()
    suppliers = Supplier.query.all()
    
    entities_list = []
    
    # 添加客户到列表
    for customer in customers:
        entities_list.append({
            'id': customer.id,
            'name': customer.name,
            'type': 'customer',
            'phone': customer.phone,
            'pinyin': customer.pinyin if hasattr(customer, 'pinyin') else '',
            'pinyin_initials': customer.pinyin_initials if hasattr(customer, 'pinyin_initials') else ''
        })
    
    # 添加供应商到列表
    for supplier in suppliers:
        entities_list.append({
            'id': supplier.id,
            'name': supplier.name,
            'type': 'supplier',
            'phone': supplier.phone if hasattr(supplier, 'phone') else '',
            'pinyin': supplier.pinyin if hasattr(supplier, 'pinyin') else '',
            'pinyin_initials': supplier.pinyin_initials if hasattr(supplier, 'pinyin_initials') else ''
        })
    
    # 获取所有账户
    accounts_list = []
    for account in accounts:
        accounts_list.append({
            'id': account.id,
            'name': account.name,
            'type': account.account_type
        })
    
    # 处理编辑模式，使用add.html模板
    return render_template('transactions/add.html',
                         transaction=transaction,
                         accounts=accounts_list,
                         customers=entities_list,
                         customer_data=customer_data,
                         transaction_no=transaction.transaction_no,
                         is_edit=True,
                         form=form,
                         title='编辑其他项目往来')

@app.route('/customer_transactions/<int:id>/delete', methods=['POST'])
@login_required
def customer_transaction_delete(id):
    """删除其他项目往来记录，回滚所有相关数据变化"""
    try:
        transaction = Transaction.query.get_or_404(id)
        transaction_no = transaction.transaction_no

        app.logger.info(f"开始删除其他项目往来记录: ID={id}, 单号={transaction_no}")

        # 使用服务回滚账户数据联动
        try:
            from other_transaction_service import OtherTransactionService
            OtherTransactionService.rollback_transaction_effects(transaction.id)
            app.logger.info(f"其他项目往来记录 {transaction.id} 的账户数据联动回滚完成")
        except Exception as e:
            app.logger.error(f"回滚其他项目往来记录 {transaction.id} 的账户数据联动时出错: {str(e)}")

        # 删除关联的账户交易记录
        account_transactions = AccountTransaction.query.filter_by(
            related_document=transaction_no
        ).all()
        for at in account_transactions:
            db.session.delete(at)
            app.logger.info(f"删除关联账户交易记录: ID={at.id}")

        # 删除主记录
        db.session.delete(transaction)
        db.session.commit()

        app.logger.info(f"其他项目往来记录删除成功: ID={id}")
        flash(f'其他项目往来 {transaction_no} 已删除', 'success')
        return jsonify({'success': True, 'message': f'其他项目往来 {transaction_no} 已删除'})
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除其他项目往来记录失败: {str(e)}")
        flash(f'删除失败：{str(e)}', 'error')
        return jsonify({'success': False, 'message': f'删除失败：{str(e)}'}), 500

# 供应商往来路由
@app.route('/supplier_transactions')
@login_required
def supplier_transactions():
    """供应商往来记录列表"""
    search = request.args.get('search', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    page = request.args.get('page', 1, type=int)
    per_page = 20  # 每页显示20条记录
    
    # 如果没有设置日期筛选，默认使用当天
    if not start_date and not end_date:
        today = datetime.now().strftime('%Y-%m-%d')
        start_date = today
        end_date = today
    elif start_date and not end_date:
        end_date = start_date
    
    # 构建查询
    query = SupplierTransaction.query
    
    # 添加日期筛选
    if start_date:
        query = query.filter(SupplierTransaction.business_date >= datetime.strptime(start_date, '%Y-%m-%d').date())
    if end_date:
        query = query.filter(SupplierTransaction.business_date <= datetime.strptime(end_date, '%Y-%m-%d').date())
    
    # 添加搜索条件
    if search:
        query = query.join(Supplier).filter(
            or_(
                Supplier.name.contains(search),
                SupplierTransaction.transaction_no.contains(search),
                SupplierTransaction.notes.contains(search)
            )
        )
    
    # 按业务日期倒序排序并分页
    pagination = query.order_by(SupplierTransaction.business_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    transactions = pagination.items
    
    # 获取所有供应商
    suppliers = Supplier.query.order_by(Supplier.name).all()
    
    return render_template('supplier_transactions/index.html',
                         transactions=transactions,
                         suppliers=suppliers,
                         pagination=pagination,
                         search=search,
                         start_date=start_date,
                         end_date=end_date)

@app.route('/supplier_transactions/add', methods=['GET', 'POST'])
@login_required
def supplier_transaction_add():
    if request.method == 'POST':
        try:
            # 导入服务类
            from supplier_transaction_service import SupplierTransactionService

            data = request.get_json()
            app.logger.info(f"收到供应商往来记录数据: {json.dumps(data, ensure_ascii=False)}")

            # 检查必填字段是否存在
            required_fields = ['supplier_id', 'business_date']
            for field in required_fields:
                if field not in data or not data[field]:
                    app.logger.error(f"缺少必填字段 {field}")
                    return jsonify({"success": False, "message": f"缺少必填字段: {field}"})
            
            # 解构数据对象，剔除material_transactions和money_transactions字段
            material_transactions_data = data.get('material_transactions', [])
            money_transactions_data = data.get('money_transactions', [])
            account_transactions_data = data.get('account_transactions', [])  # 获取账户交易数据
            
            # 日志记录收到的数据量
            app.logger.info(f"收到的物料交易数量: {len(material_transactions_data)}")
            app.logger.info(f"收到的款项交易数量: {len(money_transactions_data)}")
            app.logger.info(f"收到的账户交易数量: {len(account_transactions_data)}")
            
            # 移除这些字段以免影响SupplierTransaction构造函数
            transaction_data = {k: v for k, v in data.items() if k not in ['material_transactions', 'money_transactions', 'updated_supplier_info', 'account_transactions']}
            
            # 将business_date字符串转换为date对象，支持两种格式
            if 'business_date' in transaction_data and isinstance(transaction_data['business_date'], str):
                business_date_str = transaction_data['business_date']
                try:
                    if 'T' in business_date_str:
                        # datetime-local格式：YYYY-MM-DDTHH:MM
                        # 设置business_date（只保存日期部分）
                        transaction_data['business_date'] = datetime.strptime(business_date_str, '%Y-%m-%dT%H:%M').date()
                        # 设置created_at（保存完整的日期时间）
                        transaction_data['created_at'] = datetime.strptime(business_date_str, '%Y-%m-%dT%H:%M')
                        app.logger.info(f"新增模式设置录入时间: business_date={transaction_data['business_date']}, created_at={transaction_data['created_at']}")
                    else:
                        # date格式：YYYY-MM-DD
                        transaction_data['business_date'] = datetime.strptime(business_date_str, '%Y-%m-%d').date()
                        app.logger.info(f"新增模式只设置业务日期: business_date={transaction_data['business_date']}")
                except ValueError as e:
                    app.logger.error(f"解析business_date失败: {business_date_str}, 错误: {str(e)}")
                    return jsonify({"success": False, "message": f"日期格式错误: {business_date_str}"})
            
            # 添加上期数据字段 - 正确的级联逻辑
            if 'supplier_id' in transaction_data:
                supplier_id = transaction_data['supplier_id']
                supplier = Supplier.query.get(supplier_id)
                if supplier:
                    # 查找该供应商的最后一个往来记录
                    last_transaction = SupplierTransaction.query.filter_by(
                        supplier_id=supplier_id
                    ).order_by(SupplierTransaction.business_date.desc(), SupplierTransaction.id.desc()).first()

                    # === 不再保存上期数据到数据库，统一使用API动态计算 ===
                    app.logger.info("不再保存上期数据到数据库，统一使用API动态计算")
            
            # 验证供应商是否存在
            supplier = Supplier.query.get(transaction_data['supplier_id'])
            if not supplier:
                return jsonify({"success": False, "message": "找不到指定的供应商"})

            # 创建供应商往来记录
            supplier_transaction = SupplierTransaction(**transaction_data)

            # 再次检查交易编号是否已存在，若已存在则生成新编号
            today = datetime.now().strftime('%y%m%d')
            if supplier_transaction.transaction_no:
                existing = SupplierTransaction.query.filter_by(
                    transaction_no=supplier_transaction.transaction_no
                ).first()

                if existing:
                    app.logger.warning(f"交易编号 {supplier_transaction.transaction_no} 已存在，正在生成新编号")
                    # 查询最大编号并加1
                    try:
                        max_transaction = db.session.query(SupplierTransaction).filter(
                            SupplierTransaction.transaction_no.like(f"{today}%")
                        ).order_by(SupplierTransaction.transaction_no.desc()).with_for_update().first()
                        
                        if max_transaction and max_transaction.transaction_no:
                            try:
                                # 尝试从编号中提取序列号部分
                                last_seq = int(max_transaction.transaction_no[8:])
                                # 生成新序列号，并确保是4位数
                                supplier_transaction.transaction_no = f"{today}{(last_seq + 1):04d}"
                                app.logger.info(f"基于最大交易编号生成新编号: {supplier_transaction.transaction_no}")
                            except (ValueError, IndexError):
                                # 使用微秒级时间戳确保唯一性
                                timestamp = int(datetime.now().timestamp() * 1000000) % 10000
                                supplier_transaction.transaction_no = f"{today}{timestamp:04d}"
                                app.logger.info(f"无法解析现有编号，使用微秒时间戳生成: {supplier_transaction.transaction_no}")
                        else:
                            # 使用微秒级时间戳和随机数结合确保唯一性
                            random_suffix = f"{int(datetime.now().timestamp() * 1000000) % 10000:04d}"
                            supplier_transaction.transaction_no = f"{today}{random_suffix[-4:]}"
                            app.logger.info(f"未找到最大交易编号，使用时间戳: {supplier_transaction.transaction_no}")
                            
                        # 再次验证唯一性
                        duplicate_check = SupplierTransaction.query.filter_by(
                            transaction_no=supplier_transaction.transaction_no
                        ).first()
                        
                        if duplicate_check:
                            # 如果仍然存在冲突，使用当前微秒时间戳加随机数
                            timestamp = int(datetime.now().timestamp() * 1000000)
                            random_num = random.randint(1000, 9999)
                            supplier_transaction.transaction_no = f"{today}{(timestamp + random_num) % 10000:04d}"
                            app.logger.warning(f"检测到二次冲突，使用时间戳+随机数: {supplier_transaction.transaction_no}")
                    except Exception as e:
                        # 出现任何错误，使用备用方案
                        app.logger.error(f"重新生成交易编号时出错: {str(e)}")
                        # 使用当前毫秒时间戳作为后缀
                        timestamp = int(datetime.now().timestamp() * 1000)
                        supplier_transaction.transaction_no = f"{today}{timestamp % 10000:04d}"
                
                app.logger.info(f"最终使用的交易编号: {supplier_transaction.transaction_no}")
            
            # 使用新的统一服务处理数据
            app.logger.info(f"开始处理 {len(material_transactions_data)} 条物料交易数据")

            # 创建物料交易记录
            for idx, mt_data in enumerate(material_transactions_data):
                app.logger.info(f"处理第 {idx+1} 条物料数据: {json.dumps(mt_data, ensure_ascii=False)}")

                return_weight = mt_data.get('return_weight', 0)
                store_weight = mt_data.get('store_weight', 0)
                deposit_weight = mt_data.get('deposit_weight', 0)

                if return_weight == 0 and store_weight == 0 and deposit_weight == 0:
                    app.logger.warning(f"跳过权重为0的物料交易: {json.dumps(mt_data, ensure_ascii=False)}")
                    continue

                # 确保note字段存在
                if 'note' not in mt_data:
                    mt_data['note'] = None

                # 过滤掉已删除的字段
                filtered_mt_data = {k: v for k, v in mt_data.items() if k != 'actual_return_weight'}

                # 创建新的物料交易对象并关联到供应商往来记录
                mt = MaterialTransaction(**filtered_mt_data)
                app.logger.info(f"创建物料交易对象: {mt}")
                supplier_transaction.material_transactions.append(mt)

                # 金料供应商欠料变化由统一服务处理，避免重复处理
            
            # 处理款项交易
            app.logger.info(f"开始处理 {len(money_transactions_data)} 条款项交易数据")
            for idx, mt_data in enumerate(money_transactions_data):
                app.logger.info(f"处理第 {idx+1} 条款项数据: {json.dumps(mt_data, ensure_ascii=False)}")
                
                return_amount = mt_data.get('return_amount', 0)
                store_amount = mt_data.get('store_amount', 0)
                
                if return_amount == 0 and store_amount == 0:
                    app.logger.warning(f"跳过金额为0的款项交易: {json.dumps(mt_data, ensure_ascii=False)}")
                    continue
                
                # 确保note字段存在
                if 'note' not in mt_data:
                    mt_data['note'] = None
                
                # 如果是买料相关，确保return_amount是重量的两位小数倍
                if mt_data.get('return_purpose') == '买料' and mt_data.get('return_weight', 0) > 0:
                    weight = float(mt_data.get('return_weight', 0))
                    total_amount = float(mt_data.get('return_amount', 0))
                    
                    # 计算料价，保留两位小数
                    price = round(total_amount / weight, 2) if weight > 0 else 0
                    app.logger.info(f"买料料价计算: {total_amount} / {weight} = {price} (两位小数)")
                    
                    # 使用两位小数料价重新计算总金额
                    mt_data['return_amount'] = round(price * weight, 2)
                    app.logger.info(f"修正后的买料金额: {price} * {weight} = {mt_data['return_amount']}")
                
                # 创建新的款项交易对象并关联到供应商往来记录
                mt = MoneyTransaction(**mt_data)
                app.logger.info(f"创建款项交易对象: {mt}")
                supplier_transaction.money_transactions.append(mt)
            
            # 保存到数据库
            db.session.add(supplier_transaction)
            
            app.logger.info(f"供应商往来记录添加到会话中，ID={supplier_transaction.id if supplier_transaction.id else '未生成'}")
            app.logger.info(f"关联的物料交易数量: {len(supplier_transaction.material_transactions)}")
            app.logger.info(f"关联的款项交易数量: {len(supplier_transaction.money_transactions)}")
            
            # 提交前检查是否有数据
            if len(supplier_transaction.material_transactions) == 0 and len(supplier_transaction.money_transactions) == 0:
                app.logger.warning(f"警告：没有有效的物料或款项交易数据")
            
            # 先保存基础交易记录
            db.session.add(supplier_transaction)

            try:
                db.session.commit()
                app.logger.info(f"供应商往来记录成功保存，ID={supplier_transaction.id}")

                # 使用统一数据管理器应用所有数据变化
                app.logger.info("开始使用统一数据管理器应用交易影响")
                from unified_supplier_transaction_manager import supplier_transaction_manager
                success = supplier_transaction_manager.apply_transaction_effects(supplier_transaction)

                if not success:
                    db.session.rollback()
                    return jsonify({"success": False, "message": "应用交易影响失败"})

                # 提交所有变化
                db.session.commit()
                app.logger.info("所有交易影响应用完成")
                
                # 处理账户交易数据 - 更新账户余额并创建账户交易记录
                if account_transactions_data:
                    app.logger.info(f"开始处理 {len(account_transactions_data)} 条账户交易数据")
                    for idx, at_data in enumerate(account_transactions_data):
                        app.logger.info(f"处理第 {idx+1} 条账户交易数据: {json.dumps(at_data, ensure_ascii=False)}")
                        
                        account_name = at_data.get('account_name')
                        amount = at_data.get('amount', 0)
                        
                        if not account_name or amount == 0:
                            app.logger.warning(f"跳过无效的账户交易数据: {json.dumps(at_data, ensure_ascii=False)}")
                            continue
                        
                        # 查找账户
                        account = Account.query.filter_by(name=account_name).first()
                        if not account:
                            app.logger.warning(f"找不到账户: {account_name}，跳过此账户交易")
                            continue
                        
                        # 账户余额由统一服务处理，这里不需要重复修改
                        app.logger.info(f"账户 {account_name} 的余额变化将由统一服务处理: {amount}元")
                        
                        # 创建账户交易记录
                        transaction_type = at_data.get('transaction_type', 'expense')
                        purpose = at_data.get('purpose', '供应商交易')
                        related_party = at_data.get('related_party', '')
                        notes = at_data.get('notes', '')
                        
                        # 读取供应商名称
                        supplier = Supplier.query.get(data['supplier_id'])
                        supplier_name = supplier.name if supplier else "未知供应商"
                        
                        # 创建账户交易记录
                        account_transaction = AccountTransaction(
                            account_id=account.id,
                            transaction_date=transaction_data['business_date'],
                            amount=abs(amount),  # 存储绝对值
                            transaction_type=transaction_type,
                            purpose=purpose,
                            related_party=related_party or supplier_name,
                            related_document=supplier_transaction.transaction_no,
                            notes=notes or f"供应商往来交易: {supplier_transaction.transaction_no}",
                            supplier_id=data['supplier_id'],
                            supplier_transaction_id=supplier_transaction.id
                        )
                        
                        db.session.add(account_transaction)
                    
                    try:
                        db.session.commit()
                        app.logger.info(f"成功处理账户交易数据")
                    except Exception as e:
                        db.session.rollback()
                        app.logger.error(f"更新账户数据失败: {str(e)}")
                        # 不返回错误，因为主要交易已经成功
                
                # 新增单据成功后，触发级联更新后续单据
                try:
                    from supplier_transaction_cascade_service import SupplierTransactionCascadeService
                    app.logger.info(f"新增单据成功，开始级联更新后续单据")

                    # 计算当前单据的结果值（用于级联更新）
                    current_owed_gold = supplier_transaction.previous_owed_gold or 0.0
                    current_deposit_gold = supplier_transaction.previous_deposit_gold or 0.0
                    current_owed_amount = supplier_transaction.previous_owed_amount or 0.0
                    current_deposit_amount = supplier_transaction.previous_deposit_amount or 0.0

                    # 加上当前单据的变化
                    for mt in supplier_transaction.material_transactions:
                        if mt.return_weight:
                            current_owed_gold -= mt.return_weight
                        if mt.store_weight:
                            current_owed_gold += mt.store_weight
                        if mt.deposit_weight:
                            current_deposit_gold += mt.deposit_weight

                    for money_t in supplier_transaction.money_transactions:
                        if money_t.return_amount:
                            current_owed_amount -= money_t.return_amount
                        if money_t.store_amount:
                            current_deposit_amount += money_t.store_amount

                    # 触发级联更新
                    cascade_success = SupplierTransactionCascadeService.update_subsequent_transactions_previous_values(
                        supplier_transaction.supplier_id,
                        supplier_transaction.id,
                        current_owed_gold,
                        current_deposit_gold,
                        current_owed_amount,
                        current_deposit_amount
                    )

                    if not cascade_success:
                        app.logger.warning(f"新增单据 {supplier_transaction.id} 级联更新失败，但单据本身已保存")

                except Exception as e:
                    app.logger.error(f"新增单据级联更新失败: {str(e)}")
                    # 不影响主要保存流程

                return jsonify({"success": True, "transaction_id": supplier_transaction.id})
            except SQLAlchemyError as e:
                db.session.rollback()
                error_msg = str(e)
                app.logger.error(f"数据库错误: {error_msg}")
                
                # 检查是否是唯一约束错误
                if "UNIQUE constraint failed" in error_msg and "transaction_no" in error_msg:
                    app.logger.warning(f"检测到交易编号唯一性冲突: {supplier_transaction.transaction_no}，自动重新生成编号")
                    
                    # 自动重新生成编号
                    today = datetime.now().strftime('%y%m%d')
                    prefix = "GYS"  # 添加供应商往来前缀
                    timestamp = int(datetime.now().timestamp() * 1000000)
                    random_num = random.randint(1000, 9999)
                    supplier_transaction.transaction_no = f"{prefix}-{today}-{(timestamp + random_num) % 10000:04d}"
                    
                    app.logger.info(f"自动生成新编号: {supplier_transaction.transaction_no}，重新尝试保存")
                    
                    # 重新尝试保存
                    try:
                        db.session.add(supplier_transaction)
                        db.session.commit()
                        app.logger.info(f"使用新编号成功保存交易记录，ID={supplier_transaction.id}")
                        return jsonify({
                            "success": True, 
                            "transaction_id": supplier_transaction.id,
                            "info": "系统检测到编号冲突，已自动使用新编号保存记录"
                        })
                    except Exception as inner_e:
                        db.session.rollback()
                        app.logger.error(f"重新保存失败: {str(inner_e)}")
                        return jsonify({
                            "success": False, 
                            "message": f"尝试使用新编号保存失败: {str(inner_e)}"
                        })
                    
                return jsonify({"success": False, "message": f"数据库错误: {error_msg}"})
        except Exception as e:
            db.session.rollback()
            error_msg = str(e)
            app.logger.error(f"发生异常: {error_msg}")
            app.logger.error(traceback.format_exc())
            return jsonify({"success": False, "message": f"处理请求时出错: {error_msg}"})
    
    # GET 请求逻辑（显示添加表单）
    suppliers = Supplier.query.all()
    
    # 生成交易编号 - 更可靠的方式
    today = datetime.now().strftime('%y%m%d')
    prefix = "GYS"  # 添加供应商往来前缀
    # 查询当天最大的交易编号，确保生成唯一编号
    try:
        # 使用带锁的查询以防止并发问题
        max_transaction = db.session.query(SupplierTransaction).filter(
            SupplierTransaction.transaction_no.like(f"{prefix}-{today}%")
        ).order_by(SupplierTransaction.transaction_no.desc()).with_for_update().first()
        
        if max_transaction and max_transaction.transaction_no:
            # 如果已存在交易，从最大的编号提取序号并加1
            try:
                # 尝试从编号中提取序列号部分
                parts = max_transaction.transaction_no.split('-')
                if len(parts) == 3:
                    last_seq = int(parts[2])
                    # 生成新序列号，并确保是4位数
                    transaction_no = f"{prefix}-{today}-{(last_seq + 1):04d}"
                else:
                    # 回退到老式格式
                    last_seq = int(max_transaction.transaction_no[8:])
                    transaction_no = f"{prefix}-{today}-{(last_seq + 1):04d}"
                app.logger.info(f"基于最大交易编号生成新编号: {transaction_no}")
            except (ValueError, IndexError):
                # 如果解析出错，使用微秒级时间戳确保唯一性
                timestamp = int(datetime.now().timestamp() * 1000000) % 10000
                transaction_no = f"{prefix}-{today}-{timestamp:04d}"
                app.logger.info(f"无法解析现有编号，使用时间戳生成: {transaction_no}")
        else:
            # 没有当天交易，从1开始
            transaction_no = f"{prefix}-{today}-0001"
            app.logger.info(f"当天无交易记录，使用初始编号: {transaction_no}")
            
        # 额外验证确保生成的编号不会重复
        duplicate_check = SupplierTransaction.query.filter_by(transaction_no=transaction_no).first()
        if duplicate_check:
            # 使用微秒级时间戳和随机数结合确保唯一性
            random_suffix = f"{int(datetime.now().timestamp() * 1000000) % 10000:04d}{random.randint(1000, 9999):04d}"
            transaction_no = f"{prefix}-{today}-{random_suffix[-4:]}"
            app.logger.warning(f"检测到生成的编号已存在，使用时间戳+随机数: {transaction_no}")
    except Exception as e:
        # 出现任何错误，使用备用方案
        app.logger.error(f"生成交易编号时出错: {str(e)}")
        # 使用当前时间的微秒级时间戳作为后缀
        timestamp = int(datetime.now().timestamp() * 1000000) % 10000
        transaction_no = f"{today}{timestamp:04d}"
        app.logger.info(f"使用备用方案生成编号: {transaction_no}")
    
    app.logger.info(f"为新交易生成编号: {transaction_no}")
    
    # 将供应商对象转换为字典以支持JSON序列化
    suppliers_data = []
    for supplier in suppliers:
        suppliers_data.append({
            'id': supplier.id,
            'name': supplier.name,
            'supplier_type': supplier.supplier_type,
            'contact_person': supplier.contact_person,
            'phone': supplier.phone,
            'owed_amount': supplier.owed_amount or 0,
            'owed_gold': supplier.owed_gold or 0,
            'deposit_amount': supplier.deposit_amount or 0,
            'deposit_gold': supplier.deposit_gold or 0
        })

    # 计算默认的上期余额数据（避免页面闪烁）
    # 使用0作为默认值，当用户选择供应商时会通过JavaScript更新为实际值
    default_previous_balance = {
        'previous_owed_gold': 0.00,
        'previous_deposit_gold': 0.00,
        'previous_owed_amount': 0.00,
        'previous_deposit_amount': 0.00
    }

    return render_template('supplier_transactions/add.html',
                         suppliers=suppliers_data,
                         transaction_no=transaction_no,
                         today_datetime=datetime.now().strftime('%Y-%m-%dT%H:%M'),
                         default_previous_balance=default_previous_balance)

@app.route('/supplier_transactions/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def supplier_transaction_edit(id):
    """编辑供应商往来记录"""
    try:
        # 获取供应商往来记录
        app.logger.info(f"尝试访问供应商往来编辑页面，ID: {id}")
        transaction = SupplierTransaction.query.get(id)
        
        if not transaction:
            app.logger.error(f"未找到ID为{id}的供应商往来记录")
            flash(f'未找到ID为{id}的供应商往来记录', 'error')
            return redirect(url_for('supplier_transactions'))
        
        if request.method == 'POST':
            try:
                # 获取请求中的JSON数据
                data = request.get_json()
                if not data:
                    return jsonify({'success': False, 'message': '无效的请求数据'}), 400
                
                app.logger.info(f"接收到更新供应商往来数据: {data}")
                
                # 导入统一数据管理器
                from unified_supplier_transaction_manager import supplier_transaction_manager

                # 更新基本信息
                transaction.supplier_id = data.get('supplier_id')

                # 解析business_date，支持两种格式
                business_date_str = data.get('business_date')
                if business_date_str:
                    try:
                        if 'T' in business_date_str:
                            # datetime-local格式：YYYY-MM-DDTHH:MM
                            # 更新business_date（只保存日期部分）
                            transaction.business_date = datetime.strptime(business_date_str, '%Y-%m-%dT%H:%M').date()
                            # 更新created_at（保存完整的日期时间）
                            transaction.created_at = datetime.strptime(business_date_str, '%Y-%m-%dT%H:%M')
                            app.logger.info(f"更新录入时间: business_date={transaction.business_date}, created_at={transaction.created_at}")
                        else:
                            # date格式：YYYY-MM-DD
                            transaction.business_date = datetime.strptime(business_date_str, '%Y-%m-%d').date()
                            # 如果只有日期，保持原有的created_at时间部分
                            app.logger.info(f"只更新业务日期: business_date={transaction.business_date}")
                    except ValueError as e:
                        app.logger.error(f"解析business_date失败: {business_date_str}, 错误: {str(e)}")
                        return jsonify({'success': False, 'message': f'日期格式错误: {business_date_str}'}), 400

                transaction.notes = data.get('notes', '')

                # 保留历史余额数据（编辑时不应该改变历史数据）
                # 重要：编辑时不应该修改历史数据，历史数据应该保持不变
                # 只有在级联更新时才会修改历史数据
                app.logger.info(f"编辑交易记录 {transaction.id}，保持历史余额数据不变")
                app.logger.info(f"  当前历史数据: 欠料{transaction.previous_owed_gold}g, 存料{transaction.previous_deposit_gold}g")
                app.logger.info(f"  当前历史数据: 欠款¥{transaction.previous_owed_amount}, 存款¥{transaction.previous_deposit_amount}")

                # 注释掉历史数据的更新，防止被前端数据错误覆盖
                # if 'previous_owed_gold' in data:
                #     transaction.previous_owed_gold = float(data.get('previous_owed_gold', 0))
                # if 'previous_deposit_gold' in data:
                #     transaction.previous_deposit_gold = float(data.get('previous_deposit_gold', 0))
                # if 'previous_owed_amount' in data:
                #     transaction.previous_owed_amount = float(data.get('previous_owed_amount', 0))
                # if 'previous_deposit_amount' in data:
                #     transaction.previous_deposit_amount = float(data.get('previous_deposit_amount', 0))

                # 获取供应商信息用于后续处理
                supplier = Supplier.query.get(transaction.supplier_id)
                if not supplier:
                    return jsonify({'success': False, 'message': '找不到供应商'}), 400

                # 使用统一服务回滚原始交易的影响
                app.logger.info(f"开始回滚交易 {id} 的原始影响")
                from supplier_transaction_service import SupplierTransactionService
                SupplierTransactionService.rollback_transaction_effects(id)

                # 删除原有的料项和款项交易记录
                app.logger.info("删除原有的交易记录...")
                MaterialTransaction.query.filter_by(transaction_id=transaction.id).delete()
                MoneyTransaction.query.filter_by(transaction_id=transaction.id).delete()

                # 创建新的物料和款项交易记录
                material_transactions_data = data.get('material_transactions', [])
                money_transactions_data = data.get('money_transactions', [])

                app.logger.info(f"创建新的交易记录: {len(material_transactions_data)} 条物料交易, {len(money_transactions_data)} 条款项交易")

                # 创建新的物料交易记录
                for mt_data in material_transactions_data:
                    mt = MaterialTransaction(
                        transaction_id=transaction.id,
                        return_weight=mt_data.get('return_weight', 0),
                        return_material_type=mt_data.get('return_material_type', ''),
                        return_source=mt_data.get('return_source', ''),
                        wastage_loss=mt_data.get('wastage_loss', 0),
                        store_weight=mt_data.get('store_weight', 0),
                        store_material_type=mt_data.get('store_material_type', ''),
                        store_source=mt_data.get('store_source', ''),
                        deposit_weight=mt_data.get('deposit_weight', 0),
                        deposit_material_type=mt_data.get('deposit_material_type', ''),
                        actual_deposit_weight=mt_data.get('actual_deposit_weight', 0),
                        deposit_loss=mt_data.get('deposit_loss', 0),
                        note=mt_data.get('note', '')
                    )
                    db.session.add(mt)

                # 创建新的款项交易记录
                for mt_data in money_transactions_data:
                    mt = MoneyTransaction(
                        transaction_id=transaction.id,
                        return_amount=mt_data.get('return_amount', 0),
                        return_source=mt_data.get('return_source', ''),
                        return_purpose=mt_data.get('return_purpose', '还款'),
                        return_material_type=mt_data.get('return_material_type', ''),
                        return_weight=mt_data.get('return_weight', 0),
                        store_amount=mt_data.get('store_amount', 0),
                        store_source=mt_data.get('store_source', ''),
                        note=mt_data.get('note', '')
                    )
                    db.session.add(mt)

                # 先计算原始影响数据（修改前的数据）
                app.logger.info("计算原始影响数据用于回滚")
                original_effects = supplier_transaction_manager.calculate_transaction_effects(transaction)

                # 使用统一数据管理器处理数据变化（先回滚再应用）
                app.logger.info("开始使用统一数据管理器处理数据变化")

                # 先回滚原始影响
                success = supplier_transaction_manager.rollback_transaction_effects(transaction, original_effects)
                if not success:
                    db.session.rollback()
                    return jsonify({'success': False, 'message': '回滚原始数据失败'}), 500

                # 再应用新的影响
                success = supplier_transaction_manager.apply_transaction_effects(transaction, rollback_first=False)
                if not success:
                    db.session.rollback()
                    return jsonify({'success': False, 'message': '应用新数据失败'}), 500

                # 提交当前交易的变化
                db.session.commit()
                app.logger.info(f"交易记录更新成功，ID: {transaction.id}")

                # 执行级联更新整个记录链
                app.logger.info(f"开始级联更新供应商往来记录链")
                cascade_success = supplier_transaction_manager.update_transaction_chain(
                    transaction.supplier_id,
                    transaction.id
                )

                if not cascade_success:
                    app.logger.warning(f"供应商往来记录 {transaction.id} 级联更新失败，但记录本身已保存")

                return jsonify({'success': True, 'message': '更新成功'})
            
            except ValueError as e:
                db.session.rollback()
                app.logger.error(f"数据格式错误: {str(e)}")
                return jsonify({'success': False, 'message': f'数据格式错误: {str(e)}'}), 400
            except Exception as e:
                db.session.rollback()
                app.logger.error(f"更新供应商往来记录失败: {str(e)}")
                return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500
        
        # GET请求处理
        app.logger.info(f"开始渲染供应商往来编辑页面，ID: {id}")
        
        suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.name).all()
        app.logger.info(f"找到 {len(suppliers)} 个供应商")
        
        # 获取所有账户
        accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
        app.logger.info(f"找到 {len(accounts)} 个账户")
        
        # 获取所有物料来源
        material_sources = MaterialSource.query.filter_by(is_active=True).order_by(MaterialSource.name).all()
        app.logger.info(f"找到 {len(material_sources)} 个物料来源")
        
        # 获取所有旧料类型
        old_materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.name).all()
        app.logger.info(f"找到 {len(old_materials)} 个旧料类型")
        
        # 获取所有金料供应商
        gold_material_suppliers = Supplier.query.filter_by(supplier_type='gold_material', is_active=True).order_by(Supplier.name).all()
        app.logger.info(f"找到 {len(gold_material_suppliers)} 个金料供应商")
        
        # 获取交易的物料和款项
        material_transactions = MaterialTransaction.query.filter_by(transaction_id=transaction.id).all()
        money_transactions = MoneyTransaction.query.filter_by(transaction_id=transaction.id).all()
        
        app.logger.info(f"交易ID {id} 关联的物料交易数量: {len(material_transactions)}")
        app.logger.info(f"交易ID {id} 关联的款项交易数量: {len(money_transactions)}")
        
        # 设置transaction的关联数据
        transaction.material_transactions = material_transactions
        transaction.money_transactions = money_transactions
        
        # 计算初始状态 - 将交易影响反向计算
        supplier = Supplier.query.get(transaction.supplier_id)
        
        app.logger.info(f"开始渲染add.html模板，传递交易ID: {transaction.id}")
        
        # 计算实际的上期余额数据（避免页面闪烁）
        from routes.report_routes import calculate_supplier_previous_balance

        # 使用录入时间计算上期余额，排除当前交易
        business_datetime = transaction.created_at or transaction.business_date
        calculated_balance = calculate_supplier_previous_balance(
            transaction.supplier_id,
            business_datetime,
            transaction.id  # 排除当前交易
        )

        if calculated_balance:
            default_previous_balance = {
                'previous_owed_gold': calculated_balance.get('previous_owed_gold', 0.00),
                'previous_deposit_gold': calculated_balance.get('previous_deposit_gold', 0.00),
                'previous_owed_amount': calculated_balance.get('previous_owed_amount', 0.00),
                'previous_deposit_amount': calculated_balance.get('previous_deposit_amount', 0.00)
            }
        else:
            default_previous_balance = {
                'previous_owed_gold': 0.00,
                'previous_deposit_gold': 0.00,
                'previous_owed_amount': 0.00,
                'previous_deposit_amount': 0.00
            }

        # 预计算旧料明细数据
        supplier_old_materials = None
        if transaction.supplier_id:
            try:
                # 获取供应商旧料明细
                from routes.report_routes import get_supplier_material_details_at_date
                supplier_old_materials = get_supplier_material_details_at_date(
                    transaction.supplier_id,
                    business_datetime,
                    exclude_transaction_id=transaction.id
                )
            except Exception as e:
                app.logger.error(f"获取供应商旧料明细失败: {str(e)}")
                supplier_old_materials = None

        # 预计算本期数据和总计数据
        current_period_data = calculate_current_period_data(transaction)
        total_balance_data = calculate_total_balance_data(default_previous_balance, current_period_data)

        # 调试信息
        app.logger.info(f"编辑模式数据传递:")
        app.logger.info(f"  supplier_old_materials: {supplier_old_materials}")
        app.logger.info(f"  current_period_data: {current_period_data}")
        app.logger.info(f"  total_balance_data: {total_balance_data}")
        app.logger.info(f"  material_transactions数量: {len(transaction.material_transactions)}")
        app.logger.info(f"  money_transactions数量: {len(transaction.money_transactions)}")

        # 使用add.html模板，传递edit_mode=True，这样只需要一个模板即可
        return render_template('supplier_transactions/add.html',
                           transaction=transaction,
                           suppliers=suppliers,
                           accounts=accounts,
                           material_sources=material_sources,
                           old_materials=old_materials,
                           gold_material_suppliers=gold_material_suppliers,
                           edit_mode=True,
                           app_name=app.config.get('APP_NAME', '金料管理系统'),
                           today_datetime=datetime.now().strftime('%Y-%m-%dT%H:%M'),
                           default_previous_balance=default_previous_balance,
                           supplier_old_materials=supplier_old_materials,
                           current_period_data=current_period_data,
                           total_balance_data=total_balance_data)
    
    except Exception as e:
        app.logger.error(f"处理供应商往来编辑请求时出错: {str(e)}")
        app.logger.error(traceback.format_exc())
        flash(f'处理请求时出错: {str(e)}', 'error')
        return redirect(url_for('supplier_transactions'))

@app.route('/supplier_transactions/<int:id>/delete', methods=['POST'])
@login_required
def supplier_transaction_delete(id):
    """删除供应商往来记录，使用统一服务回滚所有相关数据变化"""
    try:
        # 获取交易记录及关联的供应商
        transaction = SupplierTransaction.query.get_or_404(id)
        supplier = Supplier.query.get(transaction.supplier_id)

        if not supplier:
            app.logger.error(f"删除失败: 找不到供应商ID={transaction.supplier_id}")
            return jsonify({'success': False, 'message': '找不到关联的供应商'}), 404

        app.logger.info(f"开始删除供应商往来记录: ID={id}, 供应商={supplier.name}")

        # 记录删除前的供应商状态
        app.logger.info(f"删除前供应商状态: 欠料={supplier.owed_gold}克, 存料={supplier.deposit_gold}克, " +
                        f"欠款={supplier.owed_amount}元, 存款={supplier.deposit_amount}元")

        # 使用统一服务回滚交易影响
        app.logger.info("使用统一服务回滚交易影响")
        from supplier_transaction_service import SupplierTransactionService
        SupplierTransactionService.rollback_transaction_effects(id)

        # 记录删除后的供应商状态
        app.logger.info(f"删除后供应商状态: 欠料={supplier.owed_gold}克, 存料={supplier.deposit_gold}克, " +
                        f"欠款={supplier.owed_amount}元, 存款={supplier.deposit_amount}元")

        # 删除关联的账户交易记录
        account_transactions = AccountTransaction.query.filter_by(supplier_transaction_id=id).all()
        for at in account_transactions:
            db.session.delete(at)
            app.logger.info(f"删除关联账户交易记录: ID={at.id}")
        
        
        # 删除关联的物料和款项交易记录
        MaterialTransaction.query.filter_by(transaction_id=id).delete()
        MoneyTransaction.query.filter_by(transaction_id=id).delete()
        
        # 删除主记录
        db.session.delete(transaction)
        db.session.commit()
        
        app.logger.info(f"供应商往来记录删除成功: ID={id}")
        flash('供应商往来记录已删除', 'success')
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除供应商往来记录失败: {str(e)}")
        # 打印完整堆栈跟踪
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 500

@app.route('/other_transactions')
@login_required
def other_transactions():
    """其他往来记录列表"""
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    customer_id = request.args.get('customer_id', '', type=str)

    # 构建查询
    query = Transaction.query

    # 日期过滤
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(func.date(Transaction.business_time) >= start_date_obj)
        except ValueError:
            start_date = ''
            start_date_obj = None
    else:
        start_date_obj = None

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(func.date(Transaction.business_time) <= end_date_obj)
        except ValueError:
            end_date = ''
            end_date_obj = None
    else:
        end_date_obj = None

    # 客户过滤
    if customer_id:
        query = query.filter(Transaction.customer_id == customer_id)

    # 分页
    pagination = query.order_by(Transaction.business_time.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    transactions = pagination.items

    # 获取客户列表用于筛选
    customers = Customer.query.all()

    return render_template('transactions/index.html',
                         transactions=transactions,
                         pagination=pagination,
                         start_date=start_date,  # 传递字符串而不是日期对象
                         end_date=end_date,      # 传递字符串而不是日期对象
                         customer_id=int(customer_id) if customer_id else None,
                         customers=customers)

@app.route('/material_adjustments')
@login_required
def material_adjustments():
    """货料调整记录列表"""
    # 获取搜索参数
    search = request.args.get('search', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    page = request.args.get('page', 1, type=int)

    # 如果没有提供日期，默认为今天
    if not start_date:
        start_date = datetime.now().strftime('%Y-%m-%d')
    if not end_date:
        end_date = start_date

    # 创建基础查询
    query = MaterialAdjustment.query

    # 添加日期筛选条件
    if start_date:
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d').date()
        query = query.filter(MaterialAdjustment.business_date >= start_datetime)

    if end_date:
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d').date()
        query = query.filter(MaterialAdjustment.business_date <= end_datetime)

    # 添加搜索条件
    if search:
        query = query.filter(or_(
            MaterialAdjustment.adjustment_no.contains(search),
            MaterialAdjustment.notes.contains(search)
        ))

    # 按创建时间降序排序
    query = query.order_by(MaterialAdjustment.created_at.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=20, error_out=False)
    adjustments = pagination.items

    return render_template('material_adjustments/index.html',
                         adjustments=adjustments,
                         pagination=pagination,
                         search=search,
                         start_date=start_date,
                         end_date=end_date)

@app.route('/material_adjustments/add', methods=['GET', 'POST'])
@login_required
def material_adjustments_add():
    """货料调整录入页面"""
    if request.method == 'GET':
        # 获取旧料列表，按照旧料信息表的顺序（按编号排序）
        old_materials = OldMaterial.query.order_by(OldMaterial.code).all()
        old_materials_list = []
        from routes.report_routes import calculate_old_material_inventory_at_date
        from datetime import date

        for om in old_materials:
            # 使用正向计算的旧料库存
            current_stock = calculate_old_material_inventory_at_date(om, date.today())
            old_materials_list.append({
                'id': om.id,
                'name': om.name,
                'department_stock': current_stock
            })

        # 获取产品列表，按照产品信息表的顺序（按编号排序）
        from routes.report_routes import calculate_product_inventory_at_date
        from datetime import date
        products = Product.query.order_by(Product.code).all()
        products_list = []
        for p in products:
            current_stock = calculate_product_inventory_at_date(p, date.today())
            products_list.append({
                'id': p.id,
                'name': p.name,
                'stock': current_stock
            })

        # 生成调整单号
        from material_adjustment_service import MaterialAdjustmentService
        adjustment_no = MaterialAdjustmentService.generate_adjustment_no()

        return render_template('material_adjustments/add.html',
                             old_materials=old_materials_list,
                             products=products_list,
                             adjustment_no=adjustment_no,
                             business_date=datetime.now().strftime('%Y-%m-%dT%H:%M'))

    elif request.method == 'POST':
        try:
            # 获取JSON数据
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '未收到有效数据'}), 400

            # 创建货料调整记录，支持datetime-local格式
            business_date_str = data.get('businessDate')
            try:
                if 'T' in business_date_str:
                    # datetime-local格式：2025-07-11T16:30
                    business_date = datetime.strptime(business_date_str, '%Y-%m-%dT%H:%M').date()
                else:
                    # 只有日期格式：2025-07-11
                    business_date = datetime.strptime(business_date_str, '%Y-%m-%d').date()
            except (ValueError, TypeError) as e:
                app.logger.error(f'新增货料调整时解析业务时间出错: {str(e)}, 原始值: {business_date_str}')
                return jsonify({'success': False, 'message': f'业务时间格式错误: {business_date_str}'}), 400

            adjustment = MaterialAdjustment(
                adjustment_no=data.get('adjustmentNo'),
                business_date=business_date,
                adjustment_type=data.get('adjustmentType'),
                notes=data.get('notes', ''),
                created_by=current_user.id
            )

            db.session.add(adjustment)
            db.session.flush()  # 获取ID

            # 创建调整项目
            items_data = data.get('items', [])
            for item_data in items_data:
                # 兼容新旧字段名
                target_type = item_data.get('targetType') or item_data.get('itemType')
                target_name = item_data.get('targetName') or item_data.get('itemName')
                weight_change = float(item_data.get('weightChange', 0))
                is_conversion = item_data.get('isConversion', False)
                conversion_target = item_data.get('conversionTarget', '')
                notes = item_data.get('notes', '')

                # 使用新字段名
                item = MaterialAdjustmentItem(
                    adjustment_id=adjustment.id,
                    target_type=target_type,
                    target_name=target_name,
                    weight_change=weight_change,
                    is_conversion=is_conversion,
                    conversion_target=conversion_target,
                    notes=notes
                )

                db.session.add(item)

            # 在提交前应用货料调整对库存的影响
            try:
                from material_adjustment_service import MaterialAdjustmentService
                MaterialAdjustmentService.apply_adjustment_effects(adjustment.id, is_update=False)
                app.logger.info(f"货料调整 {adjustment.id} 的库存影响处理完成")

                # 统一提交所有更改
                db.session.commit()

                return jsonify({'success': True, 'message': '货料调整记录创建成功', 'id': adjustment.id})
            except Exception as e:
                app.logger.error(f"处理货料调整 {adjustment.id} 的库存影响时出错: {str(e)}")
                db.session.rollback()
                return jsonify({'success': False, 'message': f'应用库存影响失败: {str(e)}'}), 500

        except Exception as e:
            db.session.rollback()
            app.logger.error(f'保存货料调整记录时发生错误: {str(e)}', exc_info=True)
            return jsonify({'success': False, 'message': f'保存货料调整记录时发生错误: {str(e)}'}), 500

@app.route('/material_adjustments/<int:adjustment_id>/view')
@login_required
def material_adjustments_view(adjustment_id):
    """查看货料调整"""
    adjustment = MaterialAdjustment.query.get_or_404(adjustment_id)

    # 获取调整项目
    adjustment_items = MaterialAdjustmentItem.query.filter_by(adjustment_id=adjustment_id).all()

    # 获取历史库存数据（调整前的库存）
    historical_products = []
    historical_old_materials = []

    # 获取产品列表，按照产品信息表的顺序（按编号排序）
    products = Product.query.order_by(Product.code).all()
    for p in products:
        # 使用正向计算获取当前库存
        from routes.report_routes import calculate_product_inventory_at_date
        from datetime import date
        current_stock = calculate_product_inventory_at_date(p, date.today())

        # 计算历史库存（当前库存减去调整变化）
        adjustment_change = 0
        for item in adjustment_items:
            if (item.target_type == 'product' or item.target_type == 'new_product') and item.target_name == p.name:
                adjustment_change += item.weight_change or 0

        historical_stock = current_stock - adjustment_change
        historical_products.append({
            'id': p.id,
            'name': p.name,
            'code': p.code,
            'stock': current_stock,  # 当前库存（正向计算）
            'historical_stock': historical_stock  # 调整前库存
        })

    # 获取旧料列表，按照旧料信息表的顺序（按编号排序）
    old_materials = OldMaterial.query.order_by(OldMaterial.code).all()
    from routes.report_routes import calculate_old_material_inventory_at_date
    from datetime import date

    for old_material in old_materials:
        # 使用正向计算的当前库存
        current_stock = calculate_old_material_inventory_at_date(old_material, date.today())

        # 计算历史库存（当前库存减去调整变化）
        adjustment_change = 0
        for item in adjustment_items:
            if item.target_type == 'old_material' and item.target_name == old_material.name:
                adjustment_change += item.weight_change or 0

        historical_stock = current_stock - adjustment_change
        historical_old_materials.append({
            'id': old_material.id,
            'name': old_material.name,
            'code': old_material.code,
            'department_stock': current_stock,  # 当前库存（正向计算）
            'historical_stock': historical_stock  # 调整前库存
        })

    # 将调整项目转换为可序列化的字典格式
    adjustment_items_data = []
    for item in adjustment_items:
        adjustment_items_data.append({
            'id': item.id,
            'target_type': item.target_type,
            'target_name': item.target_name,
            'weight_change': float(item.weight_change) if item.weight_change else 0,
            'is_conversion': getattr(item, 'is_conversion', False),
            'conversion_target': getattr(item, 'conversion_target', ''),
            'before_adjustment_stock': item.before_adjustment_stock,
            'after_adjustment_stock': item.after_adjustment_stock,
            'notes': item.notes or ''
        })

    app.logger.info(f"查看货料调整 {adjustment_id}，传递给模板的数据: adjustment_items={adjustment_items_data}")

    return render_template('material_adjustments/add.html',
                         mode='view',
                         adjustment=adjustment,
                         adjustment_items=adjustment_items_data,
                         products=historical_products,
                         old_materials=historical_old_materials)

@app.route('/material_adjustments/<int:adjustment_id>/edit', methods=['GET', 'POST'])
@login_required
def material_adjustments_edit(adjustment_id):
    """编辑货料调整"""
    adjustment = MaterialAdjustment.query.get_or_404(adjustment_id)

    if request.method == 'POST':
        try:
            # 先确保session是干净的
            db.session.rollback()

            data = request.get_json()
            app.logger.info(f"更新货料调整记录，接收到的数据: {data}")

            # 验证必要字段 - 支持前端的字段名
            adjustment_type = data.get('adjustmentType') or data.get('adjustment_type')
            if not adjustment_type:
                return jsonify({'success': False, 'message': '调整类型不能为空'}), 400

            # 重新获取adjustment对象
            adjustment = MaterialAdjustment.query.get_or_404(adjustment_id)

            # 更新基本信息，支持datetime-local格式
            business_date_str = data.get('businessDate') or data.get('business_date')
            if business_date_str:
                try:
                    if 'T' in business_date_str:
                        # datetime-local格式：2025-07-11T16:30
                        adjustment.business_date = datetime.strptime(business_date_str, '%Y-%m-%dT%H:%M').date()
                    else:
                        # 只有日期格式：2025-07-11
                        adjustment.business_date = datetime.strptime(business_date_str, '%Y-%m-%d').date()
                except (ValueError, TypeError) as e:
                    app.logger.error(f'编辑货料调整时解析业务时间出错: {str(e)}, 原始值: {business_date_str}')
                    return jsonify({'success': False, 'message': f'业务时间格式错误: {business_date_str}'}), 400

            adjustment.adjustment_type = adjustment_type
            adjustment.notes = data.get('notes', '')

            # 保存原有调整项目的库存快照信息
            old_items = MaterialAdjustmentItem.query.filter_by(adjustment_id=adjustment_id).all()
            old_snapshots = {}
            for old_item in old_items:
                key = (old_item.target_type, old_item.target_name)
                old_snapshots[key] = {
                    'before_adjustment_stock': old_item.before_adjustment_stock,
                    'after_adjustment_stock': old_item.after_adjustment_stock
                }

            # 删除原有的调整项目
            MaterialAdjustmentItem.query.filter_by(adjustment_id=adjustment_id).delete()

            # 创建新的调整项目
            items_data = data.get('items', [])
            for item_data in items_data:
                # 兼容新旧字段名
                target_type = item_data.get('targetType') or item_data.get('itemType')
                target_name = item_data.get('targetName') or item_data.get('itemName')

                item = MaterialAdjustmentItem(
                    adjustment_id=adjustment.id,
                    target_type=target_type,
                    target_name=target_name,
                    weight_change=float(item_data.get('weightChange', 0)),
                    is_conversion=item_data.get('isConversion', False),
                    conversion_target=item_data.get('conversionTarget', ''),
                    notes=item_data.get('notes', '')
                )

                # 恢复库存快照信息（如果存在）
                key = (target_type, target_name)
                if key in old_snapshots:
                    item.before_adjustment_stock = old_snapshots[key]['before_adjustment_stock']
                    # after_adjustment_stock 会在应用调整时重新计算

                db.session.add(item)

            # 应用新的库存影响（更新操作，需要回滚重放）
            db.session.flush()  # 确保调整项目已保存
            from material_adjustment_service import MaterialAdjustmentService
            MaterialAdjustmentService.apply_adjustment_effects(adjustment_id, is_update=True)

            db.session.commit()

            return jsonify({'success': True, 'message': '货料调整记录已更新'})

        except Exception as e:
            db.session.rollback()
            app.logger.error(f'更新货料调整记录时发生错误: {str(e)}', exc_info=True)
            return jsonify({'success': False, 'message': f'更新货料调整记录时发生错误: {str(e)}'}), 500

    # GET请求：显示编辑页面
    # 获取调整项目
    adjustment_items = MaterialAdjustmentItem.query.filter_by(
        adjustment_id=adjustment_id
    ).order_by(MaterialAdjustmentItem.id).all()

    # 获取产品和旧料数据，按照信息表的顺序排序
    products = Product.query.order_by(Product.code).all()
    old_materials = OldMaterial.query.order_by(OldMaterial.code).all()

    # 使用保存的库存快照数据
    historical_products = []
    historical_old_materials = []

    # 为每个产品获取历史库存快照
    from routes.report_routes import calculate_product_inventory_at_date
    from datetime import date
    for product in products:
        current_stock = calculate_product_inventory_at_date(product, date.today())
        historical_stock = current_stock

        # 查找这个调整单中对该产品的调整项目，使用保存的快照
        for item in adjustment_items:
            if item.target_type == 'product' and item.target_name == product.name:
                # 使用保存的调整前库存快照
                if item.before_adjustment_stock is not None:
                    historical_stock = item.before_adjustment_stock
                else:
                    # 兼容旧数据：计算调整前的库存
                    # 需要从当前库存中减去这个调整单的所有影响
                    adjustment_total = 0
                    for adj_item in adjustment_items:
                        if adj_item.target_type == 'product' and adj_item.target_name == product.name:
                            adjustment_total += adj_item.weight_change or 0
                    historical_stock = current_stock - adjustment_total
                break  # 找到第一个匹配的项目就退出

        historical_products.append({
            'id': product.id,
            'name': product.name,
            'stock': current_stock,  # 当前库存（正向计算）
            'historical_stock': historical_stock  # 调整前库存
        })

    # 为每个旧料获取历史库存快照
    from routes.report_routes import calculate_old_material_inventory_at_date
    from datetime import date

    for old_material in old_materials:
        # 使用正向计算的当前库存
        current_stock = calculate_old_material_inventory_at_date(old_material, date.today())

        historical_stock = old_material.department_stock or 0
        # 查找这个调整单中对该旧料的调整项目，使用保存的快照
        for item in adjustment_items:
            if item.target_type == 'old_material' and item.target_name == old_material.name:
                # 使用保存的调整前库存快照
                if item.before_adjustment_stock is not None:
                    historical_stock = item.before_adjustment_stock
                else:
                    # 兼容旧数据：减去调整量得到调整前的库存
                    historical_stock = current_stock - item.weight_change

        historical_old_materials.append({
            'id': old_material.id,
            'name': old_material.name,
            'code': old_material.code,
            'department_stock': current_stock,  # 当前库存（正向计算）
            'historical_stock': historical_stock  # 调整前库存
        })

    # 将调整项目转换为可序列化的字典格式
    adjustment_items_data = []
    for item in adjustment_items:
        adjustment_items_data.append({
            'id': item.id,
            'target_type': item.target_type,
            'target_name': item.target_name,
            'weight_change': float(item.weight_change) if item.weight_change else 0,
            'is_conversion': getattr(item, 'is_conversion', False),
            'conversion_target': getattr(item, 'conversion_target', ''),
            'before_adjustment_stock': item.before_adjustment_stock,
            'after_adjustment_stock': item.after_adjustment_stock,
            'notes': item.notes or ''
        })

    app.logger.info(f"编辑货料调整 {adjustment_id}，传递给模板的数据: adjustment_items={adjustment_items_data}")

    return render_template('material_adjustments/add.html',
                         mode='edit',
                         adjustment=adjustment,
                         adjustment_items=adjustment_items_data,
                         products=historical_products,
                         old_materials=historical_old_materials)

@app.route('/material_adjustments/<int:adjustment_id>/delete', methods=['POST'])
@login_required
def material_adjustments_delete(adjustment_id):
    """删除货料调整记录 - 包含完整的库存回滚"""
    try:
        app.logger.info(f"开始删除货料调整记录: {adjustment_id}")

        adjustment = MaterialAdjustment.query.get_or_404(adjustment_id)
        adjustment_no = adjustment.adjustment_no

        # 1. 先回滚库存影响
        from material_adjustment_service import MaterialAdjustmentService
        app.logger.info(f"回滚货料调整 {adjustment_no} 的库存影响")
        MaterialAdjustmentService.rollback_adjustment_effects(adjustment_id)

        # 2. 删除调整项目
        deleted_items = MaterialAdjustmentItem.query.filter_by(adjustment_id=adjustment_id).delete()
        app.logger.info(f"删除了 {deleted_items} 个调整项目")

        # 3. 删除调整记录
        db.session.delete(adjustment)

        # 4. 重新计算该单据之后的所有单据影响
        # 获取该单据之后的所有调整单据
        later_adjustments = MaterialAdjustment.query.filter(
            MaterialAdjustment.created_at > adjustment.created_at
        ).order_by(MaterialAdjustment.created_at.asc()).all()

        if later_adjustments:
            app.logger.info(f"重新计算 {len(later_adjustments)} 个后续调整单据的库存影响")
            # 重新应用后续单据的影响
            for later_adjustment in later_adjustments:
                items = MaterialAdjustmentItem.query.filter_by(adjustment_id=later_adjustment.id).all()
                for item in items:
                    MaterialAdjustmentService._handle_adjustment_with_snapshot(item)

        # 5. 提交所有更改
        db.session.commit()

        app.logger.info(f"货料调整记录 {adjustment_no} 已删除，库存已重新计算")
        flash(f'货料调整单 {adjustment_no} 已删除', 'success')
        return jsonify({'success': True, 'message': f'货料调整单 {adjustment_no} 已删除'})

    except Exception as e:
        db.session.rollback()
        app.logger.error(f'删除货料调整记录时发生错误: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 400

@app.route('/daily_reports')
@login_required
def daily_reports():
    """日报表页面"""
    # 提供当前日期作为默认值
    today = datetime.now().strftime('%Y-%m-%d')
    return render_template('reports/daily.html', today=today)

@app.route('/account_reports', methods=['GET', 'POST'])
@login_required
def account_reports():
    view = request.args.get('view', 'transactions')  # 默认显示交易明细
    
    # 初始化所有可能的变量，避免未定义错误
    transactions = []
    total_income = 0.0
    total_expense = 0.0
    accounts = Account.query.all()
    account_balances = []
    
    # 初始化统计变量避免undefined错误
    total_initial_balance = 0.0
    total_current_balance = 0.0
    total_final_balance = 0.0  # 添加这个变量以避免undefined错误
    
    # 获取时间段类型参数
    period_type = request.args.get('period_type', 'day')  # day, month, year
    month_year = request.args.get('month_year', datetime.now().strftime('%Y-%m'))
    year = request.args.get('year', datetime.now().year)
    
    # 默认使用当天日期
    today = datetime.now().strftime('%Y-%m-%d')
    
    # 优先检查是否有明确的start_date和end_date参数
    explicit_start_date = request.args.get('start_date')
    explicit_end_date = request.args.get('end_date')
    
    # 如果有明确的日期参数，直接使用
    if explicit_start_date and explicit_end_date:
        start_date = explicit_start_date
        end_date = explicit_end_date
    else:
        # 根据时间段类型设置日期范围
        if period_type == 'month':
            # 按月筛选
            try:
                month_date = datetime.strptime(month_year + '-01', '%Y-%m-%d')
                start_date = month_date.strftime('%Y-%m-%d')
                # 获取月份的最后一天
                if month_date.month == 12:
                    next_month = month_date.replace(year=month_date.year + 1, month=1)
                else:
                    next_month = month_date.replace(month=month_date.month + 1)
                end_date = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
            except:
                # 如果解析失败，使用当天（而不是当月第一天）
                start_date = today
                end_date = today
        elif period_type == 'year':
            # 按年筛选
            try:
                year_int = int(year)
                start_date = f'{year_int}-01-01'
                end_date = f'{year_int}-12-31'
            except:
                # 如果解析失败，使用当天（而不是当年第一天）
                start_date = today
                end_date = today
        else:
            # 按日筛选（默认）- 对于余额概览和流水明细都默认使用当天
            start_date = today
            end_date = today
    
    # 如果导出为Excel
    if request.args.get('export') == 'excel':
        return generate_account_report_excel(start_date, end_date)
    
    # 其他过滤条件
    account_id = request.args.get('account_id', '')
    transaction_type = request.args.get('transaction_type', '')
    purpose = request.args.get('purpose', '')
    keyword = request.args.get('keyword', '')
    min_amount = request.args.get('min_amount', '')
    max_amount = request.args.get('max_amount', '')
    
    # 查询构建
    query = db.session.query(
        AccountTransaction,
        Account.name.label('account_name')
    ).join(
        Account, AccountTransaction.account_id == Account.id
    )
    
    # 基本日期过滤
    if start_date:
        query = query.filter(AccountTransaction.transaction_date >= start_date)
    if end_date:
        next_day = (datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
        query = query.filter(AccountTransaction.transaction_date < next_day)
    
    # 其他筛选条件
    if account_id:
        query = query.filter(AccountTransaction.account_id == account_id)
    if transaction_type:
        query = query.filter(AccountTransaction.transaction_type == transaction_type)
    if purpose:
        query = query.filter(AccountTransaction.purpose == purpose)
    if keyword:
        keyword_search = f"%{keyword}%"
        query = query.filter(db.or_(
            AccountTransaction.related_party.like(keyword_search),
            AccountTransaction.related_document.like(keyword_search),
            AccountTransaction.notes.like(keyword_search)
        ))
    if min_amount:
        query = query.filter(AccountTransaction.amount >= float(min_amount))
    if max_amount:
        query = query.filter(AccountTransaction.amount <= float(max_amount))
    
    # 默认排序
    query = query.order_by(AccountTransaction.transaction_date.desc(), AccountTransaction.id.desc())
    
    # 执行查询
    result = query.all()
    
    # 处理结果
    transactions = []
    for trans, account_name in result:
        # 检查是否是销售订单的支付记录，且订单创建时间与支付时间不在同一天
        purpose = trans.purpose
        if (trans.purpose == '销售收款' and trans.order_id and 
            hasattr(trans, 'transaction_date') and trans.transaction_date):
            # 查询关联的订单
            order = Order.query.get(trans.order_id)
            if order and order.created_at:
                # 比较订单创建日期和交易日期
                order_date = order.created_at.date()
                transaction_date = trans.transaction_date
                
                # 如果不是同一天，修改用途为"客户还款"
                if order_date != transaction_date:
                    purpose = '客户还款'
        
        # 获取关联单据信息
        order_id = trans.order_id
        order_no = None
        purchase_id = trans.purchase_id
        purchase_no = None
        supplier_transaction_id = trans.supplier_transaction_id
        supplier_transaction_no = None

        # 查询关联的订单号
        if order_id:
            order = Order.query.get(order_id)
            if order:
                order_no = order.order_no

        # 查询关联的采购单号
        if purchase_id:
            purchase = Purchase.query.get(purchase_id)
            if purchase:
                purchase_no = getattr(purchase, 'purchase_no', None)

        # 查询关联的供应商往来单号
        if supplier_transaction_id:
            supplier_trans = SupplierTransaction.query.get(supplier_transaction_id)
            if supplier_trans:
                supplier_transaction_no = getattr(supplier_trans, 'transaction_no', None)

        transaction_data = {
            'id': trans.id,
            'business_time': trans.created_at,  # 使用created_at作为业务时间
            'account_id': trans.account_id,
            'account_name': account_name,
            'amount': float(trans.amount) if trans.amount else 0,
            'is_income': trans.transaction_type == 'income',
            'purpose': purpose,  # 使用处理后的用途
            'related_party': trans.related_party,
            'related_document': trans.related_document,
            'notes': trans.notes,
            'customer_name': trans.related_party if hasattr(trans, 'related_party') else None,
            'supplier_name': None,
            'order_id': order_id,
            'order_no': order_no,
            'purchase_id': purchase_id,
            'purchase_no': purchase_no,
            'supplier_transaction_id': supplier_transaction_id,
            'supplier_transaction_no': supplier_transaction_no
        }
        transactions.append(transaction_data)
    
    # 计算总收入和总支出
    for t in transactions:
        if t['is_income']:
            total_income += float(t['amount'])
        else:
            total_expense += float(t['amount'])
    
    # 确保结果为浮点数并保留两位小数
    total_income = round(float(total_income), 2)
    total_expense = round(float(total_expense), 2)
    
    # 始终计算余额概览的统计数据，无论view参数如何
    # 使用时间段汇总逻辑（恢复原有显示方式）
    account_balances = calculate_period_account_balances(start_date, end_date, accounts)

    # 累计总初始余额和总期末余额
    for account_data in account_balances:
        total_initial_balance += account_data['initial_balance']
        total_final_balance += account_data['final_balance']

    # 确保数值格式化为浮点数并保留两位小数
    total_initial_balance = round(float(total_initial_balance), 2)
    total_final_balance = round(float(total_final_balance), 2)

    # 重新计算收支汇总：收入为所有账户收入之和，支出为所有账户支出之和
    total_income = sum(account['income'] for account in account_balances)
    total_expense = sum(account['expense'] for account in account_balances)
    total_income = round(float(total_income), 2)
    total_expense = round(float(total_expense), 2)
    
    # 传递所有必要的变量到模板
    return render_template('reports/accounts.html', 
                          transactions=transactions, 
                          accounts=accounts,
                          total_income=total_income,
                          total_expense=total_expense,
                          start_date=start_date,
                          end_date=end_date,
                          account_id=account_id,
                          transaction_type=transaction_type,
                          purpose=purpose,
                          keyword=keyword,
                          min_amount=min_amount,
                          max_amount=max_amount,
                          view=view,
                          period_type=period_type,
                          month_year=month_year,
                          year=year,
                          account_balances=account_balances,
                          total_initial_balance=total_initial_balance,
                          total_final_balance=total_final_balance,
                          now=datetime.now())

@app.route('/customer_reports')
@login_required
def customer_reports():
    """客户往来查询页面"""
    # 获取筛选参数
    start_date = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))
    customer_id = request.args.get('customer_id', type=int)
    order_type = request.args.get('order_type', '')  # wholesale 或 retail
    payment_status = request.args.get('payment_status', '')  # paid 或 unpaid
    keyword = request.args.get('keyword', '')
    min_amount = request.args.get('min_amount', type=float)
    max_amount = request.args.get('max_amount', type=float)
    
    # 构建查询
    query = Order.query
    
    # 过滤未删除的订单
    if hasattr(Order, 'is_deleted'):
        query = query.filter(Order.is_deleted == False)
    
    # 应用日期筛选
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(Order.order_date >= start_date_obj)
        except:
            app.logger.error(f"无效的开始日期: {start_date}")
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
            end_date_obj = datetime.combine(end_date_obj.date(), time.max)  # 设置为当天的最后一秒
            query = query.filter(Order.order_date <= end_date_obj)
        except:
            app.logger.error(f"无效的结束日期: {end_date}")
    
    # 应用客户筛选
    if customer_id:
        query = query.filter(Order.customer_id == customer_id)
    
    # 应用客户类型筛选
    if order_type:
        query = query.join(Customer).filter(Customer.customer_type == order_type)
    
    # 应用付款状态筛选
    if payment_status:
        is_paid = payment_status == 'paid'
        query = query.filter(Order.is_paid == is_paid)
    
    # 应用金额筛选
    if min_amount is not None:
        query = query.filter(Order.total_amount >= min_amount)
    
    if max_amount is not None:
        query = query.filter(Order.total_amount <= max_amount)
    
    # 应用关键词搜索
    if keyword:
        query = query.filter(
            db.or_(
                Order.order_no.like(f'%{keyword}%'),
                Order.notes.like(f'%{keyword}%'),
                Order.customer.has(Customer.name.like(f'%{keyword}%'))
            )
        )
    
    # 按创建时间排序（升序），与开单录入页一致
    query = query.order_by(Order.created_at.asc(), Order.id.asc())
    
    # 获取订单数据
    orders = query.all()
    
    # 计算批发和零售客户的销售统计
    wholesale_stats = {
        'order_count': 0,
        'total_amount': 0,
        'gold_out': 0,
        'gold_recycle': 0,
        'gold_return': 0,
        'avg_order_amount': 0,
        'customer_debt_material': 0,  # 客户欠料
        'customer_debt': 0  # 客户欠款
    }

    retail_stats = {
        'order_count': 0,
        'total_amount': 0,
        'gold_out': 0,
        'gold_recycle': 0,
        'gold_return': 0,
        'avg_order_amount': 0
    }

    # 为了统计，需要遍历所有订单
    for order in orders:
        # 确保订单有关联的客户
        if not order.customer:
            continue

        # 根据客户类型分类统计
        if order.customer.customer_type == 'wholesale':
            wholesale_stats['order_count'] += 1
            wholesale_stats['total_amount'] += order.total_amount or 0
            wholesale_stats['gold_out'] += order.gold_out or 0
            wholesale_stats['gold_recycle'] += order.gold_recycle or 0
            wholesale_stats['gold_return'] += order.gold_return or 0
        else:
            retail_stats['order_count'] += 1
            retail_stats['total_amount'] += order.total_amount or 0
            retail_stats['gold_out'] += order.gold_out or 0
            retail_stats['gold_recycle'] += order.gold_recycle or 0
            retail_stats['gold_return'] += order.gold_return or 0

    # 计算平均客单价
    if wholesale_stats['order_count'] > 0:
        wholesale_stats['avg_order_amount'] = wholesale_stats['total_amount'] / wholesale_stats['order_count']

    if retail_stats['order_count'] > 0:
        retail_stats['avg_order_amount'] = retail_stats['total_amount'] / retail_stats['order_count']

    # 计算批发客户的欠料和欠款统计
    from models import OrderItem, Payment

    # 获取所有批发客户
    wholesale_customers = Customer.query.filter_by(customer_type='wholesale').all()

    for customer in wholesale_customers:
        # 计算客户欠料：订单中结算方式为欠料的旧料足金的净重之和
        customer_orders = Order.query.filter_by(customer_id=customer.id, is_deleted=False).all()
        customer_debt_material = 0
        customer_debt_amount = 0

        for order in customer_orders:
            # 计算欠料：结算方式为欠料的旧料足金净重
            order_items = OrderItem.query.filter_by(order_id=order.id).all()
            for item in order_items:
                if (item.item_type == 'old' and
                    item.material_name == '旧料足金' and
                    item.settlement_type == '欠料'):
                    customer_debt_material += float(item.net_weight or 0)

            # 计算欠款：未支付完成的订单金额（使用total_amount代替net_amount）
            order_debt = float(order.total_amount or 0)
            payments = Payment.query.filter_by(order_id=order.id).all()
            payment_amount = sum(float(p.amount or 0) for p in payments)
            net_debt = order_debt - payment_amount
            if net_debt > 0:
                customer_debt_amount += net_debt

        wholesale_stats['customer_debt_material'] += customer_debt_material
        wholesale_stats['customer_debt'] += customer_debt_amount
    
    # 获取所有客户列表，用于筛选
    customers = Customer.query.order_by(Customer.name).all()
    
    return render_template('reports/customer.html',
                        orders=orders,
                          customers=customers,
                        wholesale_stats=wholesale_stats,
                        retail_stats=retail_stats,
                          start_date=start_date,
                          end_date=end_date,
                        customer_id=customer_id,
                        order_type=order_type,
                        payment_status=payment_status,
                        keyword=keyword,
                        min_amount=min_amount,
                        max_amount=max_amount)

@app.route('/customer_reports/export')
@login_required
def customer_reports_export():
    """导出客户往来报表为Excel"""
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill
    from io import BytesIO
    
    # 获取与customer_reports相同的筛选参数
    start_date = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))
    customer_id = request.args.get('customer_id', type=int)
    order_type = request.args.get('order_type', '')
    payment_status = request.args.get('payment_status', '')
    keyword = request.args.get('keyword', '')
    min_amount = request.args.get('min_amount', type=float)
    max_amount = request.args.get('max_amount', type=float)
    
    # 构建查询（与customer_reports相同的逻辑）
    query = Order.query
    
    # 过滤未删除的订单
    if hasattr(Order, 'is_deleted'):
        query = query.filter(Order.is_deleted == False)
    
    # 应用日期筛选
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(Order.order_date >= start_date_obj)
        except:
            app.logger.error(f"无效的开始日期: {start_date}")
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
            end_date_obj = datetime.combine(end_date_obj.date(), time.max)
            query = query.filter(Order.order_date <= end_date_obj)
        except:
            app.logger.error(f"无效的结束日期: {end_date}")
    
    # 应用客户筛选
    if customer_id:
        query = query.filter(Order.customer_id == customer_id)
    
    # 应用客户类型筛选
    if order_type:
        query = query.join(Customer).filter(Customer.customer_type == order_type)
    
    # 应用付款状态筛选
    if payment_status:
        is_paid = payment_status == 'paid'
        query = query.filter(Order.is_paid == is_paid)
    
    # 应用金额筛选
    if min_amount is not None:
        query = query.filter(Order.total_amount >= min_amount)
    
    if max_amount is not None:
        query = query.filter(Order.total_amount <= max_amount)
    
    # 应用关键词搜索
    if keyword:
        query = query.filter(
            db.or_(
                Order.order_no.like(f'%{keyword}%'),
                Order.notes.like(f'%{keyword}%'),
                Order.customer.has(Customer.name.like(f'%{keyword}%'))
            )
        )
    
    # 按日期排序（降序）
    query = query.order_by(Order.order_date.desc())
    
    # 获取订单数据
    orders = query.all()
    
    # 创建Excel工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "客户往来报表"
    
    # 设置标题样式
    title_font = Font(name='宋体', size=12, bold=True)
    header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    center_align = Alignment(horizontal='center', vertical='center')
    
    # 添加标题行
    ws.append(["客户往来报表"])
    ws.merge_cells('A1:J1')
    ws['A1'].font = Font(name='宋体', size=16, bold=True)
    ws['A1'].alignment = center_align
    
    # 添加筛选信息
    filter_text = f"筛选条件: 日期范围 {start_date} 至 {end_date}"
    if customer_id:
        customer = Customer.query.get(customer_id)
        if customer:
            filter_text += f", 客户: {customer.name}"
    
    if order_type:
        order_type_text = "批发客户" if order_type == "wholesale" else "零售客户"
        filter_text += f", 客户类型: {order_type_text}"
    
    if payment_status:
        payment_status_text = "已付款" if payment_status == "paid" else "未付款"
        filter_text += f", 付款状态: {payment_status_text}"
    
    ws.append([filter_text])
    ws.merge_cells('A2:J2')
    ws['A2'].alignment = Alignment(horizontal='left', vertical='center')
    
    # 添加报表日期
    report_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    ws.append([f"生成时间: {report_date}"])
    ws.merge_cells('A3:J3')
    ws['A3'].alignment = Alignment(horizontal='left', vertical='center')
    
    # 添加空行
    ws.append([])
    
    # 添加表头
    headers = ["日期", "订单编号", "客户名称", "客户类型", "付款状态", "订单金额", "黄金(克)", "旧料(克)", "退货(克)", "备注"]
    ws.append(headers)
    
    # 设置表头样式
    for col_num, cell in enumerate(ws[5], 1):
        cell.font = title_font
        cell.fill = header_fill
        cell.alignment = center_align
        
    # 添加数据行
    for order in orders:
        customer_type = "批发" if order.customer and order.customer.customer_type == "wholesale" else "零售"
        payment_status = "已付款" if order.is_paid else "未付款"
        
        row = [
            order.order_date.strftime('%Y-%m-%d'),
            order.order_no,
            order.customer.name if order.customer else "",
            customer_type,
            payment_status,
            order.total_amount or 0,
            order.gold_out or 0,
            order.gold_recycle or 0,
            order.gold_return or 0,
            order.notes or ""
        ]
        ws.append(row)
    
    # 设置列宽
    column_widths = [12, 15, 20, 10, 10, 12, 12, 12, 12, 30]
    for i, width in enumerate(column_widths, 1):
        col_letter = ws.cell(row=1, column=i).column_letter
        ws.column_dimensions[col_letter].width = width
    
    # 创建内存文件对象
    output = BytesIO()
    wb.save(output)
    output.seek(0)
    
    # 创建响应
    filename = f"客户往来报表_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    return send_file(
        output,
        download_name=filename,
        as_attachment=True,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@app.route('/supplier_reports')
@login_required
def supplier_reports():
    """供应商往来报表页面"""
    # 获取查询参数
    supplier_id = request.args.get('supplier_id', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    view = request.args.get('view', 'details')  # 默认显示明细页

    # 设置默认日期范围为当天
    today = datetime.now().date()
    if not start_date:
        start_date = today.strftime('%Y-%m-%d')
    if not end_date:
        end_date = today.strftime('%Y-%m-%d')

    # 获取所有供应商
    suppliers = Supplier.query.order_by(Supplier.name).all()

    # 获取供应商名称
    supplier_name = None
    if supplier_id:
        supplier = Supplier.query.get(supplier_id)
        if supplier:
            supplier_name = supplier.name

    # 初始化数据
    transactions = []
    summary_data = []
    purchases = []  # 添加采购数据列表

    return render_template('reports/supplier_reports.html',
                         suppliers=suppliers,
                         supplier_id=supplier_id,
                         supplier_name=supplier_name,
                         start_date=start_date,
                         end_date=end_date,
                         view=view,
                         transactions=transactions,
                         summary_data=summary_data,
                         purchases=purchases)

@app.route('/inventory_reports')
@login_required
def inventory_reports():
    """库存查询页面"""
    # 设置默认日期范围为当天
    today = datetime.now().date()
    start_date = today.strftime('%Y-%m-%d')
    end_date = today.strftime('%Y-%m-%d')

    return render_template('reports/inventory_query.html',
                         start_date=start_date,
                         end_date=end_date)


@app.route('/supplier_reports/export', methods=['GET'])
@login_required
def supplier_reports_export():
    """导出供应商往来报表"""
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    supplier_id = request.args.get('supplier_id', '')
    view = request.args.get('view', 'details')
    
    # 生成Excel文件
    output = io.BytesIO()
    workbook = pd.ExcelWriter(output, engine='xlsxwriter')
    
    # 构建数据
    if view == 'details':
        # 往来明细数据
        query = db.session.query(
            SupplierTransaction,
            Supplier.name.label('supplier_name')
        ).join(
            Supplier, SupplierTransaction.supplier_id == Supplier.id
        )
        
        # 添加日期过滤
        if start_date:
            query = query.filter(SupplierTransaction.business_date >= datetime.strptime(start_date, '%Y-%m-%d').date())
        if end_date:
            query = query.filter(SupplierTransaction.business_date <= datetime.strptime(end_date, '%Y-%m-%d').date())
        
        # 添加供应商过滤
        if supplier_id:
            query = query.filter(SupplierTransaction.supplier_id == supplier_id)
        
        # 按日期排序
        query = query.order_by(SupplierTransaction.business_date.desc())
        
        # 执行查询
        transactions = query.all()
        
        # 准备数据
        data = []
        for idx, (transaction, supplier_name) in enumerate(transactions):
            # 获取物料和款项交易
            material_transactions = transaction.material_transactions
            money_transactions = transaction.money_transactions
            
            # 计算各项总和
            total_return_weight = sum(mt.return_weight or 0 for mt in material_transactions)
            
            # 整理物料交易的来源
            return_sources = ', '.join(set(mt.return_source for mt in material_transactions if mt.return_source and mt.return_weight > 0))
            
            # 整理款项交易的来源
            money_sources = ', '.join(set(mt.return_source for mt in money_transactions if mt.return_source and mt.return_amount > 0))
            
            # 计算涉及金额
            total_amount = sum(mt.return_amount or 0 for mt in money_transactions)
            
            # 计算款项用途
            store_amount = sum(mt.store_amount or 0 for mt in money_transactions)
            
            data.append({
                '序号': idx + 1,
                '往来时间': transaction.business_date.strftime('%Y-%m-%d'),
                '供应商总称': supplier_name,
                '往来项目': transaction.transaction_type or '',
                '关联单据': transaction.transaction_no,
                '涉及旧料': transaction.material_type or '',
                '旧料克重': round(total_return_weight, 2),
                '旧料来源': return_sources,
                '涉及金额': round(total_amount, 2),
                '款项来源': money_sources,
                '款项用途': round(store_amount, 2),
                '备注': transaction.notes or ''
            })
        
        # 创建DataFrame并写入Excel
        if data:
            df = pd.DataFrame(data)
            df.to_excel(workbook, sheet_name='供应商往来明细', index=False)
        else:
            # 如果没有数据，创建一个只有表头的DataFrame
            headers = ['序号', '往来时间', '供应商总称', '往来项目', '关联单据', '涉及旧料', 
                      '旧料克重', '旧料来源', '涉及金额', '款项来源', '款项用途', '备注']
            df = pd.DataFrame(columns=headers)
            df.to_excel(workbook, sheet_name='供应商往来明细', index=False)
    else:
        # 款料明细数据
        summary_query = db.session.query(
            Supplier.id,
            Supplier.name,
            Supplier.owed_gold,
            Supplier.deposit_gold,
            Supplier.owed_amount,
            Supplier.deposit_amount
        )
        
        if supplier_id:
            summary_query = summary_query.filter(Supplier.id == supplier_id)
        
        summary_items = summary_query.all()
        
        # 为每个供应商计算期初值
        data = []
        for idx, item in enumerate(summary_items):
            supplier_id = item[0]
            
            # 查询开始日期之前的最后一笔交易，获取期初值
            initial_transaction = None
            if start_date:
                initial_transaction = db.session.query(
                    SupplierTransaction
                ).filter(
                    SupplierTransaction.supplier_id == supplier_id,
                    SupplierTransaction.business_date < datetime.strptime(start_date, '%Y-%m-%d').date()
                ).order_by(
                    SupplierTransaction.business_date.desc()
                ).first()
            
            # 设置期初值
            initial_owed_gold = 0
            initial_deposit_gold = 0
            initial_owed_amount = 0
            initial_deposit_amount = 0
            
            if initial_transaction:
                initial_owed_gold = initial_transaction.previous_owed_gold
                initial_deposit_gold = initial_transaction.previous_deposit_gold
                initial_owed_amount = initial_transaction.previous_owed_amount
                initial_deposit_amount = initial_transaction.previous_deposit_amount
            
            data.append({
                '序号': idx + 1,
                '供应商名称': item[1],
                '旧料足金-欠料': round(item[2], 2),
                '旧料足金-存料': round(item[3], 2),
                '旧料18K金-欠料': 0.00,
                '旧料18K金-存料': 0.00,
                '旧料22K金-欠料': 0.00,
                '旧料22K金-存料': 0.00,
                '旧料铂金-欠料': 0.00,
                '旧料铂金-存料': 0.00,
                '旧料足银-欠料': 0.00,
                '旧料足银-存料': 0.00,
                '欠款': round(item[4], 2),
                '存款': round(item[5], 2)
            })
        
        # 创建DataFrame并写入Excel
        if data:
            df = pd.DataFrame(data)
            df.to_excel(workbook, sheet_name='供应商款料明细', index=False)
        else:
            # 如果没有数据，创建一个只有表头的DataFrame
            headers = ['序号', '供应商名称', '旧料足金-欠料', '旧料足金-存料', '旧料18K金-欠料', '旧料18K金-存料',
                     '旧料22K金-欠料', '旧料22K金-存料', '旧料铂金-欠料', '旧料铂金-存料', 
                     '旧料足银-欠料', '旧料足银-存料', '欠款', '存款']
            df = pd.DataFrame(columns=headers)
            df.to_excel(workbook, sheet_name='供应商款料明细', index=False)
    
    # 保存Excel文件
    workbook.close()
    output.seek(0)
    
    # 设置文件名
    if view == 'details':
        filename = '供应商往来明细报表.xlsx'
    else:
        filename = '供应商款料明细报表.xlsx'
    
    # 返回Excel文件
    return send_file(
        output,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )










@app.route('/customers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def customer_edit(id):
    """编辑客户信息"""
    customer = Customer.query.get_or_404(id)
    form = CustomerForm(obj=customer)
    
    if request.method == 'POST':
        try:
            # 只允许修改联系电话和地址，其他字段保持不变
            # 客户名称、客户类型、财务相关字段（欠料克重、存料克重、欠款金额、存款金额、添加日期）不允许修改
            customer.phone = request.form.get('phone', '')
            customer.address = request.form.get('address', '')

            # 注意：不修改以下字段
            # - customer.name (客户名称)
            # - customer.customer_type (客户类型)
            # - customer.owed_gold (欠料克重)
            # - customer.deposit_gold (存料克重)
            # - customer.balance (欠款金额)
            # - customer.deposit_amount (存款金额)
            # - customer.created_at (添加日期)

            db.session.commit()
            flash('客户信息更新成功！', 'success')
            return redirect(url_for('customers'))
        except Exception as e:
            db.session.rollback()
            flash(f'更新客户信息时出错: {str(e)}', 'danger')
            return redirect(url_for('customers'))
    
    return render_template('customers/edit.html', customer=customer, form=form)

@app.route('/orders/<int:id>/delete', methods=['POST'])
@login_required
def order_delete(id):
    """删除订单"""
    try:
        order = Order.query.get_or_404(id)
        
        # 记录删除前的信息，用于日志
        app.logger.info(f"开始删除订单 {order.order_no}, ID={order.id}")
        
        # 记录所有支付方式和账户变动，用于日志和调试
        payments = Payment.query.filter_by(order_id=order.id).all()
        if payments:
            app.logger.info(f"订单 {order.order_no} 的支付记录:")
            for payment in payments:
                account = Account.query.get(payment.account_id) if payment.account_id else None
                account_name = account.name if account else "未知账户"
                account_balance = account.balance if account else 0
                app.logger.info(f"支付ID={payment.id}, 账户={account_name}, 金额={payment.amount}, 当前账户余额={account_balance}")
        
        # 注意：客户的欠料和存料信息现在通过计算属性自动从客户款料明细中计算
        # 不需要手动修改，删除订单时会通过process_inventory_and_payment函数处理相关的客户款料明细变化
        if order.customer_id and not order.is_deleted:
            customer = Customer.query.get(order.customer_id)
            if customer and customer.customer_type == 'wholesale':
                # 记录删除前的客户存欠料信息（仅用于日志）
                old_owed_gold = customer.owed_gold
                old_deposit_gold = customer.deposit_gold
                app.logger.info(f"删除前客户存欠料: 客户={customer.name}, 欠料={old_owed_gold}, 存料={old_deposit_gold}")

        
        # 记录订单金额信息
        app.logger.info(f"订单 {order.order_no} 金额信息: 总金额={order.total_amount}")
        
        # 回滚库存变更和支付记录
        # process_inventory_and_payment函数会处理产品库存、旧料库存、客户欠款和账户余额的回滚
        if not process_inventory_and_payment(order, 'delete'):
            raise Exception("回滚库存和支付记录失败")
        
        # 再次记录账户余额，验证回滚是否成功
        for payment in payments:
            if payment.account_id:
                account = Account.query.get(payment.account_id)
                if account:
                    app.logger.info(f"回滚后账户 {account.name} 的余额: {account.balance}")
        
        # 执行软删除
        order.is_deleted = True
        order.deleted_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        db.session.commit()
        
        app.logger.info(f"订单 {order.order_no} 删除成功")
        flash('订单已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除订单失败: {str(e)}")
        flash(f'删除订单失败: {str(e)}', 'danger')

    # 保持当前的日期筛选参数
    # 从referer中提取参数，或者从session中获取
    referer = request.headers.get('Referer', '')
    if '?' in referer:
        query_string = referer.split('?')[1]
        redirect_url = url_for('orders') + '?' + query_string
    else:
        redirect_url = url_for('orders')

    return redirect(redirect_url)

@app.route('/suppliers/add', methods=['GET', 'POST'])
@login_required
def supplier_add():
    """添加供应商"""
    if request.method == 'POST':
        try:
            # 生成供应商编号 - 改进版本，确保唯一性
            def generate_unique_supplier_code():
                # 查找所有以UP开头的供应商编号
                existing_codes = db.session.query(Supplier.code).filter(
                    Supplier.code.like('UP%')
                ).all()

                if not existing_codes:
                    return 'UP00001'

                # 提取所有数字部分
                numbers = []
                for (code,) in existing_codes:
                    if code and len(code) >= 3:
                        try:
                            num = int(code[2:])
                            numbers.append(num)
                        except ValueError:
                            continue

                if not numbers:
                    return 'UP00001'

                # 找到最大数字并加1
                max_num = max(numbers)
                new_code = f'UP{max_num + 1:05d}'

                # 确保生成的编号不存在（双重检查）
                while Supplier.query.filter_by(code=new_code).first():
                    max_num += 1
                    new_code = f'UP{max_num + 1:05d}'

                return new_code

            new_code = generate_unique_supplier_code()

            # 检查供应商名称是否重复
            supplier_name = request.form['name']
            if Supplier.query.filter_by(name=supplier_name).first():
                flash('供应商名称已存在，请使用其他名称', 'danger')
                return redirect(url_for('supplier_add'))

            # 创建新供应商
            supplier = Supplier(
                code=new_code,
                name=supplier_name,
                supplier_type=request.form.get('supplier_type', 'product'),
                contact_person=request.form.get('contact_person', ''),
                phone=request.form.get('phone', ''),
                address=request.form.get('address', '')
            )
            
            # 更新拼音字段
            try:
                pinyin_list = lazy_pinyin(supplier.name)
                supplier.pinyin = ''.join(pinyin_list)
                pinyin_initials = lazy_pinyin(supplier.name, style=Style.FIRST_LETTER)
                supplier.pinyin_initials = ''.join(pinyin_initials)
            except Exception as e:
                print(f"更新供应商拼音字段时出错: {e}")
            
            db.session.add(supplier)
            db.session.flush()  # 获取supplier.id

            # 创建供应商旧料余额记录
            from models import SupplierMaterialBalance, OldMaterial
            all_materials = OldMaterial.query.filter_by(is_active=True).all()

            for material in all_materials:
                if material.name == '旧料足金':
                    # 旧料足金使用Supplier表中的字段值
                    owed_weight = supplier.owed_gold
                    stored_weight = supplier.deposit_gold
                else:
                    # 其他旧料从表单获取
                    owed_key = f'material_{material.name}_owed'
                    stored_key = f'material_{material.name}_stored'
                    owed_weight = float(request.form.get(owed_key, 0))
                    stored_weight = float(request.form.get(stored_key, 0))

                # 为所有旧料类型创建记录，即使值为0（用于供应商款料明细显示）
                balance = SupplierMaterialBalance(
                    supplier_id=supplier.id,
                    material_name=material.name,
                    owed_weight=owed_weight,
                    stored_weight=stored_weight
                )
                db.session.add(balance)

            db.session.commit()
            flash('供应商添加成功！', 'success')
            return redirect(url_for('suppliers'))
        except Exception as e:
            db.session.rollback()
            flash(f'添加供应商时出错: {str(e)}', 'danger')
            return redirect(url_for('suppliers'))
    
    return render_template('suppliers/add.html')


# 添加采购单页面
@app.route('/purchases/add', methods=['GET', 'POST'])
@login_required
def purchases_add():
    """采购单添加页面"""
    form = PurchaseForm()
    
    if request.method == 'GET':
        # 获取供应商列表 - 将Supplier对象转换为字典列表，避免JSON序列化错误
        suppliers_query = Supplier.query.order_by(Supplier.name).all()
        suppliers = []
        for s in suppliers_query:
            suppliers.append({
                'id': s.id,
                'name': s.name,
                'code': s.code,
                'phone': s.phone,
                'supplier_type': s.supplier_type,
                'owed_gold': s.owed_gold,
                'deposit_gold': s.deposit_gold,
                'owed_amount': s.owed_amount,
                'deposit_amount': s.deposit_amount,
                'pinyin': getattr(s, 'pinyin', ''),
                'pinyin_initials': getattr(s, 'pinyin_initials', '')
            })
        
        # 获取产品列表 - 将Product对象转换为字典列表
        from routes.report_routes import calculate_product_inventory_at_date
        from datetime import date
        products_query = Product.query.order_by(Product.code).all()
        products = []
        for p in products_query:
            current_stock = calculate_product_inventory_at_date(p, date.today())
            products.append({
                'id': p.id,
                'code': p.code,
                'name': p.name,
                'category': p.category if hasattr(p, 'category') else '',
                'price': float(p.price) if hasattr(p, 'price') and p.price is not None else 0,
                'stock': current_stock
            })
        
        # 生成采购单号
        current_date = datetime.now().strftime('%y%m%d')
        # 获取当日最后一个采购单号
        last_purchase = db.session.query(Purchase).filter(
            Purchase.purchase_no.like(f'CG{current_date}%')
        ).order_by(Purchase.purchase_no.desc()).first()
        
        if last_purchase:
            # 提取序号并加1
            try:
                last_no = int(last_purchase.purchase_no[-4:])
                new_no = last_no + 1
                purchase_no = f'CG{current_date}{new_no:04d}'
            except ValueError:
                purchase_no = f'CG{current_date}0001'
        else:
            purchase_no = f'CG{current_date}0001'
        
        # 渲染表单
        return render_template('purchases/add.html', 
                              form=form, 
                              suppliers=suppliers, 
                              products=products,
                              purchase_no=purchase_no,
                              business_date=datetime.now().strftime('%Y-%m-%d'))
    
    elif request.method == 'POST':
        try:
            # 从JSON请求获取数据
            if request.is_json:
                data = request.json
            else:
                # 获取表单数据
                data = request.form
            
            # 获取业务时间并验证
            business_time_str = data.get('businessTime')
            if not business_time_str:
                return jsonify({'success': False, 'message': '业务时间不能为空'}), 400

            # 创建采购单
            purchase = Purchase()
            purchase.purchase_no = data.get('purchaseNumber')
            
            # 安全地解析业务时间，支持datetime-local格式
            try:
                if 'T' in business_time_str:
                    # datetime-local格式：2025-07-11T16:30
                    purchase.business_time = datetime.strptime(business_time_str, '%Y-%m-%dT%H:%M')
                else:
                    # 只有日期格式：2025-07-11
                    purchase.business_time = datetime.strptime(business_time_str, '%Y-%m-%d')
            except (ValueError, TypeError) as e:
                app.logger.error(f'解析业务时间出错: {str(e)}, 原始值: {business_time_str}')
                return jsonify({'success': False, 'message': f'业务时间格式错误: {business_time_str}'}), 400
                
            purchase.notes = data.get('notes', '')
            purchase.created_by = current_user.id
            purchase.created_at = datetime.now()
            purchase.is_deleted = False
            
            # 初始化采购单合计
            purchase.total_amount = 0
            purchase.total_final_amount = 0
            
            db.session.add(purchase)
            db.session.flush()  # 获取ID
            
            # 处理采购单项
            items_data = data.get('items', []) if request.is_json else []
            
            # 如果不是JSON请求，尝试从表单获取
            if not items_data and data.get('items_json'):
                try:
                    items_data = json.loads(data.get('items_json'))
                except Exception as e:
                    app.logger.error(f'解析items_json失败: {str(e)}')
                    return jsonify({'success': False, 'message': '采购单项目数据格式错误'}), 400
            
            if not items_data:
                return jsonify({'success': False, 'message': '请至少添加一个有效的产品项'}), 400
            
            for idx, item_data in enumerate(items_data):
                item = PurchaseItem()
                item.purchase_id = purchase.id
                item.supplier_id = item_data.get('supplierId')
                item.product_id = item_data.get('productId', None)
                
                # 从产品对象获取产品名称和类型
                if item.product_id:
                    product = Product.query.get(item.product_id)
                    if product:
                        item.product_name = product.name
                        item.product_type = product.category
                    else:
                        item.product_name = item_data.get('product_name', '')
                        item.product_type = item_data.get('product_type', '')
                else:
                    item.product_name = item_data.get('product_name', '')
                    item.product_type = item_data.get('product_type', '')
                
                item.weight = item_data.get('weight', 0)
                item.labor_fee = item_data.get('laborFee', 0)
                item.gold_price = item_data.get('gold_price', 0)
                item.settlement_method = item_data.get('settlementMethod', '')
                item.final_amount = item_data.get('finalAmount', 0)  # 使用final_amount字段
                item.notes = item_data.get('notes', '')
                item.is_deleted = False
                
                # 累加到采购单合计
                purchase.total_amount += float(item.labor_fee or 0)
                purchase.total_weight += float(item.weight or 0)
                purchase.total_final_amount += float(item.final_amount or 0)  # 累加结价金额
                
                db.session.add(item)
            
            db.session.commit()

            # 应用采购单据对供应商信息的影响
            try:
                from purchase_service import PurchaseService
                PurchaseService.apply_purchase_effects(purchase.id)
                db.session.commit()
                app.logger.info(f"采购单 {purchase.id} 的供应商数据联动处理完成")
            except Exception as e:
                app.logger.error(f"处理采购单 {purchase.id} 的供应商数据联动时出错: {str(e)}")

            return jsonify({'success': True, 'message': '采购单创建成功', 'id': purchase.id})
        except Exception as e:
            db.session.rollback()
            app.logger.error(f'保存采购单时发生错误: {str(e)}', exc_info=True)
            return jsonify({'success': False, 'message': f'保存采购单时发生错误: {str(e)}'}), 500
        
        return redirect(url_for('purchases'))

@app.route('/purchases/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def purchases_edit(id):
    """采购单编辑页面"""
    # 获取采购单
    purchase = Purchase.query.get_or_404(id)
    
    if request.method == 'GET':
        # 获取供应商列表（直接使用对象，保持与模板一致）
        suppliers = Supplier.query.order_by(Supplier.name).all()
        
        # 获取产品列表（直接使用对象，保持与模板一致）
        products = Product.query.order_by(Product.code).all()
        
        # 获取采购项目（未删除的）
        purchase_items = PurchaseItem.query.filter_by(
            purchase_id=id, 
            is_deleted=False
        ).all()
        
        # 计算当前金价
        gold_price = 680  # 这里应该从配置或其他地方获取实时金价
        
        return render_template('purchases/add.html',
                              edit_mode=True,
                              purchase=purchase,
                              suppliers=suppliers, 
                              products=products,
                              gold_price=gold_price,
                              business_date=purchase.business_time.strftime('%Y-%m-%d') if purchase.business_time else datetime.now().strftime('%Y-%m-%d'))
    
    elif request.method == 'POST':
        try:
            # 获取JSON数据
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '未收到有效数据'}), 400
            
            app.logger.info(f'收到编辑采购单请求，ID: {id}, 数据: {json.dumps(data, ensure_ascii=False)}')
            
            # 更新采购单基本信息
            business_time_str = data['businessTime']
            try:
                if 'T' in business_time_str:
                    # datetime-local格式：2025-07-11T16:30
                    purchase.business_time = datetime.strptime(business_time_str, '%Y-%m-%dT%H:%M')
                else:
                    # 只有日期格式：2025-07-11
                    purchase.business_time = datetime.strptime(business_time_str, '%Y-%m-%d')
            except (ValueError, TypeError) as e:
                app.logger.error(f'编辑采购单时解析业务时间出错: {str(e)}, 原始值: {business_time_str}')
                return jsonify({'success': False, 'message': f'业务时间格式错误: {business_time_str}'}), 400

            purchase.notes = data.get('notes', '')
            
            # 重置总计
            purchase.total_amount = 0
            purchase.total_weight = 0
            purchase.total_final_amount = 0  # 重置结价金额总计
            
            # 先回滚当前有效项目的影响
            from purchase_service import PurchaseService
            if not PurchaseService.rollback_purchase_effects(purchase.id, only_active=True):
                raise Exception("回滚采购单据的供应商数据联动失败")

            # 然后标记现有采购项为删除状态
            existing_items = PurchaseItem.query.filter_by(purchase_id=id, is_deleted=False).all()
            for item in existing_items:
                item.is_deleted = True
                item.deleted_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 添加新的采购项
            items_data = data.get('items', [])
            if not items_data:
                return jsonify({'success': False, 'message': '请至少添加一个有效的产品项'}), 400
            
            for idx, item_data in enumerate(items_data):
                item = PurchaseItem()
                item.purchase_id = purchase.id
                item.supplier_id = item_data.get('supplierId')
                item.product_id = item_data.get('productId', None)
                
                # 从产品对象获取产品名称和类型
                if item.product_id:
                    product = Product.query.get(item.product_id)
                    if product:
                        item.product_name = product.name
                        item.product_type = product.category
                    else:
                        item.product_name = item_data.get('product_name', '')
                        item.product_type = item_data.get('product_type', '')
                else:
                    item.product_name = item_data.get('product_name', '')
                    item.product_type = item_data.get('product_type', '')
                
                item.weight = item_data.get('weight', 0)
                item.labor_fee = item_data.get('laborFee', 0)
                item.gold_price = item_data.get('gold_price', 0)
                item.settlement_method = item_data.get('settlementMethod', '')
                item.final_amount = item_data.get('finalAmount', 0)  # 使用final_amount字段
                item.notes = item_data.get('notes', '')
                item.is_deleted = False
                
                # 累加到采购单合计
                purchase.total_amount += float(item.labor_fee or 0)
                purchase.total_weight += float(item.weight or 0)
                purchase.total_final_amount += float(item.final_amount or 0)  # 累加结价金额
                
                db.session.add(item)
            
            # 刷新数据库会话，确保采购项目已保存
            db.session.flush()

            # 重新应用当前有效项目的影响
            if not PurchaseService.apply_purchase_effects(purchase.id):
                raise Exception("应用采购单据的供应商数据联动失败")

            # 统一提交所有更改
            db.session.commit()

            app.logger.info(f'采购单 {purchase.purchase_no} 编辑成功，供应商数据联动更新完成')
            return jsonify({'success': True, 'message': '采购单更新成功', 'id': purchase.id})
            
        except Exception as e:
            db.session.rollback()
            app.logger.error(f'编辑采购单时发生错误: {str(e)}', exc_info=True)
            return jsonify({'success': False, 'message': f'编辑采购单时发生错误: {str(e)}'}), 500

@app.route('/orders/add', methods=['GET', 'POST'])
@login_required
def order_add():
    if request.method == 'GET':
        # 获取所有客户（余额从客户款料明细计算）
        from routes.report_routes import calculate_customer_balances
        customers = Customer.query.all()
        customers_list = []
        for c in customers:
            balances = calculate_customer_balances(c.id)
            customers_list.append({
                'id': c.id,
                'name': c.name,
                'type': c.customer_type,
                'phone': c.phone,
                'address': c.address,
                'pinyin': c.pinyin,
                'pinyin_initials': c.pinyin_initials,
                'owed_gold': balances.get('owed_gold', 0),
                'deposit_gold': balances.get('deposit_gold', 0),
                'balance': balances.get('owed_amount', 0)
            })
        
        # 获取所有产品（使用正向计算获取库存）
        from routes.report_routes import calculate_product_inventory_at_date
        from datetime import date
        products = Product.query.all()
        products_list = []
        for p in products:
            current_stock = calculate_product_inventory_at_date(p, date.today())
            products_list.append({
                'id': p.id,
                'code': p.code,
                'name': p.name,
                'stock': current_stock,
                'wholesale_labor_fee': p.wholesale_labor_fee,
                'wholesale_premium_fee': p.wholesale_premium_fee,
                'retail_labor_fee': p.retail_labor_fee,
                'retail_premium_fee': p.retail_premium_fee
            })
        
        # 获取所有旧料
        old_materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code.asc()).all()
        old_materials_list = [{
            'id': m.id,
            'name': m.name,
            'code': m.code
        } for m in old_materials]
        
        # 获取所有账户
        accounts = Account.query.filter_by(is_active=True).all()
        accounts_list = [{
            'id': a.id,
            'name': a.name,
            'type': a.account_type
        } for a in accounts]
        
        # 获取当前金价
        gold_price = 680  # 这里应该从配置或其他地方获取实时金价
        
        # 生成无序化订单编号
        try:
            import random
            import string
            import time
            
            # 获取日期部分
            date_part = datetime.now().strftime('%y%m%d')
                
            # 生成随机字母数字字符串（4位）
            random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
            
            # 获取毫秒级时间戳的后4位
            time_suffix = str(int(time.time() * 1000))[-4:]
            
            # 组合订单号：DD + 日期 + 随机字符 + 时间戳后4位
            order_no = f"DD{date_part}{random_chars}{time_suffix}"
            
            # 确保唯一性，如果已存在则重新生成
            while Order.query.filter_by(order_no=order_no).first():
                random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
                time_suffix = str(int(time.time() * 1000))[-4:]
                order_no = f"DD{date_part}{random_chars}{time_suffix}"
        except Exception as e:
            # 如果生成失败，使用备用方案
            app.logger.error(f"订单号生成错误: {str(e)}")
            order_no = f"DD{datetime.now().strftime('%y%m%d%H%M%S')}"
        
        return render_template('orders/add.html', 
                            customers=customers_list, 
                            products=products_list, 
                            old_materials=old_materials_list,
                            accounts=accounts_list,
                            gold_price=gold_price,
                            order_no=order_no)
    else:  # POST方法处理
        try:
            # 解析JSON数据
            data = request.json
            
            # 创建新订单
            order = Order(
                order_no=data.get('orderNo'),
                customer_id=data.get('customerId'),
                notes=data.get('notes'),
                order_date=datetime.strptime(data.get('businessTime'), '%Y-%m-%dT%H:%M') if data.get('businessTime') else datetime.now(),
                is_paid=data.get('isPaid', False),
                is_material_owed=False,  # 默认值
                total_amount=data.get('grandTotal'),
                status='active',
                created_by=current_user.id
            )
            
            # 折扣功能已删除，直接使用总金额
            
            db.session.add(order)
            db.session.flush()  # 获取订单ID
            
            # 添加订单项目
            items_data = data.get('items', [])
            
            # 初始化欠料和存料变更信息
            previous_owed_gold = 0
            current_owed_gold = 0
            current_return_gold = 0
            previous_deposit_gold = 0
            current_deposit_gold = 0
            deduct_deposit_gold = 0
            
            # 处理批发客户的欠料存料
            customer = Customer.query.get(data.get('customerId'))
            if customer and customer.customer_type == 'wholesale':
                # 获取该客户的最后一个订单的总计值作为上期值（排除当前订单）
                last_order = Order.query.filter(
                    Order.customer_id == customer.id,
                    Order.is_deleted == False,
                    Order.id != order.id  # 排除当前订单
                ).order_by(Order.created_at.desc()).first()

                if last_order:
                    previous_owed_gold = round(last_order.total_owed_gold or 0, 2)
                    previous_deposit_gold = round(last_order.total_deposit_gold or 0, 2)
                    print(f"从上一订单 {last_order.order_no} 获取上期值: 欠料={previous_owed_gold}, 存料={previous_deposit_gold}")
                else:
                    # 如果没有历史订单，使用客户的初始余额
                    previous_owed_gold = round(customer.owed_gold or 0, 2)
                    previous_deposit_gold = round(customer.deposit_gold or 0, 2)
                    print(f"客户 {customer.name} 首次订单，使用初始余额: 欠料={previous_owed_gold}, 存料={previous_deposit_gold}")
            
            # 处理订单项目
            order.gold_out = 0
            order.gold_return = 0
            order.gold_recycle = 0
            
            for item_data in items_data:
                item = OrderItem(
                    order_id=order.id,
                    item_type=item_data.get('type'),
                    product_id=item_data.get('product_id'),
                    product_code=item_data.get('productCode', ''),
                    product_name=item_data.get('productName', ''),
                    weight=item_data.get('weight', 0),
                    amount=item_data.get('amount', 0)
                )
                
                if item.item_type == 'new':
                    item.labor_cost = item_data.get('labor', 0)
                    item.premium_cost = item_data.get('premium', 0)
                    item.gold_price = item_data.get('goldPrice', 0)
                    # 只有黄金相关产品才计入gold_out
                    is_gold_product = any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']) if item.product_name else False
                    if is_gold_product:
                        order.gold_out += float(item.weight or 0)
                    # 新货出库不再计入欠料
                elif item.item_type == 'return':
                    item.labor_cost = item_data.get('labor', 0)
                    item.premium_cost = item_data.get('premium', 0)
                    item.gold_price = item_data.get('goldPrice', 0)
                    # 只有黄金相关产品才计入gold_return
                    is_gold_product = any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']) if item.product_name else False
                    if is_gold_product:
                        order.gold_return += float(item.weight or 0)
                    # 退货录入不再计入还料
                elif item.item_type == 'old':
                    item.material_name = item_data.get('materialName', '')
                    item.settlement_type = item_data.get('settlementType', '')
                    item.purity = item_data.get('purity', 0)
                    item.net_weight = item_data.get('netWeight', 0)
                    item.material_price = item_data.get('materialPrice', 0)
                    
                    # 只有旧料足金且结算方式为存料、还料或结料时才计入gold_recycle
                    if item.material_name == '旧料足金' and item.settlement_type in ['store', 'return', 'settle']:
                        order.gold_recycle += float(item.net_weight or 0)
                    
                    # 批发客户才需要计算存料和欠料
                    if customer and customer.customer_type == 'wholesale' and item.material_name == '旧料足金':
                        # 旧料足金根据结算方式处理
                        if item.settlement_type == 'owe':  # 欠料
                            current_owed_gold += float(item.net_weight or 0)
                        elif item.settlement_type == 'return':  # 还料
                            current_return_gold += float(item.net_weight or 0)
                        elif item.settlement_type == 'store':  # 存料
                            current_deposit_gold += float(item.net_weight or 0)
                        elif item.settlement_type == 'deduct':  # 存料抵扣
                            deduct_deposit_gold += float(item.net_weight or 0)
                else:  # other
                    item.other_type = item_data.get('otherType', '')
                    item.quantity = item_data.get('quantity', 0)
                    item.unit_price = item_data.get('unitPrice', 0)
                
                db.session.add(item)
            
            # 计算总的欠料和存料变更
            total_owed_gold = round(previous_owed_gold + current_owed_gold - current_return_gold, 2)
            total_deposit_gold = round(previous_deposit_gold + current_deposit_gold - deduct_deposit_gold, 2)
            
            # 更新订单中的欠料和存料历史数据
            order.previous_owed_gold = previous_owed_gold
            order.current_owed_gold = current_owed_gold
            order.current_return_gold = current_return_gold
            order.total_owed_gold = total_owed_gold
            
            order.previous_deposit_gold = previous_deposit_gold
            order.current_deposit_gold = current_deposit_gold
            order.deduct_deposit_gold = deduct_deposit_gold
            order.total_deposit_gold = total_deposit_gold
            
            # 更新订单的欠料状态
            if current_owed_gold > 0:
                order.is_material_owed = True
                print(f"订单 {order.order_no} 标记为欠料: 本期欠料={current_owed_gold}")
            else:
                order.is_material_owed = False
                print(f"订单 {order.order_no} 标记为不欠料: 本期欠料={current_owed_gold}")
            
            # 批发客户的欠料和存料现在通过计算属性自动获取，无需手动更新
            
            # 添加支付记录
            payments_data = data.get('payments', [])
            total_payment = 0
            for payment_data in payments_data:
                # 处理支付时间
                payment_time = payment_data.get('paymentTime')
                try:
                    payment_date = datetime.strptime(payment_time, '%Y-%m-%dT%H:%M') if payment_time else datetime.now()
                except ValueError:
                    payment_date = datetime.now()
                    
                payment_amount = float(payment_data.get('amount', 0))
                total_payment += payment_amount
                
                payment = Payment(
                    order_id=order.id,
                    account_id=payment_data.get('type'),
                    amount=payment_amount,
                    payment_date=payment_date  # 只使用payment_date字段
                )
                db.session.add(payment)
                print(f"添加支付记录: order_id={payment.order_id}, account_id={payment.account_id}, amount={payment.amount}, payment_date={payment.payment_date}")

                # 注意：账户交易记录将在process_inventory_and_payment函数中统一创建，避免重复
            
            # 更新订单结清状态（使用total_amount代替net_amount）
            if order.total_amount >= 0:
                # 正常情况：应付金额为正，支付金额大于等于应付金额
                order.is_paid = total_payment >= order.total_amount
            else:
                # 特殊情况：应付金额为负值，支付金额应该为0或负数才算结清
                order.is_paid = total_payment <= order.total_amount

            if order.is_paid:
                print(f"订单 {order.order_no} 已标记为已结清: 支付总额={total_payment}, 订单总额={order.total_amount}")
            else:
                print(f"订单 {order.order_no} 标记为未结清: 支付总额={total_payment}, 订单总额={order.total_amount}")
            
            db.session.commit()

            # 处理库存变更
            process_inventory_and_payment(order, 'add')

            # 如果是批发客户且有存欠料变化，执行级联更新后续订单
            if customer and customer.customer_type == 'wholesale' and (current_owed_gold != 0 or current_return_gold != 0 or current_deposit_gold != 0 or deduct_deposit_gold != 0):
                from order_cascade_service import OrderCascadeService
                print(f"开始级联更新订单 {order.id} 后续订单的上期值")
                cascade_success = OrderCascadeService.update_subsequent_orders_previous_values(
                    customer.id, order.id, total_owed_gold, total_deposit_gold
                )
                if not cascade_success:
                    print(f"订单 {order.id} 级联更新失败，但订单本身已保存")

            return jsonify({'success': True, 'orderId': order.id})
        except Exception as e:
            db.session.rollback()
            print(f"订单创建失败: {str(e)}")
            return jsonify({'success': False, 'message': f'订单创建失败: {str(e)}'})

@app.route('/customers/<int:id>/delete', methods=['GET', 'POST'])
@login_required
def customer_delete(id):
    """删除客户"""
    try:
        customer = Customer.query.get_or_404(id)

        # 检查是否有关联的订单
        has_orders = Order.query.filter_by(customer_id=id).first() is not None

        if has_orders:
            flash('该客户已有关联的订单，不能删除', 'danger')
            return redirect(url_for('customers'))

        # 检查是否有关联的其他项目往来记录
        from models import Transaction
        has_transactions = Transaction.query.filter_by(customer_id=id).first() is not None

        if has_transactions:
            flash('该客户已有关联的其他项目往来记录，不能删除', 'danger')
            return redirect(url_for('customers'))

        # 删除相关的CustomerMaterialBalance记录
        from models import CustomerMaterialBalance
        CustomerMaterialBalance.query.filter_by(customer_id=id).delete()

        # 删除客户记录
        db.session.delete(customer)
        db.session.commit()
        flash('客户已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除客户失败: {str(e)}', 'danger')

    return redirect(url_for('customers'))

@app.route('/suppliers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def supplier_edit(id):
    """编辑供应商信息"""
    supplier = Supplier.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            # 只允许修改联系人、联系人电话、地址字段
            # 不允许修改：name, supplier_type, owed_gold, deposit_gold, owed_amount, deposit_amount, created_at
            supplier.contact_person = request.form.get('contact_person', '')
            supplier.phone = request.form.get('phone', '')
            supplier.address = request.form.get('address', '')

            # 注意：以下字段不允许修改，保持原值
            # - supplier.name (供应商名称)
            # - supplier.supplier_type (供应商类别)
            # - supplier.owed_gold (欠料克重)
            # - supplier.deposit_gold (存料克重)
            # - supplier.owed_amount (欠款金额)
            # - supplier.deposit_amount (存款金额)
            # - supplier.created_at (添加日期)

            db.session.commit()
            flash('供应商更新成功！', 'success')
            return redirect(url_for('suppliers'))
        except Exception as e:
            db.session.rollback()
            flash(f'更新供应商时出错: {str(e)}', 'danger')
            return redirect(url_for('supplier_edit', id=id))
    
    return render_template('suppliers/edit.html', supplier=supplier)

@app.route('/suppliers/<int:id>/delete', methods=['GET', 'POST'])
@login_required
def supplier_delete(id):
    """删除供应商"""
    try:
        supplier = Supplier.query.get_or_404(id)
        app.logger.info(f"尝试删除供应商: ID={id}, 名称={supplier.name}")
        
        # 检查是否有关联的采购单项 (而不是采购单本身)
        has_purchase_items = PurchaseItem.query.filter_by(supplier_id=id).first() is not None
        app.logger.info(f"供应商 {supplier.name} 关联采购项检查结果: {has_purchase_items}")
        if has_purchase_items:
            # 可以进一步查询涉及的具体采购单，提供更详细的日志或提示
            linked_purchases = db.session.query(Purchase.purchase_no).join(PurchaseItem).filter(PurchaseItem.supplier_id == id).distinct().limit(5).all()
            linked_purchase_nos = [p[0] for p in linked_purchases]
            app.logger.warning(f"供应商 {supplier.name} 仍有关联的采购项，涉及采购单: {linked_purchase_nos}...")
            flash(f'该供应商已有关联的采购项，不能删除。涉及采购单: {", ".join(linked_purchase_nos)}...', 'danger')
            return redirect(url_for('suppliers'))

        # 检查是否有关联的供应商往来记录
        transactions = SupplierTransaction.query.filter_by(supplier_id=id).all()
        has_transactions = len(transactions) > 0
        app.logger.info(f"供应商 {supplier.name} 关联供应商往来记录数量: {len(transactions)}")
        if has_transactions:
            for t in transactions:
                app.logger.info(f"关联往来记录: ID={t.id}, 编号={t.transaction_no if hasattr(t, 'transaction_no') else '无编号'}")
        
        # 特别检查信达祥供应商
        if supplier.name == '信达祥':
            app.logger.warning(f"检测到信达祥供应商删除，重点检查关联项目：采购单={has_purchase_items}，往来记录={has_transactions}")
            # 查找关联的软删除采购项
            soft_deleted_purchase_items = PurchaseItem.query.join(Purchase).filter(
                PurchaseItem.supplier_id == id,
                Purchase.is_deleted == True
            ).all()
            app.logger.warning(f"信达祥关联的软删除采购项数量: {len(soft_deleted_purchase_items)}")
            # 列出相关的采购单
            soft_deleted_purchase_ids = set([item.purchase_id for item in soft_deleted_purchase_items])
            soft_deleted_purchases = Purchase.query.filter(Purchase.id.in_(soft_deleted_purchase_ids)).all()
            for p in soft_deleted_purchases:
                app.logger.warning(f"软删除采购单: ID={p.id}, 采购单号={p.purchase_no}")
        
        if has_purchase_items or has_transactions:
            flash('该供应商已有关联的采购单或往来记录，不能删除', 'danger')
            return redirect(url_for('suppliers'))

        # 删除关联的供应商旧料余额记录
        SupplierMaterialBalance.query.filter_by(supplier_id=id).delete()
        app.logger.info(f"已删除供应商 {supplier.name} 的旧料余额记录")

        # 删除供应商
        db.session.delete(supplier)
        db.session.commit()
        flash('供应商已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除供应商失败: {str(e)}", exc_info=True)
        flash(f'删除供应商失败: {str(e)}', 'danger')
    
    return redirect(url_for('suppliers'))

@app.route('/orders/<int:id>/print')
@login_required
def order_print(id):
    """订单打印页面"""
    try:
        order = Order.query.get_or_404(id)
        
        # 获取订单项并按类型分类
        order_items = OrderItem.query.filter_by(order_id=id).all()
        new_items = [item for item in order_items if item.item_type == 'new']
        return_items = [item for item in order_items if item.item_type == 'return']
        old_items = [item for item in order_items if item.item_type == 'old']
        other_items = [item for item in order_items if item.item_type == 'other']
        
        # 获取客户信息
        customer = Customer.query.get(order.customer_id) if order.customer_id else None
        
        # 获取支付信息
        payments = Payment.query.filter_by(order_id=id).all()
        payments_list = []
        for payment in payments:
            payments_list.append({
                'id': payment.id,
                'type': payment.account_id,  # 保持type字段为account_id以与前端匹配
                'amount': float(payment.amount) if payment.amount else 0,
                'created_at': payment.created_at.strftime('%Y-%m-%d %H:%M') if payment.created_at else None,
                'payment_time': payment.payment_date.strftime('%Y-%m-%d %H:%M') if hasattr(payment, 'payment_date') and payment.payment_date else None
            })
        
        # 打印支付信息，用于调试
        print(f"订单ID {order.id} 的支付信息: {payments_list}")
        
        # 按金属类型分组的统计信息
        metal_stats = {
            '黄金': {'total': 0, 'products': {}},
            '铂金': {'total': 0, 'products': {}},
            '白银': {'total': 0, 'products': {}},
            '玉石': {'total': 0, 'products': {}},
            '钻石': {'total': 0, 'products': {}},
            '18K金': {'total': 0, 'products': {}},
            '22K金': {'total': 0, 'products': {}}
        }
        
        # 计算各类金属总重量和产品统计
        for item in new_items:
            if item.product and item.product.metal_type:
                metal_type = item.product.metal_type
                if metal_type in metal_stats:
                    product_name = item.product.name if item.product else item.product_name
                    metal_stats[metal_type]['total'] += float(item.weight or 0)
                    
                    if product_name not in metal_stats[metal_type]['products']:
                        metal_stats[metal_type]['products'][product_name] = {'count': 0, 'weight': 0}
                    
                    metal_stats[metal_type]['products'][product_name]['count'] += 1
                    metal_stats[metal_type]['products'][product_name]['weight'] += float(item.weight or 0)
        
        # 准备结算数据
        summary = {
            'total_items': len(order_items),
            'total_amount': sum(float(item.amount or 0) for item in order_items),
            'total_weight': sum(float(item.weight or 0) for item in order_items if item.item_type in ['new', 'return', 'old']),
        }
        
        # 计算欠料/存料 - 统计订单项中的欠料和存料数据
        previous_owed_gold = 0
        current_owed_gold = 0
        current_return_gold = 0
        total_owed_gold = 0
        
        previous_deposit_gold = 0
        current_deposit_gold = 0
        deduct_deposit_gold = 0
        total_deposit_gold = 0
        
        # 若是批发客户，提取订单中的欠料/存料数据
        if customer and customer.customer_type == 'wholesale':
            # 获取欠料和存料数据
            previous_owed_gold = float(order.previous_owed_gold or 0)
            current_owed_gold = float(order.current_owed_gold or 0)
            current_return_gold = float(order.current_return_gold or 0)
            total_owed_gold = float(order.total_owed_gold or 0)
            
            previous_deposit_gold = float(order.previous_deposit_gold or 0)
            current_deposit_gold = float(order.current_deposit_gold or 0)
            deduct_deposit_gold = float(order.deduct_deposit_gold or 0)
            total_deposit_gold = float(order.total_deposit_gold or 0)
        
        # 将计算后的值保存回订单对象，确保模板能够正确显示
        order.previous_owed_gold = previous_owed_gold
        order.current_owed_gold = current_owed_gold
        order.current_return_gold = current_return_gold
        order.total_owed_gold = total_owed_gold
        
        order.previous_deposit_gold = previous_deposit_gold
        order.current_deposit_gold = current_deposit_gold
        order.deduct_deposit_gold = deduct_deposit_gold
        order.total_deposit_gold = total_deposit_gold
        
        return render_template('orders/print.html',
                             order=order,
                             new_items=new_items,
                             return_items=return_items,
                             old_items=old_items,
                             other_items=other_items,
                             customer=customer,
                             payments=payments,
                             summary=summary,
                             metal_stats=metal_stats)
    except Exception as e:
        flash(f'获取订单打印信息失败: {str(e)}', 'danger')
        return redirect(url_for('orders'))

@app.route('/api/orders/<int:id>/generate_pdf')
@login_required
def generate_order_pdf(id):
    """生成订单PDF直接下载"""
    try:
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import mm
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.pdfbase.cidfonts import UnicodeCIDFont
        from io import BytesIO
        
        # 注册字体(使用标准字体，避免中文字体问题)
        USE_CID_FONTS = True  # 使用CID字体模式而不是TTF
        
        if USE_CID_FONTS:
            # 使用ReportLab内置的中文支持
            pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
        else:
            # 尝试注册系统字体
            try:
                # 尝试Windows字体路径
                pdfmetrics.registerFont(TTFont('SimSun', 'C:/Windows/Fonts/simsun.ttc'))
            except:
                try:
                    # 尝试应用内字体
                    pdfmetrics.registerFont(TTFont('SimSun', 'static/fonts/simsun.ttc'))
                except:
                    # 如果都失败，使用默认字体
                    app.logger.warning("无法加载中文字体文件，将使用默认字体")
                    pass
        
        # 获取订单信息
        order = Order.query.get_or_404(id)
        
        # 获取订单项并按类型分类
        order_items = OrderItem.query.filter_by(order_id=id).all()
        new_items = [item for item in order_items if item.item_type == 'new']
        return_items = [item for item in order_items if item.item_type == 'return']
        old_items = [item for item in order_items if item.item_type == 'old']
        other_items = [item for item in order_items if item.item_type == 'other']
        
        # 获取客户信息
        customer = Customer.query.get(order.customer_id) if order.customer_id else None
        
        # 计算欠料/存料
        previous_owed_gold = float(order.previous_owed_gold or 0)
        current_owed_gold = float(order.current_owed_gold or 0)
        current_return_gold = float(order.current_return_gold or 0)
        total_owed_gold = float(order.total_owed_gold or 0)
        
        previous_deposit_gold = float(order.previous_deposit_gold or 0)
        current_deposit_gold = float(order.current_deposit_gold or 0)
        deduct_deposit_gold = float(order.deduct_deposit_gold or 0)
        total_deposit_gold = float(order.total_deposit_gold or 0)
        
        # 按金属类型分组的统计信息
        metal_stats = {
            '黄金': {'total': 0, 'products': {}},
            '铂金': {'total': 0, 'products': {}},
            '白银': {'total': 0, 'products': {}},
            '玉石': {'total': 0, 'products': {}},
            '钻石': {'total': 0, 'products': {}},
            '18K金': {'total': 0, 'products': {}},
            '22K金': {'total': 0, 'products': {}}
        }
        
        # 计算各类金属总重量和产品统计
        for item in new_items:
            if item.product and item.product.metal_type:
                metal_type = item.product.metal_type
                if metal_type in metal_stats:
                    product_name = item.product.name if item.product else item.product_name
                    metal_stats[metal_type]['total'] += float(item.weight or 0)
                    
                    if product_name not in metal_stats[metal_type]['products']:
                        metal_stats[metal_type]['products'][product_name] = {'count': 0, 'weight': 0}
                    
                    metal_stats[metal_type]['products'][product_name]['count'] += 1
                    metal_stats[metal_type]['products'][product_name]['weight'] += float(item.weight or 0)
        
        # 创建内存缓冲区作为PDF输出
        buffer = BytesIO()
        
        # 自定义页面大小 (241mm x 280mm)
        page_width = 241 * mm
        page_height = 280 * mm
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            buffer, 
            pagesize=(page_width, page_height),
            rightMargin=10*mm, 
            leftMargin=10*mm,
            topMargin=10*mm, 
            bottomMargin=10*mm
        )
        
        # 获取样式
        styles = getSampleStyleSheet()
        
        # 设置字体名称
        chinese_font = 'STSong-Light' if USE_CID_FONTS else 'SimSun'
        
        # 创建中文标题样式
        title_style = ParagraphStyle(
            'ChineseTitle',
            parent=styles['Title'],
            fontName=chinese_font,
            fontSize=16,
            alignment=1  # 居中
        )
        
        # 创建中文正文样式
        normal_style = ParagraphStyle(
            'ChineseNormal',
            parent=styles['Normal'],
            fontName=chinese_font,
            fontSize=10
        )
        
        # 创建中文小号样式
        small_style = ParagraphStyle(
            'ChineseSmall',
            parent=styles['Normal'],
            fontName=chinese_font,
            fontSize=8
        )
        
        # 构建PDF内容
        elements = []
        
        # 添加标题
        elements.append(Paragraph('销售单', title_style))
        elements.append(Spacer(1, 10*mm))
        
        # 添加订单基本信息
        order_info = [
            [
                Paragraph('客户名称：' + (customer.name if customer else '无客户'), normal_style),
                Paragraph('业务时间：' + order.created_at.strftime('%Y-%m-%d %H:%M'), normal_style),
                Paragraph('单据编号：' + order.order_no, normal_style),
                Paragraph('备注：' + (order.notes or ''), normal_style)
            ]
        ]
        
        order_info_table = Table(order_info, colWidths=[page_width/4-5*mm]*4)
        order_info_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ]))
        
        elements.append(order_info_table)
        elements.append(Spacer(1, 5*mm))
        
        # 添加新货出库表格
        if new_items:
            elements.append(Paragraph('新货出库', normal_style))
            elements.append(Spacer(1, 2*mm))
            
            # 表头
            new_items_data = [
                ['序号', '项目名称', '产品编号', '产品名称', '克重', '工费', '精品费', '金价', '金额']
            ]
            
            # 表格数据
            for idx, item in enumerate(new_items, 1):
                new_items_data.append([
                    str(idx),
                    '新货出库',
                    item.product.code if item.product else item.product_code or '',
                    item.product.name if item.product else item.product_name or '',
                    f"{float(item.weight or 0):.2f}",
                    f"{float(item.labor_cost or 0):.2f}",
                    f"{float(item.premium or 0):.2f}",
                    f"{float(item.gold_price or 0):.2f}",
                    f"{float(item.amount or 0):.2f}"
                ])
            
            # 设置列宽 (总宽度为页面宽度减去左右边距)
            available_width = page_width - 20*mm
            col_widths = [
                available_width * 0.06,  # 序号
                available_width * 0.12,  # 项目名称
                available_width * 0.12,  # 产品编号
                available_width * 0.18,  # 产品名称
                available_width * 0.1,   # 克重
                available_width * 0.1,   # 工费
                available_width * 0.1,   # 精品费
                available_width * 0.1,   # 金价
                available_width * 0.12   # 金额
            ]
            
            new_items_table = Table(new_items_data, colWidths=col_widths)
            new_items_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
            ]))
            
            elements.append(new_items_table)
            elements.append(Spacer(1, 5*mm))
        
        # 添加退货录入表格
        if return_items:
            elements.append(Paragraph('退货录入', normal_style))
            elements.append(Spacer(1, 2*mm))
            
            # 表头
            return_items_data = [
                ['序号', '项目名称', '产品编号', '产品名称', '克重', '工费', '精品费', '金价', '金额']
            ]
            
            # 表格数据
            for idx, item in enumerate(return_items, 1):
                return_items_data.append([
                    str(idx),
                    '退货录入',
                    item.product.code if item.product else item.product_code or '',
                    item.product.name if item.product else item.product_name or '',
                    f"{float(item.weight or 0):.2f}",
                    f"{float(item.labor_cost or 0):.2f}",
                    f"{float(item.premium or 0):.2f}",
                    f"{float(item.gold_price or 0):.2f}",
                    f"{float(item.amount or 0):.2f}"
                ])
            
            return_items_table = Table(return_items_data, colWidths=col_widths)
            return_items_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
            ]))
            
            elements.append(return_items_table)
            elements.append(Spacer(1, 5*mm))
        
        # 添加旧料录入表格
        if old_items:
            elements.append(Paragraph('旧料录入', normal_style))
            elements.append(Spacer(1, 2*mm))
            
            # 表头
            old_items_data = [
                ['序号', '项目名称', '结算方式', '旧料名称', '克重', '成色', '净重', '料价', '金额']
            ]
            
            # 表格数据
            for idx, item in enumerate(old_items, 1):
                # 结算方式显示
                settlement_type_map = {
                    'store': '存料',
                    'owe': '欠料',
                    'settle': '结料',
                    'return': '还料',
                    'deduct': '存料抵扣'
                }
                settlement_type = settlement_type_map.get(item.settlement_type, item.settlement_type or '')
                
                old_items_data.append([
                    str(idx),
                    '旧料录入',
                    settlement_type,
                    item.material_name or '',
                    f"{float(item.weight or 0):.2f}",
                    f"{float(item.purity or 0):.2f}",
                    f"{float(item.net_weight or 0):.2f}",
                    f"{float(item.material_price or 0):.2f}",
                    f"{float(item.amount or 0):.2f}"
                ])
            
            old_items_table = Table(old_items_data, colWidths=col_widths)
            old_items_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
            ]))
            
            elements.append(old_items_table)
            elements.append(Spacer(1, 5*mm))
        
        # 添加其他项目表格
        if other_items:
            elements.append(Paragraph('其他项目', normal_style))
            elements.append(Spacer(1, 2*mm))
            
            # 表头
            other_items_data = [
                ['序号', '项目名称', '类型', '-', '-', '-', '数量', '单价', '金额']
            ]
            
            # 表格数据
            for idx, item in enumerate(other_items, 1):
                # 类型显示
                other_type_map = {
                    'enamel': '珐琅费',
                    'provincial': '省检费',
                    'national': '国检费',
                    'string': '编绳费',
                    'custom': '定制加工费',
                    'repair': '维修费',
                    'discount': '优惠'
                }
                other_type = other_type_map.get(item.other_type, item.other_type or '')
                
                other_items_data.append([
                    str(idx),
                    '其他项目',
                    other_type,
                    '-',
                    '-',
                    '-',
                    f"{float(item.quantity or 0):.2f}",
                    f"{float(item.unit_price or 0):.2f}",
                    f"{float(item.amount or 0):.2f}"
                ])
            
            other_items_table = Table(other_items_data, colWidths=col_widths)
            other_items_table.setStyle(TableStyle([
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
            ]))
            
            elements.append(other_items_table)
            elements.append(Spacer(1, 5*mm))
        
        # 添加统计信息
        if customer and customer.customer_type == 'wholesale':
            # 批发客户显示金属统计
            metal_stats_text = ""
            for metal_type, stats in metal_stats.items():
                if stats['total'] > 0:
                    metal_stats_text += f"{metal_type}总计 {stats['total']:.2f}克："
                    products_text = []
                    for product_name, data in stats['products'].items():
                        products_text.append(f"{product_name}, {data['count']}包, {data['weight']:.2f}克")
                    metal_stats_text += " | ".join(products_text) + "   "
            
            if metal_stats_text:
                elements.append(Paragraph(metal_stats_text, small_style))
                elements.append(Spacer(1, 2*mm))
            
            # 添加欠料存料信息
            owed_deposit_data = [
                [
                    Paragraph('上期欠料: ' + f"{previous_owed_gold:.2f}克", small_style),
                    Paragraph('本期欠料: ' + f"{current_owed_gold:.2f}克", small_style),
                    Paragraph('本期还料: ' + f"{current_return_gold:.2f}克", small_style),
                    Paragraph('总计欠料: ' + f"{total_owed_gold:.2f}克", small_style)
                ],
                [
                    Paragraph('上期存料: ' + f"{previous_deposit_gold:.2f}克", small_style),
                    Paragraph('本期存料: ' + f"{current_deposit_gold:.2f}克", small_style),
                    Paragraph('存料抵扣: ' + f"{deduct_deposit_gold:.2f}克", small_style),
                    Paragraph('总计存料: ' + f"{total_deposit_gold:.2f}克", small_style)
                ]
            ]
            
            owed_deposit_table = Table(owed_deposit_data, colWidths=[page_width/4-5*mm]*4)
            owed_deposit_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            elements.append(owed_deposit_table)
            elements.append(Spacer(1, 2*mm))
        
        # 添加应付金额
        elements.append(Paragraph(f'应付合计：{float(order.total_amount or 0):.2f} 元', normal_style))
        elements.append(Spacer(1, 5*mm))
        
        # 添加签名区域
        signature_data = [
            [
                Paragraph('业务员：_________________', normal_style),
                Paragraph('审核：_________________', normal_style),
                Paragraph('客户签字：_________________', normal_style)
            ]
        ]
        
        signature_table = Table(signature_data, colWidths=[page_width/3-5*mm]*3)
        signature_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'CENTER'),
            ('ALIGN', (2, 0), (2, 0), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(signature_table)
        
        # 构建PDF文档
        doc.build(elements)
        
        # 重置文件指针位置
        buffer.seek(0)
        
        # 返回PDF文件
        return send_file(
            buffer,
            as_attachment=True,
            download_name=f'订单_{order.order_no}.pdf',
            mimetype='application/pdf'
        )
    except Exception as e:
        app.logger.error(f'生成订单PDF失败: {str(e)}')
        return jsonify({'success': False, 'message': f'生成PDF失败: {str(e)}'}), 500

@app.route('/api/orders/get_id_by_no')
@login_required
def get_order_id_by_no():
    order_no = request.args.get('order_no')
    if not order_no:
        return jsonify({'success': False, 'message': '订单号不能为空'})
    
    order = Order.query.filter_by(order_no=order_no).first()
    if not order:
        return jsonify({'success': False, 'message': '未找到对应的订单'})
    
    return jsonify({'success': True, 'order_id': order.id})

def order_to_dict(order):
    """将订单转换为字典格式"""
    # 获取客户信息
    customer = Customer.query.get(order.customer_id) if order.customer_id else None
    is_wholesale = customer and customer.customer_type == 'wholesale' if customer else False
    
    # 获取订单项
    order_items = OrderItem.query.filter_by(order_id=order.id).all()
    
    # 根据类型分组订单项
    new_items = []
    return_items = []
    old_items = []
    other_items = []
    
    # 直接从订单对象中获取历史欠料和存料数据（如果存在）
    previous_owed_gold = round(order.previous_owed_gold or 0, 2)
    current_owed_gold = round(order.current_owed_gold or 0, 2)
    current_return_gold = round(order.current_return_gold or 0, 2)
    total_owed_gold = round(order.total_owed_gold or 0, 2)
    
    previous_deposit_gold = round(order.previous_deposit_gold or 0, 2)
    current_deposit_gold = round(order.current_deposit_gold or 0, 2)
    deduct_deposit_gold = round(order.deduct_deposit_gold or 0, 2)
    total_deposit_gold = round(order.total_deposit_gold or 0, 2)
    
    # 处理订单项
    for item in order_items:
        item_dict = {
            'id': item.id,
            'product_id': item.product_id,
            'productCode': item.product_code,
            'productName': item.product_name,
            'amount': float(item.amount) if item.amount else 0
        }
        
        if item.item_type == 'new':
            item_dict.update({
                'type': 'new',
                'weight': float(item.weight) if item.weight else 0,
                'labor': float(item.labor_cost) if hasattr(item, 'labor_cost') and item.labor_cost else 0,
                'premium': float(item.premium_cost) if hasattr(item, 'premium_cost') and item.premium_cost else 0,
                'goldPrice': float(item.gold_price) if hasattr(item, 'gold_price') and item.gold_price else 0
            })
            new_items.append(item_dict)
        elif item.item_type == 'return':
            item_dict.update({
                'type': 'return',
                'weight': float(item.weight) if item.weight else 0,
                'labor': float(item.labor_cost) if hasattr(item, 'labor_cost') and item.labor_cost else 0,
                'premium': float(item.premium_cost) if hasattr(item, 'premium_cost') and item.premium_cost else 0,
                'goldPrice': float(item.gold_price) if hasattr(item, 'gold_price') and item.gold_price else 0
            })
            return_items.append(item_dict)
        elif item.item_type == 'old':
            item_dict.update({
                'type': 'old',
                'weight': float(item.weight) if item.weight else 0,
                'materialName': item.material_name if hasattr(item, 'material_name') and item.material_name else '',
                'settlementType': item.settlement_type if hasattr(item, 'settlement_type') and item.settlement_type else '',
                'purity': float(item.purity) if hasattr(item, 'purity') and item.purity else 0,
                'netWeight': float(item.net_weight) if hasattr(item, 'net_weight') and item.net_weight else 0,
                'materialPrice': float(item.material_price) if hasattr(item, 'material_price') and item.material_price else 0
            })
            old_items.append(item_dict)
        elif item.item_type == 'other':
            item_dict.update({
                'type': 'other',
                'otherType': item.other_type if hasattr(item, 'other_type') and item.other_type else '',
                'quantity': float(item.quantity) if hasattr(item, 'quantity') and item.quantity else 0,
                'unitPrice': float(item.unit_price) if hasattr(item, 'unit_price') and item.unit_price else 0
            })
            other_items.append(item_dict)
    
    # 获取支付信息
    payments = Payment.query.filter_by(order_id=order.id).all()
    payments_list = []
    for payment in payments:
        payments_list.append({
            'id': payment.id,
            'type': payment.account_id,
            'amount': float(payment.amount) if payment.amount else 0,
            'paymentTime': payment.payment_date.strftime('%Y-%m-%dT%H:%M') if hasattr(payment, 'payment_date') and payment.payment_date else None
        })
    
    # 构建订单字典
    # 直接使用order_date作为业务时间，不要混合created_at的时间部分
    business_datetime = None
    if order.order_date:
        business_datetime = order.order_date
    else:
        business_datetime = datetime.now()
    
    order_dict = {
        'id': order.id,
        'customerId': order.customer_id,
        'customerName': customer.name if customer else '',
        'customerType': customer.customer_type if customer else '',
        'notes': order.notes,
        'businessTime': business_datetime.strftime('%Y-%m-%dT%H:%M'),
        'orderNo': order.order_no,
        'isPaid': order.is_paid,
        'grandTotal': float(order.total_amount) if order.total_amount else 0,
        'payments': payments_list,
        'new_items': new_items,
        'return_items': return_items,
        'old_items': old_items,
        'other_items': other_items,
    }
    
    # 如果是批发客户，添加欠料和存料信息
    if is_wholesale:
        order_dict.update({
            'previous_owed_gold': previous_owed_gold,
            'current_owed_gold': current_owed_gold,
            'current_return_gold': current_return_gold,
            'total_owed_gold': total_owed_gold,
            'previous_deposit_gold': previous_deposit_gold,
            'current_deposit_gold': current_deposit_gold,
            'deduct_deposit_gold': deduct_deposit_gold,
            'total_deposit_gold': total_deposit_gold,
            'is_wholesale': True
        })
    else:
        order_dict.update({
            'is_wholesale': False
        })
    
    return order_dict

@app.route('/orders/edit/<int:id>', methods=['GET'])
@login_required
def order_edit(id):
    order = Order.query.get_or_404(id)
    
    # 获取所有客户（余额从客户款料明细计算）
    from routes.report_routes import calculate_customer_balances
    customers = Customer.query.all()
    customers_list = []
    for c in customers:
        balances = calculate_customer_balances(c.id)
        customers_list.append({
            'id': c.id,
            'name': c.name,
            'type': c.customer_type,
            'phone': c.phone,
            'address': c.address,
            'pinyin': c.pinyin,
            'pinyin_initials': c.pinyin_initials,
            'owed_gold': balances.get('owed_gold', 0),
            'deposit_gold': balances.get('deposit_gold', 0),
            'balance': balances.get('owed_amount', 0)
        })
    
    # 获取所有产品（使用正向计算获取库存）
    from routes.report_routes import calculate_product_inventory_at_date
    from datetime import date
    products = Product.query.all()
    products_list = []
    for p in products:
        current_stock = calculate_product_inventory_at_date(p, date.today())
        products_list.append({
            'id': p.id,
            'code': p.code,
            'name': p.name,
            'stock': current_stock,
            'wholesale_labor_fee': p.wholesale_labor_fee,
            'wholesale_premium_fee': p.wholesale_premium_fee,
            'retail_labor_fee': p.retail_labor_fee,
            'retail_premium_fee': p.retail_premium_fee
        })
    
    # 获取所有旧料
    old_materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code.asc()).all()
    old_materials_list = [{
        'id': m.id,
        'name': m.name,
        'code': m.code
    } for m in old_materials]
    
    # 获取所有账户
    accounts = Account.query.filter_by(is_active=True).all()
    accounts_list = [{
        'id': a.id,
        'name': a.name,
        'type': a.account_type
    } for a in accounts]
    
    # 获取当前金价
    gold_price = 680  # 这里应该从配置或其他地方获取实时金价
    
    # 将order对象转换为可序列化的字典
    order_dict = order_to_dict(order)
    
    # 添加order_id字段，确保在前端可以识别编辑模式
    order_dict['id'] = order.id
    
    return render_template('orders/add.html',
                          order=order_dict,
                          customers=customers_list,
                          products=products_list,
                          old_materials=old_materials_list,
                          accounts=accounts_list,
                          gold_price=gold_price,
                          order_id=order.id,
                          order_no=order.order_no)

@app.route('/orders/view_edit/<int:id>', methods=['GET'])
@login_required
def order_view_edit(id):
    """查看/编辑订单（作为order_edit的别名）"""
    order = Order.query.get_or_404(id)
    
    # 获取所有客户（余额从客户款料明细计算）
    from routes.report_routes import calculate_customer_balances
    customers = Customer.query.all()
    customers_list = []
    for c in customers:
        balances = calculate_customer_balances(c.id)
        customers_list.append({
            'id': c.id,
            'name': c.name,
            'type': c.customer_type,
            'phone': c.phone,
            'address': c.address,
            'pinyin': c.pinyin,
            'pinyin_initials': c.pinyin_initials,
            'owed_gold': balances.get('owed_gold', 0),
            'deposit_gold': balances.get('deposit_gold', 0),
            'balance': balances.get('owed_amount', 0)
        })
    
    # 获取所有产品（使用正向计算获取库存）
    from routes.report_routes import calculate_product_inventory_at_date
    from datetime import date
    products = Product.query.all()
    products_list = []
    for p in products:
        current_stock = calculate_product_inventory_at_date(p, date.today())
        products_list.append({
            'id': p.id,
            'code': p.code,
            'name': p.name,
            'stock': current_stock,
            'wholesale_labor_fee': p.wholesale_labor_fee,
            'wholesale_premium_fee': p.wholesale_premium_fee,
            'retail_labor_fee': p.retail_labor_fee,
            'retail_premium_fee': p.retail_premium_fee
        })
    
    # 获取所有旧料
    old_materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code.asc()).all()
    old_materials_list = [{
        'id': m.id,
        'name': m.name,
        'code': m.code
    } for m in old_materials]
    
    # 获取所有账户
    accounts = Account.query.filter_by(is_active=True).all()
    accounts_list = [{
        'id': a.id,
        'name': a.name,
        'type': a.account_type
    } for a in accounts]
    
    # 获取当前金价
    gold_price = 680  # 这里应该从配置或其他地方获取实时金价
    
    # 将order对象转换为可序列化的字典
    order_dict = order_to_dict(order)
    
    return render_template('orders/add.html',
                           order=order_dict,
                           customers=customers_list,
                           products=products_list,
                           old_materials=old_materials_list,
                           accounts=accounts_list,
                           gold_price=gold_price,
                           order_id=order.id,
                           order_no=order.order_no)

@app.route('/orders/deleted')
@login_required
def orders_deleted():
    """已删除订单列表页面"""
    # 获取搜索参数
    search = request.args.get('search', '')
    
    # 查询已删除的订单
    query = Order.query.filter(Order.is_deleted == True)
    
    # 应用搜索筛选
    if search:
        search_term = f"%{search}%"
        query = query.join(Customer, Order.customer_id == Customer.id, isouter=True) \
                    .filter(
                        or_(
                            Order.order_no.like(search_term),
                            Customer.name.like(search_term)
                        )
                    )
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = 20
    pagination = query.order_by(Order.created_at.desc()).paginate(page=page, per_page=per_page)
    orders = pagination.items
    
    # 计算序号
    order_numbers = {}
    start_index = (page - 1) * per_page + 1
    for i, order in enumerate(orders):
        order_numbers[order.id] = start_index + i
    
    # 获取统计数据
    today = datetime.now().date()
    today_start = datetime.combine(today, time.min)
    today_end = datetime.combine(today, time.max)
    
    stats = {
        'order_count': Order.query.filter(
            Order.created_at.between(today_start, today_end),
            Order.is_deleted == False
        ).count(),
        
        'today_sales': db.session.query(func.sum(Order.total_amount)).filter(
            Order.created_at.between(today_start, today_end),
            Order.is_deleted == False
        ).scalar() or 0,
        
        'today_gold_out': db.session.query(func.sum(Order.gold_out)).filter(
            Order.created_at.between(today_start, today_end),
            Order.is_deleted == False
        ).scalar() or 0,
        
        'today_gold_return': db.session.query(func.sum(Order.gold_return)).filter(
            Order.created_at.between(today_start, today_end),
            Order.is_deleted == False
        ).scalar() or 0,
        
        'today_gold_recycle': db.session.query(func.sum(Order.gold_recycle)).filter(
            Order.created_at.between(today_start, today_end),
            Order.is_deleted == False
        ).scalar() or 0,
        
        'today_unpaid_count': Order.query.filter(
            Order.created_at.between(today_start, today_end),
            Order.is_paid == False,
            Order.is_deleted == False
        ).count()
    }
    
    return render_template('orders/deleted.html',
                           orders=orders,
                           order_numbers=order_numbers,
                           pagination=pagination,
                           search=search,
                           stats=stats)

@app.route('/orders/<int:id>/restore', methods=['POST'])
@login_required
def order_restore(id):
    """恢复已删除的订单"""
    try:
        order = Order.query.get_or_404(id)
        
        # 检查订单是否已标记为删除
        if not order.is_deleted:
            flash('只能恢复已标记为删除的订单', 'warning')
            return redirect(url_for('orders'))
        
        # 注意：客户的欠料和存料信息现在通过计算属性自动从客户款料明细中计算
        # 不需要手动修改，恢复订单时会通过process_inventory_and_payment函数处理相关的客户款料明细变化
        if order.customer_id:
            customer = Customer.query.get(order.customer_id)
            if customer and customer.customer_type == 'wholesale':
                # 记录恢复前的客户存欠料信息（仅用于日志）
                old_owed_gold = customer.owed_gold
                old_deposit_gold = customer.deposit_gold
                app.logger.info(f"恢复前客户存欠料: 客户={customer.name}, 欠料={old_owed_gold}, 存料={old_deposit_gold}")
        
        # 恢复库存变更
        process_inventory_and_payment(order, 'add')
        
        # 执行恢复
        order.is_deleted = False
        order.deleted_at = None
        db.session.commit()
        
        flash('订单已成功恢复', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'恢复订单失败: {str(e)}', 'danger')
    
    return redirect(url_for('orders_deleted'))

@app.route('/orders/deleted/clear', methods=['POST'])
@login_required
def orders_deleted_clear():
    """清空已删除的订单列表（永久删除）
    注意：已删除的订单在删除时已经回滚了数据联动，清空时不需要再次回滚
    """
    try:
        # 查询所有已删除的订单
        deleted_orders = Order.query.filter(Order.is_deleted == True).all()

        if not deleted_orders:
            flash('没有已删除的订单需要清空', 'warning')
            return redirect(url_for('orders_deleted'))

        # 记录删除的订单数量
        order_count = len(deleted_orders)
        app.logger.info(f"开始清空 {order_count} 个已删除订单")

        # 循环删除每个订单及其关联数据
        for order in deleted_orders:
            app.logger.info(f"永久删除订单: {order.order_no}, ID={order.id}")

            # 删除关联的账户交易记录
            account_transactions = AccountTransaction.query.filter_by(order_id=order.id).all()
            for at in account_transactions:
                app.logger.info(f"删除关联账户交易记录: ID={at.id}")
                db.session.delete(at)

            # 删除关联的支付记录
            payments = Payment.query.filter_by(order_id=order.id).all()
            for payment in payments:
                app.logger.info(f"删除支付记录: ID={payment.id}, 金额={payment.amount}")
                db.session.delete(payment)

            # 删除关联的订单项
            order_items = OrderItem.query.filter_by(order_id=order.id).all()
            for item in order_items:
                app.logger.info(f"删除订单项: ID={item.id}, 类型={item.item_type}")
                db.session.delete(item)

            # 删除订单本身
            db.session.delete(order)

        # 提交事务
        db.session.commit()
        app.logger.info(f"成功清空 {order_count} 个已删除的订单")
        flash(f'成功清空 {order_count} 个已删除的订单', 'success')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"清空已删除订单失败: {str(e)}")
        flash(f'清空已删除订单失败: {str(e)}', 'danger')

    return redirect(url_for('orders_deleted'))

@app.route('/orders/<int:id>/edit', methods=['POST'])
@login_required
def order_update(id):
    """更新订单"""
    try:
        order = Order.query.get_or_404(id)
        data = request.json
        
        # 保存旧订单数据，用于后续的库存回滚
        old_order = Order.query.get(id) # 获取旧订单数据的副本
        
        # 获取客户信息
        customer = Customer.query.get(order.customer_id) if order.customer_id else None
        is_wholesale = customer and customer.customer_type == 'wholesale'
        
        # 批发客户的存欠料现在通过计算属性自动获取，无需手动回滚
        
        # 删除旧的订单项目并回滚之前的库存变动和客户欠款变动
        process_inventory_and_payment(order, 'delete')  # 先回滚原订单的库存变动和支付记录
        OrderItem.query.filter_by(order_id=id).delete()
        
        # 删除旧的支付记录
        Payment.query.filter_by(order_id=id).delete()
        
        # 更新订单基本信息
        order.customer_id = data.get('customerId')
        # 处理业务时间，支持datetime-local格式
        business_time_str = data.get('businessTime', data.get('orderDate', datetime.now().strftime('%Y-%m-%dT%H:%M')))
        try:
            if 'T' in business_time_str:
                # datetime-local格式：2025-07-11T16:30
                order.order_date = datetime.strptime(business_time_str, '%Y-%m-%dT%H:%M')
            else:
                # 只有日期格式：2025-07-11
                order.order_date = datetime.strptime(business_time_str, '%Y-%m-%d')
        except (ValueError, TypeError) as e:
            app.logger.error(f'编辑订单时解析业务时间出错: {str(e)}, 原始值: {business_time_str}')
            order.order_date = datetime.now()
        # 折扣功能已删除
        order.notes = data.get('notes', '')
        order.updated_at = datetime.now()
        order.account_id = data.get('accountId')
        
        # 重置订单金额和黄金统计
        order.total_amount = 0
        order.gold_out = 0
        order.gold_return = 0
        order.gold_recycle = 0
        
        # 重置其他材料的统计
        order.platinum_out = 0
        order.platinum_return = 0
        order.silver_out = 0
        order.silver_return = 0
        order.jade_out = 0
        order.jade_return = 0
        order.diamond_out = 0
        order.diamond_return = 0
        order.k18_out = 0
        order.k18_return = 0
        order.k22_out = 0
        order.k22_return = 0
        
        # 保存原有的存欠料数据，避免在更新过程中丢失
        original_previous_owed_gold = order.previous_owed_gold
        original_previous_deposit_gold = order.previous_deposit_gold
        
        # 只重置本期数据，不重置上期和总计数据
        order.current_owed_gold = 0
        order.current_return_gold = 0
        order.current_deposit_gold = 0
        order.deduct_deposit_gold = 0
        
        # 添加新的订单项目
        # 重新获取客户，因为可能已经更改了客户ID
        customer = Customer.query.get(order.customer_id) if order.customer_id else None
        is_wholesale = customer and customer.customer_type == 'wholesale'
        
        items_data = data.get('items', [])
        total_amount = 0
        
        # 处理批发客户的存欠料字段
        if is_wholesale:
            # 保持上期值不变！上期值是订单创建时的历史快照，不应该在编辑时改变
            # 如果这是第一次设置（可能是从旧数据迁移），才设置初始值
            if original_previous_owed_gold is None:
                order.previous_owed_gold = customer.owed_gold if customer else 0
            else:
                order.previous_owed_gold = original_previous_owed_gold
                
            if original_previous_deposit_gold is None:
                order.previous_deposit_gold = customer.deposit_gold if customer else 0
            else:
                order.previous_deposit_gold = original_previous_deposit_gold
                
            print(f"保持订单上期值不变: 欠料={order.previous_owed_gold}, 存料={order.previous_deposit_gold}")
        
        for item_data in items_data:
            item = OrderItem(
                order_id=order.id,
                item_type=item_data.get('type'),
                product_id=item_data.get('product_id'),
                product_code=item_data.get('productCode', ''),
                product_name=item_data.get('productName', ''),
                weight=item_data.get('weight', 0),
                amount=item_data.get('amount', 0)
            )
            
            if item.item_type == 'new':
                item.labor_cost = item_data.get('labor', 0)
                item.premium_cost = item_data.get('premium', 0)
                item.gold_price = item_data.get('goldPrice', 0)
                # 只有黄金相关产品才计入gold_out
                is_gold_product = any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']) if item.product_name else False
                if is_gold_product:
                    order.gold_out += float(item.weight or 0)
                # 新货出库不再计入欠料
            elif item.item_type == 'return':
                item.labor_cost = item_data.get('labor', 0)
                item.premium_cost = item_data.get('premium', 0)
                item.gold_price = item_data.get('goldPrice', 0)
                # 只有黄金相关产品才计入gold_return
                is_gold_product = any(keyword in item.product_name for keyword in ['黄金', '足金', '3D硬金', '5G黄金', '5D黄金']) if item.product_name else False
                if is_gold_product:
                    order.gold_return += float(item.weight or 0)
                # 退货录入不再计入还料
            elif item.item_type == 'old':
                item.material_name = item_data.get('materialName', '')
                item.settlement_type = item_data.get('settlementType', '')
                item.purity = item_data.get('purity', 1.0)
                item.net_weight = item_data.get('netWeight', 0)
                item.material_price = item_data.get('materialPrice', 0)
                
                # 旧料黄金统计和批发客户的存欠料统计
                if item.material_name == '旧料足金':
                    # 只有结算方式为存料、还料或结料时才计入旧料回收统计
                    if item.settlement_type in ['store', 'return', 'settle']:
                        order.gold_recycle += float(item.net_weight or 0)
                    
                    # 批发客户的存欠料处理
                    if is_wholesale:
                        # 欠料增加
                        if item.settlement_type == 'owe':
                            order.current_owed_gold += float(item.net_weight or 0)
                        # 还料增加
                        elif item.settlement_type == 'return':
                            order.current_return_gold += float(item.net_weight or 0)
                        # 存料增加
                        elif item.settlement_type == 'store':
                            order.current_deposit_gold += float(item.net_weight or 0)
                        # 抵扣增加
                        elif item.settlement_type == 'deduct':
                            order.deduct_deposit_gold += float(item.net_weight or 0)
            elif item.item_type == 'other':
                item.other_type = item_data.get('otherType', '')
                item.quantity = item_data.get('quantity', 0)
                item.unit_price = item_data.get('unitPrice', 0)
            
            db.session.add(item)
            total_amount += float(item.amount or 0)
        
        # 更新订单总金额
        order.total_amount = total_amount
        # net_amount字段已删除，直接使用total_amount
        
        # 更新批发客户的总计存欠料
        if is_wholesale and customer:
            # 总欠料 = 上期欠料 + 本期欠料 - 本期还料（使用精确计算）
            previous_owed = DecimalUtils.safe_float(order.previous_owed_gold)
            current_owed = DecimalUtils.safe_float(order.current_owed_gold)
            current_return = DecimalUtils.safe_float(order.current_return_gold)
            order.total_owed_gold = safe_round_3(safe_subtract(safe_add(previous_owed, current_owed, 3), current_return, 3))

            # 总存料 = 上期存料 + 本期存料 - 抵扣存料（使用精确计算）
            previous_deposit = DecimalUtils.safe_float(order.previous_deposit_gold)
            current_deposit = DecimalUtils.safe_float(order.current_deposit_gold)
            deduct_deposit = DecimalUtils.safe_float(order.deduct_deposit_gold)
            order.total_deposit_gold = safe_round_3(safe_subtract(safe_add(previous_deposit, current_deposit, 3), deduct_deposit, 3))

            # 客户存欠料现在通过计算属性自动获取，无需手动更新
            print(f"订单存欠料计算: 新欠料={order.total_owed_gold}(上期={order.previous_owed_gold}, 本期欠={order.current_owed_gold}, 本期还={order.current_return_gold})")
            print(f"订单存欠料计算: 新存料={order.total_deposit_gold}(上期={order.previous_deposit_gold}, 本期存={order.current_deposit_gold}, 本期抵扣={order.deduct_deposit_gold})")

            # 保存当前订单的新总计值，用于级联更新
            new_total_owed_gold = order.total_owed_gold
            new_total_deposit_gold = order.total_deposit_gold
        
        # 更新订单的欠料状态
        if order.current_owed_gold > 0:
            order.is_material_owed = True
            print(f"订单 {order.order_no} 标记为欠料: 本期欠料={order.current_owed_gold}")
        else:
            order.is_material_owed = False
            print(f"订单 {order.order_no} 标记为不欠料: 本期欠料={order.current_owed_gold}")
        
        # 添加支付记录
        payments_data = data.get('payments', [])
        total_payment = 0
        for payment_data in payments_data:
            # 处理支付时间
            payment_time = payment_data.get('paymentTime')
            try:
                payment_date = datetime.strptime(payment_time, '%Y-%m-%dT%H:%M') if payment_time else datetime.now()
            except ValueError:
                payment_date = datetime.now()
                
            payment_amount = float(payment_data.get('amount', 0))
            total_payment += payment_amount
                
            payment = Payment(
                order_id=order.id,
                account_id=payment_data.get('type'),
                amount=payment_amount,
                payment_date=payment_date  # 只使用payment_date字段
            )
            db.session.add(payment)
            print(f"添加支付记录: order_id={payment.order_id}, account_id={payment.account_id}, amount={payment.amount}, payment_date={payment.payment_date}")

            # 注意：账户交易记录将在process_inventory_and_payment函数中统一创建，避免重复
        
        # 更新订单结清状态（使用total_amount代替net_amount）
        if order.total_amount >= 0:
            # 正常情况：应付金额为正，支付金额大于等于应付金额
            order.is_paid = total_payment >= order.total_amount
        else:
            # 特殊情况：应付金额为负值，支付金额应该为0或负数才算结清
            order.is_paid = total_payment <= order.total_amount

        if order.is_paid:
            print(f"订单 {order.order_no} 已标记为已结清: 支付总额={total_payment}, 订单总额={order.total_amount}")
        else:
            print(f"订单 {order.order_no} 标记为未结清: 支付总额={total_payment}, 订单总额={order.total_amount}")
        
        # 创建一个会话级变量存储订单，以便在提交数据库更改前应用库存变动
        db.session.flush()
        
        # 重新应用库存和支付变更
        process_inventory_and_payment(order, 'add')  # 应用新订单的库存变动

        # 如果是批发客户且存欠料发生变化，执行级联更新
        if is_wholesale and customer and 'new_total_owed_gold' in locals():
            from order_cascade_service import OrderCascadeService
            app.logger.info(f"开始级联更新订单 {order.id} 后续订单的上期值")
            cascade_success = OrderCascadeService.update_subsequent_orders_previous_values(
                customer.id, order.id, new_total_owed_gold, new_total_deposit_gold
            )
            if not cascade_success:
                app.logger.warning(f"订单 {order.id} 级联更新失败，但订单本身已保存")

        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '订单更新成功',
            'orderId': order.id
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新订单失败: {str(e)}'
        }), 500

def calculate_daily_account_balances(start_date, end_date, accounts):
    """
    计算每日账户余额数据，确保前一天期末余额等于第二天期初余额
    """
    from datetime import datetime, timedelta

    daily_balances = []
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

    # 遍历每一天
    current_date = start_date_obj
    while current_date <= end_date_obj:
        date_str = current_date.strftime('%Y-%m-%d')

        # 计算每个账户在当日的余额变化
        accounts_data = []
        for account in accounts:
            # 计算期初余额：使用前一天的期末余额
            if current_date == start_date_obj:
                # 第一天：使用账户初始余额 + 开始日期之前的所有交易变化
                initial_balance = float(account.initial_balance or 0)
                before_period_transactions = AccountTransaction.query.filter(
                    AccountTransaction.account_id == account.id,
                    AccountTransaction.transaction_date < current_date
                ).all()

                before_period_change = 0.0
                for trans in before_period_transactions:
                    if trans.transaction_type == 'income':
                        before_period_change += float(trans.amount) if trans.amount else 0
                    else:
                        before_period_change -= float(trans.amount) if trans.amount else 0

                opening_balance = initial_balance + before_period_change
            else:
                # 其他天：使用前一天的期末余额作为期初余额
                previous_day_data = None
                for daily_data in daily_balances:
                    if daily_data['date'] == (current_date - timedelta(days=1)).strftime('%Y-%m-%d'):
                        for acc_data in daily_data['accounts']:
                            if acc_data['id'] == account.id:
                                previous_day_data = acc_data
                                break
                        break

                opening_balance = previous_day_data['closing_balance'] if previous_day_data else 0.0

            # 计算当日的收入和支出
            daily_transactions = AccountTransaction.query.filter(
                AccountTransaction.account_id == account.id,
                AccountTransaction.transaction_date == current_date
            ).all()

            daily_income = 0.0
            daily_expense = 0.0
            for trans in daily_transactions:
                if trans.transaction_type == 'income':
                    daily_income += float(trans.amount) if trans.amount else 0
                else:
                    daily_expense += float(trans.amount) if trans.amount else 0

            # 计算期末余额
            closing_balance = opening_balance + daily_income - daily_expense

            accounts_data.append({
                'id': account.id,
                'name': account.name,
                'opening_balance': round(opening_balance, 2),
                'income': round(daily_income, 2),
                'expense': round(daily_expense, 2),
                'closing_balance': round(closing_balance, 2),
                'net_change': round(daily_income - daily_expense, 2)
            })

        daily_balances.append({
            'date': date_str,
            'accounts': accounts_data
        })

        current_date += timedelta(days=1)

    return daily_balances

def calculate_period_account_balances(start_date, end_date, accounts):
    """
    计算时间段账户余额汇总数据（原有逻辑）
    """
    account_balances = []

    for account in accounts:
        # 初始化账户余额变量
        current_balance = float(account.balance or 0)  # 当前实际余额

        account_data = {
            'id': account.id,
            'name': account.name,
            'account_type': account.account_type,
            'initial_balance': 0.0,  # 期初余额，稍后计算
            'income': 0.0,
            'expense': 0.0,
            'current_balance': current_balance,  # 当前实际余额
            'final_balance': 0.0  # 期末余额，稍后计算
        }

        # 1. 查询账户交易记录（AccountTransaction表）
        account_transactions = AccountTransaction.query.filter(
            AccountTransaction.account_id == account.id,
            AccountTransaction.transaction_date >= start_date,
            AccountTransaction.transaction_date < (datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
        ).all()

        # 计算AccountTransaction的收入和支出（这已经包含了所有账户变动）
        for trans in account_transactions:
            if trans.transaction_type in ['income', 'deposit']:
                account_data['income'] += float(trans.amount) if trans.amount else 0
            elif trans.transaction_type in ['expense', 'withdrawal']:
                account_data['expense'] += float(trans.amount) if trans.amount else 0

        # 使用正向计算方法：期初余额是固定的，然后正向推算期末余额
        # 计算期初余额：使用账户的初始余额 + 选定时间段之前的所有交易变化
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')

        # 获取账户的初始余额（固定值）
        initial_balance = float(account.initial_balance or 0)

        # 计算选定时间段之前的所有交易变化
        before_period_transactions = AccountTransaction.query.filter(
            AccountTransaction.account_id == account.id,
            AccountTransaction.transaction_date < start_date
        ).all()

        before_period_change = 0.0
        for trans in before_period_transactions:
            if trans.transaction_type in ['income', 'deposit']:
                before_period_change += float(trans.amount) if trans.amount else 0
            elif trans.transaction_type in ['expense', 'withdrawal']:
                before_period_change -= float(trans.amount) if trans.amount else 0

        # 期初余额 = 账户初始余额 + 选定时间段之前的净变化
        account_data['initial_balance'] = initial_balance + before_period_change

        # 期末余额 = 期初余额 + 选定时间段内的净变化（正向计算）
        period_net_change = account_data['income'] - account_data['expense']
        account_data['final_balance'] = account_data['initial_balance'] + period_net_change

        # 确保所有浮点数都保留两位小数
        account_data['initial_balance'] = round(account_data['initial_balance'], 2)
        account_data['income'] = round(account_data['income'], 2)
        account_data['expense'] = round(account_data['expense'], 2)
        account_data['current_balance'] = round(account_data['current_balance'], 2)
        account_data['final_balance'] = round(account_data['final_balance'], 2)

        # 添加到列表
        account_balances.append(account_data)

    return account_balances

def process_inventory_and_payment(order, action='add'):
    """
    处理库存和支付逻辑
    
    参数:
    - order: 订单对象
    - action: 操作类型，'add'为添加，'delete'为删除（回滚）
    """
    try:
        app.logger.info(f"开始处理订单 {order.order_no} 的库存和支付 - 操作:{action}")
        
        # 获取订单项
        order_items = OrderItem.query.filter_by(order_id=order.id).all()
        
        # 获取客户信息
        customer = Customer.query.get(order.customer_id) if order.customer_id else None
        
        # 系数：添加为1，删除为-1（用于回滚）
        coefficient = 1 if action == 'add' else -1
        
        # 使用字典跟踪产品和旧料的库存变更，避免重复处理
        product_inventory_changes = {}  # {product_id: total_weight_change}
        material_inventory_changes = {}  # {material_name: total_weight_change}
        
        # 1. 收集所有产品的库存变更
        for item in order_items:
            if item.item_type == 'new' or item.item_type == 'return':
                # 尝试通过product_id查找产品
                product = None
                if item.product_id:
                    product = Product.query.get(item.product_id)
                
                # 如果通过ID未找到产品，尝试通过编码查找
                if not product and item.product_code:
                    product = Product.query.filter_by(code=item.product_code).first()
                
                # 如果还未找到产品，尝试通过名称查找
                if not product and item.product_name:
                    product = Product.query.filter_by(name=item.product_name).first()
                
                if product:
                    # 使用产品ID作为键
                    product_key = product.id
                    weight_change = float(item.weight or 0)
                    
                    # 根据订单项类型确定是增加还是减少库存
                    if item.item_type == 'new':
                        # 新货出库: 减少库存
                        if product_key in product_inventory_changes:
                            product_inventory_changes[product_key] -= weight_change
                        else:
                            product_inventory_changes[product_key] = -weight_change
                    elif item.item_type == 'return':
                        # 退货录入: 增加库存
                        if product_key in product_inventory_changes:
                            product_inventory_changes[product_key] += weight_change
                        else:
                            product_inventory_changes[product_key] = weight_change
        
        # 2. 收集所有旧料的库存变更
        for item in order_items:
            if item.item_type == 'old' and item.material_name and item.settlement_type in ['store', 'return', 'settle']:
                # 查找对应的旧料
                old_material = OldMaterial.query.filter_by(name=item.material_name).first()
                if old_material:
                    # 使用旧料名称作为键
                    material_key = old_material.name
                    weight_change = float(item.net_weight or 0)
                    
                    # 旧料入库: 增加库存
                    if material_key in material_inventory_changes:
                        material_inventory_changes[material_key] += weight_change
                    else:
                        material_inventory_changes[material_key] = weight_change
        
        # 3. 记录产品库存变更（库存现在由正向计算得出，不再直接修改）
        for product_id, weight_change in product_inventory_changes.items():
            product = Product.query.get(product_id)
            if product:
                # 应用系数（add或delete）
                final_change = coefficient * weight_change
                app.logger.info(f"订单影响产品 '{product.name}' 库存变化: {final_change:+.3f}克 (库存由系统正向计算)")

        # 4. 记录旧料库存变更（库存由料部库存查询计算，此处仅记录日志）
        for material_name, weight_change in material_inventory_changes.items():
            final_change = coefficient * weight_change
            app.logger.info(f"旧料 '{material_name}' 库存变更记录: 变化{final_change:+.3f}克（库存由料部库存查询计算）")

        # 5. 处理支付记录和账户余额（客户欠款现在通过计算属性自动获取）
        if customer:
            # 存储支付记录变动
            account_changes = {}  # {account_id: total_amount_change}

            # 处理支付记录
            payments = Payment.query.filter_by(order_id=order.id).all()
            app.logger.info(f"订单 {order.order_no} 的支付记录数量: {len(payments)}")

            for payment in payments:
                if payment.amount:
                    # 支付金额调整账户余额
                    payment_amount = float(payment.amount or 0)
                    payment_adjustment = coefficient * payment_amount

                    # 支付金额增加账户余额
                    if payment.account_id:
                        account = Account.query.get(payment.account_id)
                        if account:
                            old_account_balance = account.balance
                            account.balance += payment_adjustment

                            # 跟踪账户变动
                            if payment.account_id in account_changes:
                                account_changes[payment.account_id] += payment_adjustment
                            else:
                                account_changes[payment.account_id] = payment_adjustment
                                
                            app.logger.info(f"账户 '{account.name}' 余额更新: {old_account_balance} -> {account.balance} (支付调整: {payment_adjustment})")
                            
                            # 添加账户交易记录
                            if action == 'add':  # 只在添加操作时创建交易记录
                                # 检查是否已存在相同的账户交易记录，避免重复创建
                                existing_transaction = AccountTransaction.query.filter_by(
                                    order_id=order.id,
                                    account_id=account.id
                                ).first()

                                if not existing_transaction:
                                    # 创建账户交易记录
                                    transaction_date = datetime.now().date()
                                    if hasattr(payment, 'payment_date') and payment.payment_date:
                                        try:
                                            # 尝试获取支付日期
                                            transaction_date = payment.payment_date.date()
                                        except:
                                            pass

                                    # 根据支付金额的正负确定交易类型和用途
                                    if payment_amount >= 0:
                                        # 正值：销售收款（收入）
                                        transaction_type = 'income'
                                        purpose = '销售收款'
                                        amount_for_record = payment_amount
                                    else:
                                        # 负值：销售扣款（支出）
                                        transaction_type = 'expense'
                                        purpose = '销售扣款'
                                        amount_for_record = payment_amount  # 保持负值，不使用绝对值

                                    # 为每笔支付创建账户交易记录
                                    account_transaction = AccountTransaction(
                                        account_id=account.id,
                                        transaction_date=transaction_date,
                                        amount=amount_for_record,
                                        transaction_type=transaction_type,
                                        purpose=purpose,
                                        related_party=customer.name if customer else '',
                                        related_document=order.order_no,
                                        notes=order.notes if order.notes else '',  # 使用订单备注，如果没有则为空
                                        order_id=order.id,
                                        customer_id=customer.id
                                    )
                                    db.session.add(account_transaction)
                                    app.logger.info(f"添加账户交易记录: 账户={account.name}, 金额={amount_for_record}, 类型={transaction_type}, 用途={purpose}, 关联单据={order.order_no}")
                                else:
                                    app.logger.info(f"账户交易记录已存在，跳过创建: 订单={order.order_no}, 账户={account.name}, 金额={payment_amount}")
                            
                            # 如果是删除操作，需要删除相关的账户交易记录
                            elif action == 'delete':
                                account_transactions = AccountTransaction.query.filter_by(
                                    order_id=order.id,
                                    account_id=account.id
                                ).all()
                                
                                for trans in account_transactions:
                                    app.logger.info(f"删除账户交易记录: ID={trans.id}, 账户={account.name}, 金额={trans.amount}, 关联单据={order.order_no}")
                                    db.session.delete(trans)
                        else:
                            app.logger.warning(f"支付ID={payment.id}引用的账户ID={payment.account_id}不存在")
            
            # 汇总账户变动
            for account_id, amount_change in account_changes.items():
                account = Account.query.get(account_id)
                if account:
                    app.logger.info(f"账户 '{account.name}' 总变动: {amount_change}，最终余额: {account.balance}")

            # 更新订单的结清状态
            if action == 'add':
                # 计算所有支付记录的总金额
                payments_total = sum(payment.amount for payment in payments if payment.amount)
                
                # 使用total_amount代替net_amount进行支付状态判断
                if order.total_amount >= 0:
                    # 正常情况：应付金额为正，支付金额大于等于应付金额
                    order.is_paid = payments_total >= order.total_amount
                else:
                    # 特殊情况：应付金额为负值，支付金额应该为0或负数才算结清
                    order.is_paid = payments_total <= order.total_amount

                if order.is_paid:
                    app.logger.info(f"订单 {order.order_no} 已标记为已结清: 支付总额={payments_total}, 订单总额={order.total_amount}")
                else:
                    app.logger.info(f"订单 {order.order_no} 标记为未结清: 支付总额={payments_total}, 订单总额={order.total_amount}")

        # 提交事务
        db.session.commit()
        app.logger.info(f"订单 {order.order_no} 的库存和支付处理完成 - 操作:{action}")
        return True
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"处理订单 {order.order_no} 的库存和支付时出错 - 操作:{action}, 错误:{str(e)}")
        app.logger.error(traceback.format_exc())
        return False

# 导入报表API路由
try:
    from routes.report_routes import *
    logger.info("报表API路由模块加载成功!")
except Exception as e:
    logger.error(f"加载报表API路由模块失败: {str(e)}")

# 导入用户管理路由
try:
    from routes.user_routes import user_bp
    app.register_blueprint(user_bp)
    logger.info("用户管理路由模块加载成功!")
except Exception as e:
    logger.error(f"加载用户管理路由模块失败: {str(e)}")

# 供应商往来API路由已在app.py中定义，无需额外导入
logger.info("供应商往来API路由已就绪!")



@app.route('/api/accounts', methods=['GET'])
@login_required
def api_accounts():
    """获取账户列表API - 用于下拉框和报表"""
    try:
        accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()
        accounts_list = []

        for account in accounts:
            account_data = {
                'name': account.name,
                'id': account.id,
                'balance': float(account.balance) if account.balance else 0.0
            }
            accounts_list.append(account_data)

        return jsonify({
            'status': 'success',
            'data': accounts_list
        })
    except Exception as e:
        app.logger.error(f"获取账户列表失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取账户列表失败: {str(e)}'
        }), 500





@app.route('/api/supplier_transactions/get/<int:transaction_id>', methods=['GET'])
@login_required
def api_supplier_transaction_get(transaction_id):
    """获取供应商往来交易数据API"""
    try:
        app.logger.info(f"获取供应商往来交易数据，ID: {transaction_id}")

        # 获取交易记录
        transaction = SupplierTransaction.query.get(transaction_id)
        if not transaction:
            app.logger.error(f"未找到ID为{transaction_id}的供应商往来记录")
            return jsonify({
                'success': False,
                'status': 'error',
                'message': f'未找到ID为{transaction_id}的供应商往来记录'
            }), 404

        # 获取关联的物料交易和款项交易
        material_transactions = MaterialTransaction.query.filter_by(transaction_id=transaction.id).all()
        money_transactions = MoneyTransaction.query.filter_by(transaction_id=transaction.id).all()

        # 构建返回数据
        transaction_data = {
            'id': transaction.id,
            'supplier_id': transaction.supplier_id,
            'business_date': transaction.business_date.strftime('%Y-%m-%d') if transaction.business_date else '',
            # 编辑模式下使用created_at作为完整的业务日期时间（用于上期余额计算）
            'business_datetime': transaction.created_at.strftime('%Y-%m-%dT%H:%M') if transaction.created_at else '',
            'created_at': transaction.created_at.isoformat() if transaction.created_at else '',
            'transaction_no': transaction.transaction_no,
            'notes': transaction.notes or '',
            'material_transactions': [],
            'money_transactions': [],
            # 添加历史余额数据
            'previous_owed_gold': float(transaction.previous_owed_gold or 0),
            'previous_deposit_gold': float(transaction.previous_deposit_gold or 0),
            'previous_owed_amount': float(transaction.previous_owed_amount or 0),
            'previous_deposit_amount': float(transaction.previous_deposit_amount or 0)
        }

        app.logger.info(f"找到物料交易记录: {len(material_transactions)}条")
        app.logger.info(f"找到款项交易记录: {len(money_transactions)}条")

        # 添加物料交易数据
        for mt in material_transactions:
            transaction_data['material_transactions'].append({
                'return_weight': float(mt.return_weight or 0),
                'return_material_type': mt.return_material_type or '',
                'return_source': mt.return_source or '',
                'wastage_loss': float(mt.wastage_loss or 0),
                'store_weight': float(mt.store_weight or 0),
                'store_material_type': mt.store_material_type or '',
                'store_source': mt.store_source or '',
                'deposit_weight': float(mt.deposit_weight or 0),
                'deposit_material_type': mt.deposit_material_type or '',
                'actual_deposit_weight': float(mt.actual_deposit_weight or 0),
                'deposit_loss': float(mt.deposit_loss or 0),
                'note': mt.note or ''
            })

        # 添加款项交易数据
        for mt in money_transactions:
            transaction_data['money_transactions'].append({
                'return_amount': float(mt.return_amount or 0),
                'return_source': mt.return_source or '',
                'return_purpose': mt.return_purpose or '还款',
                'return_material_type': mt.return_material_type or '',
                'return_weight': float(mt.return_weight or 0),
                'store_amount': float(mt.store_amount or 0),
                'store_source': mt.store_source or '',
                'note': mt.note or ''
            })

        app.logger.info(f"成功获取交易数据，物料交易: {len(material_transactions)}条，款项交易: {len(money_transactions)}条")

        return jsonify({
            'success': True,
            'status': 'success',
            'transaction': transaction_data
        })

    except Exception as e:
        app.logger.error(f"获取供应商往来交易数据失败: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'status': 'error',
            'message': f'获取交易数据失败: {str(e)}'
        }), 500

@app.route('/test_delete_records')
@login_required
def test_delete_records():
    """测试删除记录页面"""
    return render_template('test_delete_records.html')

@app.route('/api/delete_test_records', methods=['POST'])
@login_required
def api_delete_test_records():
    """删除测试记录API"""
    try:
        # 查找所有TEST开头的记录
        test_transactions = SupplierTransaction.query.filter(
            SupplierTransaction.transaction_no.like('TEST%')
        ).all()

        deleted_count = 0
        for transaction in test_transactions:
            # 删除关联的物料交易
            MaterialTransaction.query.filter_by(transaction_id=transaction.id).delete()
            # 删除关联的款项交易
            MoneyTransaction.query.filter_by(transaction_id=transaction.id).delete()
            # 删除主记录
            db.session.delete(transaction)
            deleted_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功删除 {deleted_count} 个测试记录'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除测试记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500

@app.route('/api/suppliers', methods=['GET'])
@login_required
def api_suppliers():
    """获取供应商API - 统一接口支持多种格式和过滤"""
    try:
        # 获取参数
        supplier_type = request.args.get('type', '')
        include_balances = request.args.get('include_balances', 'false').lower() == 'true'
        format_type = request.args.get('format', 'standard')  # standard, simple, detailed

        query = Supplier.query.filter_by(is_active=True)

        # 类型过滤
        if supplier_type:
            if supplier_type == 'gold_material':
                # 兼容旧的参数名，映射到gold, gold_material, material类型
                query = query.filter(
                    Supplier.supplier_type.in_(['gold', 'gold_material', 'material'])
                )
            elif supplier_type == 'product':
                query = query.filter(Supplier.supplier_type == 'product')
            else:
                query = query.filter(Supplier.supplier_type == supplier_type)

        suppliers = query.order_by(Supplier.name).all()

        suppliers_data = []
        for supplier in suppliers:
            # 基础信息
            supplier_info = {
                'id': supplier.id,
                'name': supplier.name,
                'supplier_type': supplier.supplier_type
            }

            # 根据格式类型添加不同的信息
            if format_type in ['standard', 'detailed']:
                supplier_info.update({
                    'contact_person': supplier.contact_person,
                    'phone': supplier.phone
                })

            if format_type == 'detailed':
                supplier_info.update({
                    'address': supplier.address,
                    'email': supplier.email,
                    'code': supplier.code,
                    'created_at': supplier.created_at.isoformat() if supplier.created_at else None
                })

            # 如果需要包含余额信息，从供应商款料明细计算
            if include_balances:
                # TODO: 这里将从供应商款料明细表计算，暂时使用原字段
                supplier_info.update({
                    'owed_gold': float(supplier.owed_gold or 0),
                    'deposit_gold': float(supplier.deposit_gold or 0),
                    'owed_amount': float(supplier.owed_amount or 0),
                    'deposit_amount': float(supplier.deposit_amount or 0)
                })

            suppliers_data.append(supplier_info)

        return jsonify({
            'status': 'success',
            'data': suppliers_data
        })
    except Exception as e:
        app.logger.error(f"获取供应商列表失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取供应商列表失败: {str(e)}'
        }), 500

@app.route('/api/suppliers/gold', methods=['GET'])
@login_required
def api_suppliers_gold():
    """获取金料供应商API"""
    try:
        suppliers = Supplier.query.filter(
            Supplier.is_active == True,
            Supplier.supplier_type.in_(['gold', 'gold_material', 'material'])
        ).order_by(Supplier.name).all()

        suppliers_data = []
        for supplier in suppliers:
            suppliers_data.append({
                'id': supplier.id,
                'name': supplier.name,
                'supplier_type': supplier.supplier_type
            })

        return jsonify({
            'status': 'success',
            'suppliers': suppliers_data
        })
    except Exception as e:
        app.logger.error(f"获取金料供应商失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取金料供应商失败: {str(e)}'
        }), 500

@app.route('/api/suppliers/product', methods=['GET'])
@login_required
def api_suppliers_product():
    """获取产品供应商API"""
    try:
        suppliers = Supplier.query.filter(
            Supplier.is_active == True,
            Supplier.supplier_type == 'product'
        ).order_by(Supplier.name).all()

        suppliers_data = []
        for supplier in suppliers:
            suppliers_data.append({
                'id': supplier.id,
                'name': supplier.name,
                'supplier_type': supplier.supplier_type,
                'contact_person': supplier.contact_person,
                'phone': supplier.phone
            })

        return jsonify({
            'status': 'success',
            'suppliers': suppliers_data
        })
    except Exception as e:
        app.logger.error(f"获取产品供应商失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'获取产品供应商失败: {str(e)}'
        }), 500

# 在文件末尾，main函数前
def register_report_routes():
    """注册报表相关路由"""
    try:
        # 导入路由模块 - 在app已经被创建后导入，避免循环导入问题
        from routes.report_routes import register_routes
        register_routes(app)
        app.logger.info("成功注册报表API路由")
    except Exception as e:
        app.logger.error(f"注册报表API路由模块失败: {str(e)}")
        traceback.print_exc()

# 订单链管理API
@app.route('/api/order_chain/<int:customer_id>')
@login_required
def api_get_order_chain(customer_id):
    """获取客户的订单链信息"""
    try:
        from order_cascade_service import OrderCascadeService
        chain_info = OrderCascadeService.get_order_chain_info(customer_id, None)
        return jsonify(chain_info)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/validate_order_chain/<int:customer_id>')
@login_required
def api_validate_order_chain(customer_id):
    """验证客户订单链的一致性"""
    try:
        from order_cascade_service import OrderCascadeService
        validation_result = OrderCascadeService.validate_order_chain_consistency(customer_id)
        return jsonify(validation_result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/fix_order_chain/<int:customer_id>', methods=['POST'])
@login_required
def api_fix_order_chain(customer_id):
    """修复客户订单链的一致性"""
    try:
        # 获取客户的最新状态
        customer = Customer.query.get(customer_id)
        if not customer:
            return jsonify({"success": False, "message": "客户不存在"}), 404

        # 获取客户的第一个订单，从它开始重新计算整个链
        first_order = Order.query.filter(
            Order.customer_id == customer_id
        ).order_by(Order.created_at.asc()).first()

        if not first_order:
            return jsonify({"success": False, "message": "客户没有订单"}), 400

        # 从第一个订单开始级联更新
        from order_cascade_service import OrderCascadeService
        success = OrderCascadeService.update_subsequent_orders_previous_values(
            customer_id, first_order.id,
            first_order.total_owed_gold or 0,
            first_order.total_deposit_gold or 0
        )

        if success:
            return jsonify({"success": True, "message": "订单链修复成功"})
        else:
            return jsonify({"success": False, "message": "修复过程中发生错误"}), 500

    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

# 供应商旧料余额API
@app.route('/api/suppliers/<int:supplier_id>/material_balances')
@login_required
def api_supplier_material_balances(supplier_id):
    """获取供应商的旧料余额数据"""
    try:
        from models import SupplierMaterialBalance
        balances = SupplierMaterialBalance.query.filter_by(supplier_id=supplier_id).all()

        balance_data = []
        for balance in balances:
            balance_data.append({
                'material_name': balance.material_name,
                'owed_weight': balance.owed_weight,
                'stored_weight': balance.stored_weight
            })

        return jsonify({
            'status': 'success',
            'data': balance_data
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500



# 基本信息中心API路由
@app.route('/api/data_management/products')
@login_required
def api_data_management_products():
    """获取产品管理页面内容"""
    try:
        products = Product.query.filter_by(is_active=True).order_by(Product.code).all()

        # 构建HTML内容
        html_content = '''
                <table class="simple-table">
                    <thead>
                        <tr>
                            <th>产品编号</th>
                            <th>产品名称</th>
                            <th>分类</th>
                            <th>批发工费</th>
                            <th>批发精品费</th>
                            <th>零售工费</th>
                            <th>零售精品费</th>
                            <th>初始库存</th>
                            <th>创建日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>'''

        for product in products:
            # 格式化费用显示（只显示非零值）
            wholesale_labor = f"{product.wholesale_labor_fee:.2f}" if product.wholesale_labor_fee and product.wholesale_labor_fee != 0 else ""
            wholesale_premium = f"{product.wholesale_premium_fee:.2f}" if product.wholesale_premium_fee and product.wholesale_premium_fee != 0 else ""
            retail_labor = f"{product.retail_labor_fee:.2f}" if product.retail_labor_fee and product.retail_labor_fee != 0 else ""
            retail_premium = f"{product.retail_premium_fee:.2f}" if product.retail_premium_fee and product.retail_premium_fee != 0 else ""

            # 格式化初始库存
            initial_stock = f"{product.initial_stock:.2f}" if product.initial_stock else "0.00"

            # 格式化创建日期
            created_date = product.created_at.strftime('%Y-%m-%d') if product.created_at else '-'

            html_content += f'''
                        <tr>
                            <td>{product.code or ''}</td>
                            <td>{product.name}</td>
                            <td>{product.category or ''}</td>
                            <td>{wholesale_labor}</td>
                            <td>{wholesale_premium}</td>
                            <td>{retail_labor}</td>
                            <td>{retail_premium}</td>
                            <td>{initial_stock}</td>
                            <td>{created_date}</td>
                            <td>
                                <button class="simple-btn simple-btn-primary simple-btn-sm btn-edit" data-id="{product.id}">编辑</button>
                                <button class="simple-btn simple-btn-danger simple-btn-sm" onclick="deleteProduct({product.id})">删除</button>
                            </td>
                        </tr>'''

        html_content += '''
                    </tbody>
                </table>
                <div style="height: 30px;"></div><!-- 底部空间，确保最后一行可见 -->'''

        return html_content

    except Exception as e:
        app.logger.error(f"获取产品管理内容失败: {str(e)}")
        return f'<div class="alert alert-danger">加载失败: {str(e)}</div>'

@app.route('/api/data_management/customers')
@login_required
def api_data_management_customers():
    """获取客户管理页面内容"""
    try:
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.code).all()

        # 构建HTML内容 - 与客户管理页面一致
        html_content = '''
                <table class="simple-table">
                    <thead>
                        <tr>
                            <th>客户名称</th>
                            <th>客户类型</th>
                            <th>联系方式</th>
                            <th>欠料克重（黄金）</th>
                            <th>存料克重（黄金）</th>
                            <th>欠款金额</th>
                            <th>存款金额</th>
                            <th>添加日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>'''

        for customer in customers:
            # 格式化日期
            created_date = customer.created_at.strftime('%Y-%m-%d') if customer.created_at else ''

            html_content += f'''
                        <tr>
                            <td>{customer.name}</td>
                            <td>{'批发' if customer.customer_type == 'wholesale' else '零售'}</td>
                            <td>
                                <div>电话: {customer.phone or ''}</div>
                                <div>地址: {customer.address or ''}</div>
                            </td>
                            <td>{customer.owed_gold or 0:.2f}</td>
                            <td>{customer.deposit_gold or 0:.2f}</td>
                            <td>{customer.balance or 0:.2f}</td>
                            <td>{customer.deposit_amount or 0:.2f}</td>
                            <td>{created_date}</td>
                            <td>
                                <button class="simple-btn simple-btn-primary simple-btn-sm btn-edit" data-id="{customer.id}">编辑</button>
                                <button class="simple-btn simple-btn-danger simple-btn-sm" onclick="deleteCustomer({customer.id}, '{customer.name}')">删除</button>
                            </td>
                        </tr>'''

        html_content += '''
                    </tbody>
                </table>
                <div style="height: 30px;"></div><!-- 底部空间，确保最后一行可见 -->'''

        return html_content

    except Exception as e:
        app.logger.error(f"获取客户管理内容失败: {str(e)}")
        return f'<div class="alert alert-danger">加载失败: {str(e)}</div>'

# 注册所有路由
register_report_routes()

@app.route('/api/data_management/suppliers')
@login_required
def api_data_management_suppliers():
    """获取供应商管理页面内容"""
    try:
        suppliers = Supplier.query.filter_by(is_active=True).order_by(Supplier.code).all()

        # 构建HTML内容
        html_content = '''
                <table class="simple-table">
                    <thead>
                        <tr>
                            <th>供应商名称</th>
                            <th>供应商类别</th>
                            <th>联系方式</th>
                            <th>欠料克重</th>
                            <th>存料克重</th>
                            <th>欠款金额</th>
                            <th>存款金额</th>
                            <th>添加日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>'''

        for supplier in suppliers:
            html_content += f'''
                        <tr>
                            <td>{supplier.name}</td>
                            <td>{'产品供应商' if supplier.supplier_type == 'product' else '金料供应商'}</td>
                            <td>
                                <div>联系人: {supplier.contact_person or ''}</div>
                                <div>电话: {supplier.phone or ''}</div>
                                <div>地址: {supplier.address or ''}</div>
                            </td>
                            <td>{"{:.2f}".format(supplier.owed_gold or 0)}</td>
                            <td>{"{:.2f}".format(supplier.deposit_gold or 0)}</td>
                            <td>{"{:.2f}".format(supplier.owed_amount or 0)}</td>
                            <td>{"{:.2f}".format(supplier.deposit_amount or 0)}</td>
                            <td>{supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else ''}</td>
                            <td>
                                <button class="simple-btn simple-btn-primary simple-btn-sm btn-edit" data-id="{supplier.id}">编辑</button>
                                <button class="simple-btn simple-btn-danger simple-btn-sm" onclick="deleteSupplier({supplier.id})">删除</button>
                            </td>
                        </tr>'''

        html_content += '''
                    </tbody>
                </table>
                <div style="height: 30px;"></div><!-- 底部空间，确保最后一行可见 -->'''

        return html_content

    except Exception as e:
        app.logger.error(f"获取供应商管理内容失败: {str(e)}")
        return f'<div class="alert alert-danger">加载失败: {str(e)}</div>'

@app.route('/api/data_management/materials')
@login_required
def api_data_management_materials():
    """获取旧料管理页面内容"""
    try:
        from routes.report_routes import calculate_old_material_inventory_at_date
        from datetime import date

        materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code).all()

        # 构建HTML内容
        html_content = '''
                <table class="simple-table">
                    <thead>
                        <tr>
                            <th>旧料编号</th>
                            <th>旧料名称</th>
                            <th>料部初始库存</th>
                            <th>当前料部库存</th>
                            <th>创建日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>'''

        if materials:
            for material in materials:
                # 计算当前库存
                current_stock = calculate_old_material_inventory_at_date(material, date.today())
                stock_rounded = round(current_stock, 2)

                # 根据库存值设置样式
                if stock_rounded > 0:
                    stock_display = f'<span class="badge bg-success">{stock_rounded}</span>'
                elif stock_rounded == 0:
                    stock_display = f'<span class="badge bg-secondary">{stock_rounded}</span>'
                else:
                    stock_display = f'<span class="badge bg-warning text-dark">{stock_rounded}</span>'

                created_date = material.created_at.strftime('%Y-%m-%d') if material.created_at else '-'

                html_content += f'''
                        <tr>
                            <td>{material.code}</td>
                            <td>{material.name}</td>
                            <td>{"{:.2f}".format(material.initial_stock or 0)}</td>
                            <td>{stock_display}</td>
                            <td>{created_date}</td>
                            <td>
                                <button class="simple-btn simple-btn-danger simple-btn-sm" onclick="deleteMaterial({material.id})">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>'''
        else:
            html_content += '''
                        <tr>
                            <td colspan="6">暂无旧料数据</td>
                        </tr>'''

        html_content += '''
                    </tbody>
                </table>
                <div style="height: 30px;"></div><!-- 底部空间，确保最后一行可见 -->'''

        return html_content

    except Exception as e:
        app.logger.error(f"获取旧料管理内容失败: {str(e)}")
        return f'<div class="alert alert-danger">加载失败: {str(e)}</div>'

@app.route('/api/data_management/employees')
@login_required
def api_data_management_employees():
    """获取员工管理页面内容"""
    try:
        # 获取所有员工（包括离职的），按姓名排序
        employees = Employee.query.order_by(Employee.name).all()

        # 构建HTML内容，对照独立页面的表格结构
        html_content = '''
                <table class="simple-table">
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>性别</th>
                            <th>联系方式</th>
                            <th>职位</th>
                            <th>工资</th>
                            <th>入职日期</th>
                            <th>离职日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>'''

        for employee in employees:
            hire_date_str = employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else ''
            leave_date_str = employee.leave_date.strftime('%Y-%m-%d') if employee.leave_date else ''

            # 联系方式格式化（电话和地址）
            contact_info = f"联系电话：{employee.phone or ''}<br>地址：{employee.address or ''}"

            # 状态样式
            status_class = 'status-active' if employee.is_active else 'status-inactive'
            status_text = '在职' if employee.is_active else '离职'

            html_content += f'''
                        <tr>
                            <td>{employee.name}</td>
                            <td>{'男' if employee.gender == 'male' else '女'}</td>
                            <td>{contact_info}</td>
                            <td>{employee.position or ''}</td>
                            <td>{employee.salary or 0}</td>
                            <td>{hire_date_str}</td>
                            <td>{leave_date_str}</td>
                            <td class="{status_class}">{status_text}</td>
                            <td>
                                <button class="simple-btn simple-btn-primary simple-btn-sm btn-edit" data-id="{employee.id}">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="simple-btn simple-btn-danger simple-btn-sm" onclick="deleteEmployee({employee.id})">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>'''

        html_content += '''
                    </tbody>
                </table>
                <div style="height: 30px;"></div><!-- 底部空间，确保最后一行可见 -->'''

        return html_content

    except Exception as e:
        app.logger.error(f"获取员工管理内容失败: {str(e)}")
        return f'<div class="alert alert-danger">加载失败: {str(e)}</div>'

@app.route('/api/data_management/accounts')
@login_required
def api_data_management_accounts():
    """获取账户管理页面内容"""
    try:
        accounts = Account.query.filter_by(is_active=True).order_by(Account.name).all()

        # 使用账户余额概览的计算方法获取当前余额
        from datetime import datetime
        today = datetime.now().strftime('%Y-%m-%d')
        account_balances = calculate_period_account_balances(today, today, accounts)

        # 创建账户ID到期末余额的映射
        balance_map = {acc_data['id']: acc_data['final_balance'] for acc_data in account_balances}

        # 构建HTML内容
        html_content = '''
                <table class="simple-table">
                    <thead>
                        <tr>
                            <th>账户名称</th>
                            <th>银行名称</th>
                            <th>账号</th>
                            <th>初始余额</th>
                            <th>当前余额</th>
                            <th>添加日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>'''

        for account in accounts:
            # 格式化添加日期
            created_date = account.created_at.strftime('%Y-%m-%d') if account.created_at else '-'
            # 使用计算出的当前余额
            current_balance = balance_map.get(account.id, 0)

            html_content += f'''
                        <tr>
                            <td>{account.name}</td>
                            <td>{account.bank_name or '-'}</td>
                            <td>{account.account_number or '-'}</td>
                            <td>{round(account.initial_balance or 0, 2)}</td>
                            <td>{round(current_balance, 2)}</td>
                            <td>{created_date}</td>
                            <td>
                                <button class="simple-btn simple-btn-primary simple-btn-sm btn-edit" data-id="{account.id}">
                                    <i class="bi bi-pencil"></i>
                                </button>'''

            # 只有非默认账户才显示删除按钮
            if account.name != '默认账户':
                html_content += f'''
                                <button class="simple-btn simple-btn-danger simple-btn-sm" onclick="deleteAccount({account.id})">
                                    <i class="bi bi-trash"></i>
                                </button>'''

            html_content += '''
                            </td>
                        </tr>'''

        html_content += '''
                    </tbody>
                </table>
                <div style="height: 30px;"></div><!-- 底部空间，确保最后一行可见 -->'''

        return html_content

    except Exception as e:
        app.logger.error(f"获取账户管理内容失败: {str(e)}")
        return f'<div class="alert alert-danger">加载失败: {str(e)}</div>'

@app.route('/api/suppliers/search')
@login_required
def api_suppliers_search():
    """供应商搜索API"""
    try:
        query = request.args.get('q', '').strip()

        if query == 'all':
            # 返回所有供应商
            suppliers = Supplier.query.order_by(Supplier.name).all()
        else:
            # 根据查询条件搜索供应商
            suppliers = Supplier.query.filter(
                db.or_(
                    Supplier.name.contains(query),
                    Supplier.phone.contains(query)
                )
            ).order_by(Supplier.name).limit(20).all()

        suppliers_data = []
        for supplier in suppliers:
            suppliers_data.append({
                'id': supplier.id,
                'name': supplier.name,
                'phone': supplier.phone or '',
                'pinyin': supplier.pinyin or '',
                'pinyin_initials': supplier.pinyin_initials or ''
            })

        return jsonify({
            'success': True,
            'suppliers': suppliers_data
        })
    except Exception as e:
        app.logger.error(f"供应商搜索失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"搜索失败: {str(e)}"
        })







@app.route('/api/orders/get_id_by_no')
@login_required
def api_get_order_id_by_no():
    """通过订单编号获取订单ID"""
    order_no = request.args.get('order_no')
    if not order_no:
        return jsonify({'success': False, 'message': '订单编号不能为空'})

    order = Order.query.filter_by(order_no=order_no).first()
    if order:
        return jsonify({'success': True, 'order_id': order.id})
    else:
        return jsonify({'success': False, 'message': '未找到对应的订单'})

@app.route('/api/purchases/get_id_by_no')
@login_required
def api_get_purchase_id_by_no():
    """通过采购单编号获取采购单ID"""
    purchase_no = request.args.get('purchase_no')
    if not purchase_no:
        return jsonify({'success': False, 'message': '采购单编号不能为空'})

    purchase = Purchase.query.filter_by(purchase_no=purchase_no).first()
    if purchase:
        return jsonify({'success': True, 'purchase_id': purchase.id})
    else:
        return jsonify({'success': False, 'message': '未找到对应的采购单'})

@app.route('/api/supplier_transactions/get_id_by_no')
@login_required
def api_get_supplier_transaction_id_by_no():
    """通过供应商往来编号获取供应商往来ID"""
    transaction_no = request.args.get('transaction_no')
    if not transaction_no:
        return jsonify({'success': False, 'message': '供应商往来编号不能为空'})

    transaction = SupplierTransaction.query.filter_by(transaction_no=transaction_no).first()
    if transaction:
        return jsonify({'success': True, 'transaction_id': transaction.id})
    else:
        return jsonify({'success': False, 'message': '未找到对应的供应商往来记录'})

@app.route('/api/material_adjustments/get_id_by_no')
@login_required
def api_get_material_adjustment_id_by_no():
    """通过货料调整编号获取货料调整ID"""
    adjustment_no = request.args.get('adjustment_no')
    if not adjustment_no:
        return jsonify({'success': False, 'message': '货料调整编号不能为空'})

    adjustment = MaterialAdjustment.query.filter_by(adjustment_no=adjustment_no).first()
    if adjustment:
        return jsonify({'success': True, 'adjustment_id': adjustment.id})
    else:
        return jsonify({'success': False, 'message': '未找到对应的货料调整记录'})

# 产品管理表单API
@app.route('/api/data_management/products/add_form')
@login_required
def api_data_management_products_add_form():
    """获取产品添加表单"""
    try:
        from flask_wtf.csrf import generate_csrf
        csrf_token = generate_csrf()

        form_html = f'''
        <form id="product-add-form" class="modal-form-container">
            <input type="hidden" name="csrf_token" value="{csrf_token}"/>
            <div class="modal-form-header">
                <h4 class="modal-form-title">添加产品</h4>
            </div>
            <div class="modal-form-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <label for="code" class="form-label small">产品编号</label>
                            <input type="text" class="form-control form-control-sm" id="code" name="code">
                        </div>

                        <div class="mb-2">
                            <label for="name" class="form-label small">产品名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm" id="name" name="name" required>
                        </div>

                        <div class="mb-2">
                            <label for="category" class="form-label small">分类 <span class="text-danger">*</span></label>
                            <select class="form-select form-control-sm" id="category" name="category" required>
                                <option value="">请选择分类</option>
                                <option value="黄金" selected>黄金</option>
                                <option value="铂金">铂金</option>
                                <option value="18K金">18K金</option>
                                <option value="22K金">22K金</option>
                                <option value="白银">白银</option>
                                <option value="钻石">钻石</option>
                                <option value="玉石">玉石</option>
                            </select>
                        </div>

                        <div class="mb-2">
                            <label for="unit" class="form-label small">单位</label>
                            <select class="form-select form-control-sm" id="unit" name="unit">
                                <option value="">请选择单位</option>
                                <option value="克" selected>克</option>
                                <option value="件">件</option>
                            </select>
                        </div>

                        <div class="mb-2">
                            <label for="initial_stock" class="form-label small">初始库存 <span class="text-danger">*</span></label>
                            <input type="number" step="0.001" class="form-control form-control-sm" id="initial_stock" name="initial_stock" value="0" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-2">
                            <label for="wholesale_labor_fee" class="form-label small">批发工费</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="wholesale_labor_fee" name="wholesale_labor_fee" value="0">
                        </div>

                        <div class="mb-2">
                            <label for="wholesale_premium_fee" class="form-label small">批发精品费</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="wholesale_premium_fee" name="wholesale_premium_fee" value="0">
                        </div>

                        <div class="mb-2">
                            <label for="retail_labor_fee" class="form-label small">零售工费</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="retail_labor_fee" name="retail_labor_fee" value="0">
                        </div>

                        <div class="mb-2">
                            <label for="retail_premium_fee" class="form-label small">零售精品费</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="retail_premium_fee" name="retail_premium_fee" value="0">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-form-footer text-end">
                <button type="submit" class="btn btn-primary">添加产品</button>
                <button type="button" class="btn btn-secondary ms-2" data-bs-dismiss="modal">取消</button>
            </div>
        </form>

        <style>
        /* 统一字体加粗加大 */
        .modal-form-container .form-label, .modal-form-container .form-control, .modal-form-container .form-select, .modal-form-container .btn {{
            font-weight: bold !important;
            font-size: 1.0em !important;
        }}
        .modal-form-container .form-label.small {{
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }}
        </style>
        '''
        return form_html
    except Exception as e:
        app.logger.error(f"获取产品添加表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/products/add', methods=['POST'])
@login_required
def api_data_management_products_add():
    """处理产品添加"""
    try:
        name = request.form.get('name', '').strip()
        category = request.form.get('category', '').strip()

        if not name or not category:
            return jsonify({
                'success': False,
                'message': '产品名称和类别不能为空'
            })

        # 获取初始库存
        initial_stock = float(request.form.get('initial_stock', 0) or 0)

        # 创建新产品
        product = Product(
            code=request.form.get('code', '').strip(),
            name=name,
            category=category,
            unit=request.form.get('unit', '').strip(),
            stock=initial_stock,  # 设置当前库存等于初始库存
            initial_stock=initial_stock,  # 设置初始库存
            wholesale_labor_fee=float(request.form.get('wholesale_labor_fee', 0) or 0),
            wholesale_premium_fee=float(request.form.get('wholesale_premium_fee', 0) or 0),
            retail_labor_fee=float(request.form.get('retail_labor_fee', 0) or 0),
            retail_premium_fee=float(request.form.get('retail_premium_fee', 0) or 0),
            is_active=True
        )

        db.session.add(product)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '产品添加成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"添加产品失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        })

@app.route('/api/data_management/products/edit_form/<int:product_id>')
@login_required
def api_data_management_products_edit_form(product_id):
    """获取产品编辑表单"""
    try:
        product = Product.query.get_or_404(product_id)

        # 构建分类选项
        category_options = []
        categories = ['黄金', '铂金', '18K金', '22K金', '白银', '钻石', '玉石']
        for cat in categories:
            selected = 'selected' if product.category == cat else ''
            category_options.append(f'<option value="{cat}" {selected}>{cat}</option>')

        # 构建单位选项
        unit_options = []
        units = ['克', '件']
        for unit in units:
            selected = 'selected' if product.unit == unit else ''
            unit_options.append(f'<option value="{unit}" {selected}>{unit}</option>')

        # 将选项列表转换为字符串
        category_options_str = ''.join(category_options)
        unit_options_str = ''.join(unit_options)

        from flask_wtf.csrf import generate_csrf
        csrf_token = generate_csrf()

        form_html = f'''
        <form id="product-edit-form" class="modal-form-container">
            <input type="hidden" name="csrf_token" value="{csrf_token}"/>
            <div class="modal-form-header">
                <h4 class="modal-form-title">编辑产品</h4>
            </div>
            <div class="modal-form-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <label for="code" class="form-label small">产品编号 <span class="text-muted">(不可修改)</span></label>
                            <input type="text" class="form-control form-control-sm" id="code" name="code" value="{product.code or ''}" readonly style="background-color: #f8f9fa;">
                        </div>

                        <div class="mb-2">
                            <label for="name" class="form-label small">产品名称 <span class="text-muted">(不可修改)</span></label>
                            <input type="text" class="form-control form-control-sm" id="name" name="name" value="{product.name}" readonly style="background-color: #f8f9fa;">
                        </div>

                        <div class="mb-2">
                            <label for="category" class="form-label small">分类 <span class="text-muted">(不可修改)</span></label>
                            <input type="text" class="form-control form-control-sm" id="category" name="category" value="{product.category or ''}" readonly style="background-color: #f8f9fa;">
                        </div>

                        <div class="mb-2">
                            <label for="unit" class="form-label small">单位 <span class="text-muted">(不可修改)</span></label>
                            <input type="text" class="form-control form-control-sm" id="unit" name="unit" value="{product.unit or ''}" readonly style="background-color: #f8f9fa;">
                        </div>

                        <div class="mb-2">
                            <label for="initial_stock" class="form-label small">初始库存 <span class="text-muted">(不可修改)</span></label>
                            <input type="number" step="0.001" class="form-control form-control-sm" id="initial_stock" name="initial_stock" value="{product.initial_stock or 0}" readonly style="background-color: #f8f9fa;">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-2">
                            <label for="wholesale_labor_fee" class="form-label small">批发工费</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="wholesale_labor_fee" name="wholesale_labor_fee" value="{product.wholesale_labor_fee or 0}">
                        </div>

                        <div class="mb-2">
                            <label for="wholesale_premium_fee" class="form-label small">批发精品费</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="wholesale_premium_fee" name="wholesale_premium_fee" value="{product.wholesale_premium_fee or 0}">
                        </div>

                        <div class="mb-2">
                            <label for="retail_labor_fee" class="form-label small">零售工费</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="retail_labor_fee" name="retail_labor_fee" value="{product.retail_labor_fee or 0}">
                        </div>

                        <div class="mb-2">
                            <label for="retail_premium_fee" class="form-label small">零售精品费</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="retail_premium_fee" name="retail_premium_fee" value="{product.retail_premium_fee or 0}">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-form-footer d-flex justify-content-end">
                <button type="submit" class="btn btn-primary btn-sm">保存更改</button>
                <button type="button" class="btn btn-secondary btn-sm ms-2" data-bs-dismiss="modal">取消</button>
            </div>
        </form>

        <style>
        /* 统一字体加粗加大 */
        .modal-form-container .form-label, .modal-form-container .form-control, .modal-form-container .form-select, .modal-form-container .btn {{
            font-weight: bold !important;
            font-size: 1.0em !important;
        }}
        .modal-form-container .form-label.small {{
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }}
        /* 减小模态框宽度 */
        .modal-form-container {{
            max-width: 700px;
            margin: 0 auto;
        }}
        </style>
        '''
        return form_html
    except Exception as e:
        app.logger.error(f"获取产品编辑表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/products/edit/<int:product_id>', methods=['POST'])
@login_required
def api_data_management_products_edit(product_id):
    """处理产品编辑"""
    try:
        product = Product.query.get_or_404(product_id)

        # 只更新可编辑的字段（费用字段）
        # 产品编号、名称、分类、单位和初始库存不可修改
        product.wholesale_labor_fee = float(request.form.get('wholesale_labor_fee', 0) or 0)
        product.wholesale_premium_fee = float(request.form.get('wholesale_premium_fee', 0) or 0)
        product.retail_labor_fee = float(request.form.get('retail_labor_fee', 0) or 0)
        product.retail_premium_fee = float(request.form.get('retail_premium_fee', 0) or 0)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '产品更新成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"更新产品失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        })

@app.route('/api/data_management/products/delete/<int:product_id>', methods=['POST'])
@login_required
def api_data_management_products_delete(product_id):
    """删除产品API"""
    try:
        product = Product.query.get_or_404(product_id)

        # 检查是否有关联的订单项
        has_order_items = OrderItem.query.filter_by(product_id=product_id).first() is not None
        if has_order_items:
            return jsonify({
                'success': False,
                'message': '该产品已有关联的订单，不能删除'
            })

        # 检查是否有关联的采购单据
        has_purchase_items = PurchaseItem.query.filter_by(product_id=product_id).first() is not None
        if has_purchase_items:
            return jsonify({
                'success': False,
                'message': '该产品已有关联的采购单据，不能删除'
            })

        # 检查是否有关联的货料调整记录
        has_material_adjustments = MaterialAdjustmentItem.query.filter_by(
            target_type='product', target_name=product.name
        ).first() is not None
        if has_material_adjustments:
            return jsonify({
                'success': False,
                'message': '该产品已有关联的货料调整记录，不能删除'
            })

        # 如果没有任何关联记录，则可以删除
        db.session.delete(product)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '产品删除成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除产品失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })

# 客户管理表单API
@app.route('/api/data_management/customers/add_form')
@login_required
def api_data_management_customers_add_form():
    """获取客户添加表单"""
    try:
        # 获取所有旧料类型
        materials = OldMaterial.query.filter_by(is_active=True).all()

        form_html = f'''
        <form id="customer-add-form" class="modal-form-container" style="width: 100%; max-height: 45vh; overflow-y: auto;">
            <div class="modal-form-header">
                <h4 class="modal-form-title">添加客户</h4>
            </div>
            <div class="modal-form-body">
                <!-- 基本信息行 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="name" class="form-label">客户名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="col-md-3">
                        <label for="customer_type" class="form-label">客户类型</label>
                        <select class="form-select" id="customer_type" name="customer_type">
                            <option value="retail">零售</option>
                            <option value="wholesale">批发</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="phone" class="form-label">联系电话</label>
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="col-md-3">
                        <label for="address" class="form-label">地址</label>
                        <input type="text" class="form-control" id="address" name="address">
                    </div>
                </div>

                <!-- 旧料初始值表格 -->
                <div class="mb-3">
                    <h6 class="mb-2">旧料明细（<span class="text-danger fw-bold">初始值</span>）<span class="text-muted fw-bold ms-1">
                        <i class="fas fa-info-circle"></i>
                        设置客户的初始旧料余额，创建后不可修改。
                    </span></h6>
                    <table class="simple-table" id="material-balance-table">
                        <thead>
                            <tr>
                                {"".join([f'<th colspan="2">{material.name}</th>' for material in materials])}
                                <th colspan="2">款项</th>
                            </tr>
                            <tr>
                                {"".join(['<th>欠料克重</th><th>存料克重</th>' for _ in materials])}
                                <th>欠款金额</th><th>存款金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                {"".join([
                                    f'<td><input type="number" step="0.001" name="owed_gold" value="0" class="form-control form-control-sm"></td><td><input type="number" step="0.001" name="deposit_gold" value="0" class="form-control form-control-sm"></td>'
                                    if material.name == '旧料足金'
                                    else f'<td><input type="number" step="0.001" name="material_{material.name}_owed" value="0" class="form-control form-control-sm"></td><td><input type="number" step="0.001" name="material_{material.name}_stored" value="0" class="form-control form-control-sm"></td>'
                                    for material in materials
                                ])}
                                <td><input type="number" step="0.01" name="balance" value="0" class="form-control form-control-sm"></td>
                                <td><input type="number" step="0.01" name="deposit_amount" value="0" class="form-control form-control-sm"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-form-footer" style="text-align: right;">
                <button type="submit" class="btn btn-primary btn-sm">添加客户</button>
                <button type="button" class="btn btn-secondary btn-sm ms-2" data-bs-dismiss="modal">取消</button>
            </div>
        </form>

        <style>
        /* 统一字体加粗加大 */
        .modal-form-container .form-label, .modal-form-container .form-control, .modal-form-container .form-select, .modal-form-container .btn {{
            font-weight: bold !important;
            font-size: 1.0em !important;
        }}



        .modal-form-container .simple-table {{
            width: 100%;
            margin: 0;
            border-collapse: collapse;
        }}

        .modal-form-container .simple-table thead th {{
            background-color: #495057;
            color: white;
            font-weight: bold;
            font-size: 14px;
            padding: 8px 4px;
            text-align: center;
            border: 1px solid #6c757d;
            position: sticky;
            top: 0;
            z-index: 10;
        }}

        .modal-form-container .simple-table td {{
            text-align: center;
            vertical-align: middle;
            padding: 4px;
            border: 1px solid #dee2e6;
        }}

        .modal-form-container .simple-table input {{
            width: 80px;
            text-align: center;
            font-size: 0.8rem;
            padding: 2px 4px;
        }}
        </style>
        '''
        return form_html
    except Exception as e:
        app.logger.error(f"获取客户添加表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/customers/add', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_customers_add():
    """处理客户添加"""
    try:
        name = request.form.get('name', '').strip()

        if not name:
            return jsonify({
                'success': False,
                'message': '客户名称不能为空'
            })

        # 生成客户编号 - 确保唯一性
        new_code = None
        max_attempts = 1000  # 防止无限循环

        for attempt in range(max_attempts):
            # 获取最大的客户编号
            last_customer = Customer.query.filter(Customer.code.like('C%')).order_by(Customer.code.desc()).first()

            if last_customer and last_customer.code:
                try:
                    # 提取编号数字部分
                    code_number = int(last_customer.code[1:])
                    candidate_code = f'C{code_number + 1:04d}'
                except (ValueError, IndexError):
                    candidate_code = 'C0001'
            else:
                candidate_code = 'C0001'

            # 检查编号是否已存在
            if not Customer.query.filter_by(code=candidate_code).first():
                new_code = candidate_code
                break

        if not new_code:
            return jsonify({
                'success': False,
                'message': '无法生成唯一的客户编号，请联系管理员'
            })

        # 检查客户名称是否重复（只检查活跃客户）
        if Customer.query.filter_by(name=name, is_active=True).first():
            return jsonify({
                'success': False,
                'message': '客户名称已存在，请使用其他名称'
            })

        # 获取初始款项金额
        initial_owed_amount = float(request.form.get('balance', 0))
        initial_deposit_amount = float(request.form.get('deposit_amount', 0))

        # 创建新客户（包含初始款项字段）
        customer = Customer(
            code=new_code,
            name=name,
            customer_type=request.form.get('customer_type', 'retail'),
            phone=request.form.get('phone', '').strip(),
            address=request.form.get('address', '').strip(),
            initial_owed_amount=initial_owed_amount,
            initial_deposit_amount=initial_deposit_amount
        )

        # 更新拼音字段
        try:
            from pypinyin import lazy_pinyin, Style

            # 获取全拼
            pinyin_list = lazy_pinyin(customer.name)
            customer.pinyin = ''.join(pinyin_list)

            # 获取首字母
            pinyin_initials = lazy_pinyin(customer.name, style=Style.FIRST_LETTER)
            customer.pinyin_initials = ''.join(pinyin_initials)
        except Exception as e:
            print(f"更新客户拼音字段时出错: {e}")

        db.session.add(customer)
        db.session.flush()  # 获取客户ID

        # 处理所有旧料的初始值（包括旧料足金）
        from models import CustomerMaterialBalance, OldMaterial

        # 获取所有旧料
        all_materials = OldMaterial.query.filter_by(is_active=True).all()

        for material in all_materials:
            # 旧料足金使用特殊字段名，其他旧料使用通用字段名
            if material.name == '旧料足金':
                owed_weight = float(request.form.get('owed_gold', 0))
                stored_weight = float(request.form.get('deposit_gold', 0))
            else:
                owed_key = f'material_{material.name}_owed'
                stored_key = f'material_{material.name}_stored'
                owed_weight = float(request.form.get(owed_key, 0))
                stored_weight = float(request.form.get(stored_key, 0))

            # 为所有旧料类型创建记录，即使值为0（用于客户款料明细显示）
            balance = CustomerMaterialBalance(
                customer_id=customer.id,
                material_name=material.name,
                owed_weight=owed_weight,
                stored_weight=stored_weight
            )
            db.session.add(balance)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '客户添加成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"添加客户失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        })

@app.route('/api/data_management/customers/edit_form/<int:customer_id>')
@login_required
def api_data_management_customers_edit_form(customer_id):
    """获取客户编辑表单 - 直接使用服务器端渲染"""
    try:
        from models import Customer, OldMaterial, CustomerMaterialBalance

        customer = Customer.query.get_or_404(customer_id)
        old_materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code.asc()).all()

        # 获取客户的旧料余额（初始值）- 从第一行数据获取
        customer_balances = CustomerMaterialBalance.query.filter_by(customer_id=customer_id).order_by(CustomerMaterialBalance.created_at.asc()).all()
        balance_dict = {balance.material_name: balance for balance in customer_balances}

        form_html = f'''
        <form id="customer-edit-form" class="modal-form-container" style="width: 100%; max-height: 45vh; overflow-y: auto;">
            <div class="modal-form-header">
                <h4 class="modal-form-title">编辑客户</h4>
            </div>
            <div class="modal-form-body">
                <!-- 基本信息行 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="name" class="form-label">客户名称 <span class="text-muted">(不可修改)</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="{customer.name}" readonly style="background-color: #f8f9fa;">
                    </div>
                    <div class="col-md-3">
                        <label for="customer_type" class="form-label">客户类型 <span class="text-muted">(不可修改)</span></label>
                        <input type="text" class="form-control" value="{'批发' if customer.customer_type == 'wholesale' else '零售'}" readonly style="background-color: #f8f9fa;">
                        <input type="hidden" name="customer_type" value="{customer.customer_type}">
                    </div>
                    <div class="col-md-3">
                        <label for="phone" class="form-label">联系电话</label>
                        <input type="text" class="form-control" id="phone" name="phone" value="{customer.phone or ''}">
                    </div>
                    <div class="col-md-3">
                        <label for="address" class="form-label">地址</label>
                        <input type="text" class="form-control" id="address" name="address" value="{customer.address or ''}">
                    </div>
                </div>

                <!-- 旧料初始值表格 -->
                <div class="mb-3">
                    <h6 class="mb-2">旧料明细（<span class="text-danger fw-bold">初始值</span>）<span class="text-muted fw-bold ms-1">
                        <i class="fas fa-info-circle"></i>
                        显示客户创建时的初始值，不可修改。当前余额请查看客户款料明细。
                    </span></h6>
                    <table class="simple-table" id="material-balance-table">
                        <thead>
                            <tr>
                                {"".join([f'<th colspan="2">{material.name}</th>' for material in old_materials])}
                                <th colspan="2">款项</th>
                            </tr>
                            <tr>
                                {"".join(['<th>欠料克重</th><th>存料克重</th>' for _ in old_materials])}
                                <th>欠款金额</th><th>存款金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                {"".join([
                                    f'<td><input type="text" value="{balance_dict.get(material.name).owed_weight if balance_dict.get(material.name) else 0}" readonly class="form-control form-control-sm" title="初始欠料克重"></td><td><input type="text" value="{balance_dict.get(material.name).stored_weight if balance_dict.get(material.name) else 0}" readonly class="form-control form-control-sm" title="初始存料克重"></td>'
                                    for material in old_materials
                                ])}
                                <td><input type="text" value="{customer.balance or 0}" readonly class="form-control form-control-sm" title="初始欠款金额"></td>
                                <td><input type="text" value="{customer.deposit_amount or 0}" readonly class="form-control form-control-sm" title="初始存款金额"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-form-footer" style="text-align: right;">
                <button type="submit" class="btn btn-primary btn-sm">保存</button>
                <button type="button" class="btn btn-secondary btn-sm ms-2" data-bs-dismiss="modal">取消</button>
            </div>
        </form>

        <style>
        /* 统一字体加粗加大 */
        .modal-form-container .form-label, .modal-form-container .form-control, .modal-form-container .form-select, .modal-form-container .btn {{
            font-weight: bold !important;
            font-size: 1.0em !important;
        }}



        .modal-form-container .simple-table {{
            width: 100%;
            margin: 0;
            border-collapse: collapse;
        }}

        .modal-form-container .simple-table thead th {{
            background-color: #495057;
            color: white;
            font-weight: bold;
            font-size: 14px;
            padding: 8px 4px;
            text-align: center;
            border: 1px solid #6c757d;
            position: sticky;
            top: 0;
            z-index: 10;
        }}

        .modal-form-container .simple-table td {{
            text-align: center;
            vertical-align: middle;
            padding: 4px;
            border: 1px solid #dee2e6;
        }}

        .modal-form-container .simple-table input {{
            width: 80px;
            text-align: center;
            font-size: 0.8rem;
            padding: 2px 4px;
            background-color: #f8f9fa;
        }}
        </style>


        '''
        return form_html
    except Exception as e:
        app.logger.error(f"获取客户编辑表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/customers/edit/<int:customer_id>', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_customers_edit(customer_id):
    """处理客户编辑"""
    try:
        customer = Customer.query.get_or_404(customer_id)

        name = request.form.get('name', '').strip()

        if not name:
            return jsonify({
                'success': False,
                'message': '客户名称不能为空'
            })

        # 更新客户基本信息（余额不允许在此处修改）
        customer.name = name
        customer.customer_type = request.form.get('customer_type', 'retail')
        customer.phone = request.form.get('phone', '').strip()
        customer.address = request.form.get('address', '').strip()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '客户更新成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"更新客户失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        })

@app.route('/api/data_management/customers/delete/<int:customer_id>', methods=['POST'])
@login_required
def api_data_management_customers_delete(customer_id):
    """处理客户删除"""
    try:
        customer = Customer.query.get_or_404(customer_id)
        customer_name = customer.name

        # 检查是否有相关的订单记录
        has_orders = Order.query.filter_by(customer_id=customer_id).first() is not None
        if has_orders:
            return jsonify({
                'success': False,
                'message': f'无法删除客户 {customer_name}，存在相关的订单记录'
            })

        # 检查是否有相关的往来记录
        from models import Transaction
        has_transactions = Transaction.query.filter_by(customer_id=customer_id).first() is not None
        if has_transactions:
            return jsonify({
                'success': False,
                'message': f'无法删除客户 {customer_name}，存在相关的往来记录'
            })

        # 删除相关的CustomerMaterialBalance记录
        from models import CustomerMaterialBalance
        CustomerMaterialBalance.query.filter_by(customer_id=customer_id).delete()

        # 硬删除客户
        db.session.delete(customer)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'客户 {customer_name} 删除成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除客户失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })

# 供应商管理表单API
@app.route('/api/data_management/suppliers/add_form')
@login_required
def api_data_management_suppliers_add_form():
    """获取供应商添加表单"""
    try:
        # 获取所有旧料类型
        materials = OldMaterial.query.filter_by(is_active=True).all()

        form_html = f'''
        <form id="supplier-add-form" class="modal-form-container">
            <div class="modal-form-header">
                <h4 class="modal-form-title">添加供应商</h4>
            </div>
            <div class="modal-form-body">
                <!-- 基本信息行 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="name" class="form-label">供应商名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="col-md-3">
                        <label for="supplier_type" class="form-label">供应商类别</label>
                        <select class="form-select" id="supplier_type" name="supplier_type">
                            <option value="product">产品供应商</option>
                            <option value="gold">金料供应商</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="contact_person" class="form-label">联系人</label>
                        <input type="text" class="form-control" id="contact_person" name="contact_person">
                    </div>
                    <div class="col-md-3">
                        <label for="phone" class="form-label">联系电话</label>
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="address" class="form-label">地址</label>
                        <input type="text" class="form-control" id="address" name="address">
                    </div>
                </div>

                <!-- 旧料初始值表格 -->
                <div class="mb-3">
                    <h6 class="mb-2">旧料明细（<span class="text-danger fw-bold">初始值</span>）<span class="text-muted fw-bold ms-1">
                        <i class="fas fa-info-circle"></i>
                        设置供应商的初始旧料余额，创建后不可修改。
                    </span></h6>
                    <table class="simple-table" id="material-balance-table">
                        <thead>
                            <tr>
                                {"".join([f'<th colspan="2">{material.name}</th>' for material in materials])}
                                <th colspan="2">款项</th>
                            </tr>
                            <tr>
                                {"".join(['<th>欠料克重</th><th>存料克重</th>' for _ in materials])}
                                <th>欠款金额</th><th>存款金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                {"".join([
                                    f'<td><input type="number" step="0.001" name="material_{material.name}_owed" value="0" class="form-control form-control-sm"></td><td><input type="number" step="0.001" name="material_{material.name}_stored" value="0" class="form-control form-control-sm"></td>'
                                    for material in materials
                                ])}
                                <td><input type="number" step="0.01" name="owed_amount" value="0" class="form-control form-control-sm"></td>
                                <td><input type="number" step="0.01" name="deposit_amount" value="0" class="form-control form-control-sm"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-form-footer">
                <div class="text-end">
                    <button type="submit" class="btn btn-primary me-2">添加供应商</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </form>

        <style>
        /* 统一字体加粗加大 */
        .modal-form-container .form-label, .modal-form-container .form-control, .modal-form-container .form-select, .modal-form-container .btn {{
            font-weight: bold !important;
            font-size: 1.0em !important;
        }}

        .modal-form-container .simple-table {{
            width: 100%;
            margin: 0;
            border-collapse: collapse;
        }}

        .modal-form-container .simple-table thead th {{
            background-color: #495057;
            color: white;
            font-weight: bold;
            font-size: 14px;
            padding: 8px 4px;
            text-align: center;
            border: 1px solid #6c757d;
            position: sticky;
            top: 0;
            z-index: 10;
        }}

        .modal-form-container .simple-table td {{
            text-align: center;
            vertical-align: middle;
            padding: 4px;
            border: 1px solid #dee2e6;
        }}

        .modal-form-container .simple-table input {{
            width: 80px;
            text-align: center;
            font-size: 0.8rem;
            padding: 2px 4px;
        }}

        /* 表格容器 */
        .modal-form-container .mb-3:last-of-type {{
            margin-bottom: 20px !important;
        }}

        /* 模态框底部按钮 */
        .modal-form-container .modal-form-footer {{
            position: absolute;
            bottom: 0;
            right: 0;
            background: white;
            border-top: 1px solid #dee2e6;
            padding: 15px;
            margin: 0;
            width: 100%;
        }}
        </style>
        '''
        return form_html
    except Exception as e:
        app.logger.error(f"获取供应商添加表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/suppliers/add', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_suppliers_add():
    """处理供应商添加"""
    try:
        name = request.form.get('name', '').strip()

        if not name:
            return jsonify({
                'success': False,
                'message': '供应商名称不能为空'
            })

        # 生成供应商编号 - 确保唯一性
        new_code = None
        max_attempts = 1000  # 防止无限循环

        for attempt in range(max_attempts):
            # 获取最大的供应商编号
            last_supplier = Supplier.query.filter(Supplier.code.like('S%')).order_by(Supplier.code.desc()).first()

            if last_supplier and last_supplier.code:
                try:
                    # 提取编号数字部分
                    code_number = int(last_supplier.code[1:])
                    candidate_code = f'S{code_number + 1:04d}'
                except (ValueError, IndexError):
                    candidate_code = 'S0001'
            else:
                candidate_code = 'S0001'

            # 检查编号是否已存在
            if not Supplier.query.filter_by(code=candidate_code).first():
                new_code = candidate_code
                break

        if not new_code:
            return jsonify({
                'success': False,
                'message': '无法生成唯一的供应商编号，请联系管理员'
            })

        # 检查供应商名称是否重复
        if Supplier.query.filter_by(name=name).first():
            return jsonify({
                'success': False,
                'message': '供应商名称已存在，请使用其他名称'
            })

        # 创建新供应商（包含初始款项字段）
        supplier = Supplier(
            code=new_code,
            name=name,
            supplier_type=request.form.get('supplier_type', 'product'),
            contact_person=request.form.get('contact_person', '').strip(),
            phone=request.form.get('phone', '').strip(),
            address=request.form.get('address', '').strip(),
            initial_owed_amount=float(request.form.get('owed_amount', 0)),
            initial_deposit_amount=float(request.form.get('deposit_amount', 0))
        )

        # 更新拼音字段
        try:
            from pypinyin import lazy_pinyin, Style

            # 获取全拼
            pinyin_list = lazy_pinyin(supplier.name)
            supplier.pinyin = ''.join(pinyin_list)

            # 获取首字母
            pinyin_initials = lazy_pinyin(supplier.name, style=Style.FIRST_LETTER)
            supplier.pinyin_initials = ''.join(pinyin_initials)
        except Exception as e:
            print(f"更新供应商拼音字段时出错: {e}")

        db.session.add(supplier)
        db.session.flush()  # 获取supplier.id

        # 处理所有旧料的初始值
        from models import SupplierMaterialBalance, OldMaterial

        # 获取所有旧料
        all_materials = OldMaterial.query.filter_by(is_active=True).all()

        for material in all_materials:
            # 所有旧料都从表单获取初始值
            owed_key = f'material_{material.name}_owed'
            stored_key = f'material_{material.name}_stored'
            owed_weight = float(request.form.get(owed_key, 0))
            stored_weight = float(request.form.get(stored_key, 0))

            # 为所有旧料类型创建记录，即使值为0（用于供应商款料明细显示）
            balance = SupplierMaterialBalance(
                supplier_id=supplier.id,
                material_name=material.name,
                owed_weight=owed_weight,
                stored_weight=stored_weight
            )
            db.session.add(balance)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '供应商添加成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"添加供应商失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        })

@app.route('/api/data_management/suppliers/edit_form/<int:supplier_id>')
@login_required
def api_data_management_suppliers_edit_form(supplier_id):
    """获取供应商编辑表单 - 直接使用服务器端渲染"""
    try:
        from models import Supplier, OldMaterial, SupplierMaterialBalance

        supplier = Supplier.query.get_or_404(supplier_id)
        old_materials = OldMaterial.query.filter_by(is_active=True).order_by(OldMaterial.code.asc()).all()

        # 获取供应商的旧料余额（初始值）
        supplier_balances = SupplierMaterialBalance.query.filter_by(supplier_id=supplier_id).order_by(SupplierMaterialBalance.created_at.asc()).all()
        balance_dict = {balance.material_name: balance for balance in supplier_balances}

        # 获取供应商的初始款项余额（从supplier表的初始字段获取）
        supplier_owed_amount = supplier.initial_owed_amount or 0
        supplier_deposit_amount = supplier.initial_deposit_amount or 0

        form_html = f'''
        <form id="supplier-edit-form" class="modal-form-container" style="width: 100%; max-height: 55vh; overflow-y: auto;">
            <div class="modal-form-header">
                <h4 class="modal-form-title">编辑供应商</h4>
            </div>
            <div class="modal-form-body">
                <!-- 基本信息行 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="name" class="form-label">供应商名称 <span class="text-muted">(不可修改)</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="{supplier.name}" readonly style="background-color: #f8f9fa;">
                    </div>
                    <div class="col-md-3">
                        <label for="supplier_type" class="form-label">供应商类别 <span class="text-muted">(不可修改)</span></label>
                        <input type="text" class="form-control" value="{'金料供应商' if supplier.supplier_type == 'gold' else '产品供应商'}" readonly style="background-color: #f8f9fa;">
                        <input type="hidden" name="supplier_type" value="{supplier.supplier_type}">
                    </div>
                    <div class="col-md-3">
                        <label for="contact_person" class="form-label">联系人</label>
                        <input type="text" class="form-control" id="contact_person" name="contact_person" value="{supplier.contact_person or ''}">
                    </div>
                    <div class="col-md-3">
                        <label for="phone" class="form-label">联系电话</label>
                        <input type="text" class="form-control" id="phone" name="phone" value="{supplier.phone or ''}">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="address" class="form-label">地址</label>
                        <input type="text" class="form-control" id="address" name="address" value="{supplier.address or ''}">
                    </div>
                    <div class="col-md-3">
                        <label for="created_at" class="form-label">添加日期 <span class="text-muted">(不可修改)</span></label>
                        <input type="text" class="form-control" id="created_at" name="created_at" value="{supplier.created_at.strftime('%Y-%m-%d %H:%M:%S') if supplier.created_at else ''}" readonly style="background-color: #f8f9fa;">
                    </div>
                </div>

                <!-- 旧料初始值表格 -->
                <div class="mb-3">
                    <h6 class="mb-2">旧料明细（<span class="text-danger fw-bold">初始值</span>）</h6>
                    <table class="simple-table" id="material-balance-table">
                        <thead>
                            <tr>
                                {"".join([f'<th colspan="2">{material.name}</th>' for material in old_materials])}
                                <th colspan="2">款项</th>
                            </tr>
                            <tr>
                                {"".join(['<th>欠料克重</th><th>存料克重</th>' for _ in old_materials])}
                                <th>欠款金额</th><th>存款金额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                {"".join([
                                    f'<td><input type="text" value="{balance_dict.get(material.name).owed_weight if balance_dict.get(material.name) else 0}" readonly class="form-control form-control-sm" title="初始欠料克重"></td><td><input type="text" value="{balance_dict.get(material.name).stored_weight if balance_dict.get(material.name) else 0}" readonly class="form-control form-control-sm" title="初始存料克重"></td>'
                                    for material in old_materials
                                ])}
                                <td><input type="text" value="{supplier_owed_amount}" readonly class="form-control form-control-sm" title="初始欠款金额"></td>
                                <td><input type="text" value="{supplier_deposit_amount}" readonly class="form-control form-control-sm" title="初始存款金额"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-form-footer" style="position: absolute; bottom: 10px; right: 15px;">
                <button type="submit" class="btn btn-primary btn-sm">保存</button>
                <button type="button" class="btn btn-secondary btn-sm ms-2" data-bs-dismiss="modal">取消</button>
            </div>
        </form>

        <style>
        /* 统一字体加粗加大 */
        .modal-form-container .form-label, .modal-form-container .form-control, .modal-form-container .form-select, .modal-form-container .btn {{
            font-weight: bold !important;
            font-size: 1.0em !important;
        }}

        .modal-form-container .simple-table {{
            width: 100%;
            margin: 0;
            border-collapse: collapse;
        }}

        .modal-form-container .simple-table thead th {{
            background-color: #495057;
            color: white;
            font-weight: bold;
            font-size: 14px;
            padding: 8px 4px;
            text-align: center;
            border: 1px solid #6c757d;
            position: sticky;
            top: 0;
            z-index: 10;
        }}

        .modal-form-container .simple-table td {{
            text-align: center;
            vertical-align: middle;
            padding: 4px;
            border: 1px solid #dee2e6;
        }}

        .modal-form-container .simple-table input {{
            width: 80px;
            text-align: center;
            font-size: 0.8rem;
            padding: 2px 4px;
            background-color: #f8f9fa;
        }}
        </style>
        '''
        return form_html
    except Exception as e:
        app.logger.error(f"获取供应商编辑表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/suppliers/edit/<int:supplier_id>', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_suppliers_edit(supplier_id):
    """处理供应商编辑"""
    try:
        supplier = Supplier.query.get_or_404(supplier_id)

        name = request.form.get('name', '').strip()

        if not name:
            return jsonify({
                'success': False,
                'message': '供应商名称不能为空'
            })

        # 更新供应商基本信息（只允许修改联系人、电话和地址）
        # 注意：不修改以下字段
        # - supplier.name (供应商名称)
        # - supplier.supplier_type (供应商类别)
        # - supplier.owed_gold (欠料克重)
        # - supplier.deposit_gold (存料克重)
        # - supplier.owed_amount (欠款金额)
        # - supplier.deposit_amount (存款金额)
        # - supplier.created_at (添加日期)
        supplier.contact_person = request.form.get('contact_person', '').strip()
        supplier.phone = request.form.get('phone', '').strip()
        supplier.address = request.form.get('address', '').strip()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '供应商更新成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"更新供应商失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        })

@app.route('/api/data_management/suppliers/delete/<int:supplier_id>', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_suppliers_delete(supplier_id):
    """处理供应商删除"""
    try:
        supplier = Supplier.query.get_or_404(supplier_id)
        supplier_name = supplier.name

        # 检查是否有关联的供应商往来记录
        from models import SupplierTransaction, SupplierMaterialBalance, AccountTransaction, MaterialTransaction, MoneyTransaction
        has_transactions = SupplierTransaction.query.filter_by(supplier_id=supplier_id).first() is not None
        if has_transactions:
            return jsonify({
                'success': False,
                'message': f'无法删除供应商 {supplier_name}，存在相关的往来记录'
            })

        # 检查是否有关联的采购单项
        has_purchase_items = PurchaseItem.query.filter_by(supplier_id=supplier_id).first() is not None
        if has_purchase_items:
            return jsonify({
                'success': False,
                'message': f'无法删除供应商 {supplier_name}，存在相关的采购记录'
            })

        # 删除相关的账户交易记录
        AccountTransaction.query.filter_by(supplier_id=supplier_id).delete()

        # 删除相关的SupplierMaterialBalance记录
        SupplierMaterialBalance.query.filter_by(supplier_id=supplier_id).delete()

        # 删除供应商记录
        db.session.delete(supplier)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'供应商 {supplier_name} 删除成功'
        })
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除供应商失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })



# 旧料管理表单API
@app.route('/api/data_management/materials/add_form')
@login_required
def api_data_management_materials_add_form():
    """获取旧料添加表单"""
    try:
        form_html = '''
        <form id="material-add-form" class="modal-form-container">
            <div class="modal-form-header">
                <h5>添加旧料</h5>
            </div>
            <div class="modal-form-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="material-code" class="form-label small">旧料编号</label>
                            <input type="text" class="form-control form-control-sm" id="material-code" name="code" placeholder="留空自动生成">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="material-name" class="form-label small">旧料名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm" id="material-name" name="name" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="material-initial-stock" class="form-label small">料部初始库存</label>
                            <input type="number" step="0.01" class="form-control form-control-sm" id="material-initial-stock" name="initial_stock" value="0">
                            <div class="form-text">料部初始库存为固定值，创建后不可修改。当前库存通过料部库存查询计算</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-custom">
                <button type="submit" class="btn btn-modal-primary">添加旧料</button>
                <button type="button" class="btn btn-modal-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </form>
        <style>
        /* 统一字体加粗加大 */
        .modal-form-container .form-label, .modal-form-container .form-control, .modal-form-container .form-select, .modal-form-container .btn {
            font-weight: bold !important;
            font-size: 1.0em !important;
        }
        .form-label.small {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        </style>
        '''

        return form_html

    except Exception as e:
        app.logger.error(f"获取旧料添加表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/materials/add', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_materials_add():
    """处理旧料添加"""
    try:
        name = request.form.get('name', '').strip()
        if not name:
            return jsonify({
                'success': False,
                'message': '旧料名称不能为空'
            })

        # 检查名称是否重复
        existing_material = OldMaterial.query.filter_by(name=name).first()
        if existing_material:
            return jsonify({
                'success': False,
                'message': '旧料名称已存在，请使用其他名称'
            })

        code = request.form.get('code', '').strip()
        if not code:
            # 如果code为空，生成一个唯一的code
            import random
            code = f"OM{random.randint(10000, 99999)}"
            # 确保code唯一
            while OldMaterial.query.filter_by(code=code).first() is not None:
                code = f"OM{random.randint(10000, 99999)}"
        else:
            # 检查code是否已存在
            if OldMaterial.query.filter_by(code=code).first() is not None:
                return jsonify({
                    'success': False,
                    'message': '旧料编号已存在，请使用其他编号'
                })

        initial_stock = float(request.form.get('initial_stock', 0))
        description = request.form.get('description', '').strip()

        # 创建旧料记录
        new_material = OldMaterial(
            name=name,
            code=code,
            initial_stock=initial_stock,
            description=description,
            is_active=True
        )

        db.session.add(new_material)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '旧料添加成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"添加旧料失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        })

@app.route('/api/data_management/materials/delete/<int:material_id>', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_materials_delete(material_id):
    """处理旧料删除"""
    try:
        old_material = OldMaterial.query.get_or_404(material_id)
        material_name = old_material.name

        # 检查是否有关联的订单项（旧料项目）
        order_items = OrderItem.query.filter_by(
            item_type='old', material_name=old_material.name
        ).all()
        if order_items:
            order_count = len(order_items)
            return jsonify({
                'success': False,
                'message': f'该旧料已有关联的{order_count}个订单项目，不能删除'
            })

        # 检查是否有关联的供应商往来记录
        material_transactions = MaterialTransaction.query.filter(
            (MaterialTransaction.return_material_type == old_material.name) |
            (MaterialTransaction.store_material_type == old_material.name) |
            (MaterialTransaction.deposit_material_type == old_material.name)
        ).all()
        if material_transactions:
            transaction_count = len(material_transactions)
            return jsonify({
                'success': False,
                'message': f'该旧料已有关联的{transaction_count}个供应商往来记录，不能删除'
            })

        # 检查是否有关联的客户旧料余额记录
        from models import CustomerMaterialBalance
        customer_balances = CustomerMaterialBalance.query.filter_by(
            material_name=old_material.name
        ).all()
        if customer_balances:
            balance_count = len(customer_balances)
            return jsonify({
                'success': False,
                'message': f'该旧料已有关联的{balance_count}个客户旧料余额记录，不能删除'
            })

        # 检查是否有关联的供应商旧料余额记录
        supplier_balances = SupplierMaterialBalance.query.filter_by(
            material_name=old_material.name
        ).all()
        if supplier_balances:
            balance_count = len(supplier_balances)
            return jsonify({
                'success': False,
                'message': f'该旧料已有关联的{balance_count}个供应商旧料余额记录，不能删除'
            })

        # 检查是否有关联的货料调整记录
        material_adjustments = MaterialAdjustmentItem.query.filter_by(
            target_type='old_material', target_name=old_material.name
        ).all()
        if material_adjustments:
            adjustment_count = len(material_adjustments)
            return jsonify({
                'success': False,
                'message': f'该旧料已有关联的{adjustment_count}个货料调整记录，不能删除'
            })

        # 如果没有任何关联记录，则可以删除
        db.session.delete(old_material)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'旧料 {material_name} 删除成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除旧料失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })

@app.route('/api/data_management/employees/delete/<int:employee_id>', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_employees_delete(employee_id):
    """处理员工删除"""
    try:
        employee = Employee.query.get_or_404(employee_id)
        employee_name = employee.name

        # 检查是否有关联的订单记录
        has_orders = Order.query.filter_by(created_by=employee_id).first() is not None
        if has_orders:
            return jsonify({
                'success': False,
                'message': f'无法删除员工 {employee_name}，存在相关的订单记录'
            })

        # 查找并删除对应的用户账户
        from models import User
        user = User.query.filter_by(employee_id=employee_id).first()
        if user:
            db.session.delete(user)
            app.logger.info(f"删除员工 {employee_name} 对应的用户账户: {user.username}")

        # 删除员工记录
        db.session.delete(employee)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'员工 {employee_name} 及对应用户删除成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除员工失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })

@app.route('/api/data_management/accounts/delete/<int:account_id>', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_accounts_delete(account_id):
    """处理账户删除"""
    try:
        account = Account.query.get_or_404(account_id)
        account_name = account.name

        # 检查是否为默认账户
        if account.name == '默认账户':
            return jsonify({
                'success': False,
                'message': '默认账户不能删除'
            })

        # 检查是否有关联的订单
        has_orders = Order.query.filter_by(account_id=account_id).first() is not None
        if has_orders:
            return jsonify({
                'success': False,
                'message': f'无法删除账户 {account_name}，存在相关的订单记录'
            })

        # 检查是否有关联的账户交易记录
        has_account_transactions = AccountTransaction.query.filter_by(account_id=account_id).first() is not None
        if has_account_transactions:
            return jsonify({
                'success': False,
                'message': f'无法删除账户 {account_name}，存在相关的账户交易记录'
            })

        # 检查是否有关联的其他项目往来记录
        has_transactions = Transaction.query.filter_by(account_id=account_id).first() is not None
        if has_transactions:
            return jsonify({
                'success': False,
                'message': f'无法删除账户 {account_name}，存在相关的其他项目往来记录'
            })

        # 删除账户记录
        db.session.delete(account)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'账户 {account_name} 删除成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"删除账户失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })



# 员工管理表单API
@app.route('/api/data_management/employees/add_form')
@login_required
def api_data_management_employees_add_form():
    """获取员工添加表单"""
    try:
        form_html = '''
        <form id="employee-add-form" class="modal-form-container">
            <div class="modal-form-header">
                <h5>添加员工</h5>
            </div>
            <div class="modal-form-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label for="employee-name" class="form-label small"><strong>姓名 <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control form-control-sm" id="employee-name" name="name" required>
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-gender" class="form-label small"><strong>性别</strong></label>
                            <select class="form-select form-select-sm" id="employee-gender" name="gender">
                                <option value="male">男</option>
                                <option value="female">女</option>
                            </select>
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-phone" class="form-label small"><strong>电话</strong></label>
                            <input type="text" class="form-control form-control-sm" id="employee-phone" name="phone">
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-address" class="form-label small"><strong>地址</strong></label>
                            <input type="text" class="form-control form-control-sm" id="employee-address" name="address">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label for="employee-position" class="form-label small"><strong>职位 <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control form-control-sm" id="employee-position" name="position" required>
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-salary" class="form-label small"><strong>薪资</strong></label>
                            <input type="number" class="form-control form-control-sm" id="employee-salary" name="salary" step="0.01" value="0">
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-hire-date" class="form-label small"><strong>入职日期</strong></label>
                            <input type="date" class="form-control form-control-sm" id="employee-hire-date" name="hire_date">
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-leave-date" class="form-label small"><strong>离职日期</strong></label>
                            <input type="date" class="form-control form-control-sm" id="employee-leave-date" name="leave_date">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-custom">
                <button type="submit" class="btn btn-modal-primary">添加员工</button>
                <button type="button" class="btn btn-modal-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </form>

        <style>
        /* 统一字体加粗加大 */
        .modal-form-body .form-label, .modal-form-body .form-control, .modal-form-body .form-select, .modal-footer .btn {
            font-weight: bold !important;
            font-size: 1.0em !important;
        }
        .form-label.small {
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        </style>
        '''

        return form_html

    except Exception as e:
        app.logger.error(f"获取员工添加表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/employees/add', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_employees_add():
    """处理员工添加"""
    try:
        name = request.form.get('name', '').strip()
        if not name:
            return jsonify({
                'success': False,
                'message': '员工姓名不能为空'
            })

        # 检查员工姓名是否重复
        existing_employee = Employee.query.filter_by(name=name).first()
        if existing_employee:
            return jsonify({
                'success': False,
                'message': '员工姓名已存在，请使用其他姓名'
            })

        # 获取基本信息
        gender = request.form.get('gender', 'male')
        phone = request.form.get('phone', '').strip()
        position = request.form.get('position', '').strip()
        address = request.form.get('address', '').strip()

        # 验证职位字段
        if not position:
            return jsonify({
                'success': False,
                'message': '职位不能为空'
            })

        # 处理数值字段
        salary = float(request.form.get('salary', 0))

        # 处理日期字段
        hire_date_str = request.form.get('hire_date', '').strip()
        hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d') if hire_date_str else datetime.now()

        leave_date_str = request.form.get('leave_date', '').strip()
        leave_date = datetime.strptime(leave_date_str, '%Y-%m-%d') if leave_date_str else None

        # 根据离职日期确定员工状态
        is_active = leave_date is None

        # 创建员工记录
        employee = Employee(
            name=name,
            gender=gender,
            phone=phone,
            position=position,
            salary=salary,
            hire_date=hire_date,
            leave_date=leave_date,
            address=address,
            is_active=is_active
        )

        db.session.add(employee)
        db.session.commit()

        # 自动为员工创建用户账户
        try:
            from routes.user_routes import create_user_for_employee
            create_user_for_employee(employee)
        except Exception as e:
            app.logger.warning(f"为员工创建用户账户失败: {str(e)}")

        return jsonify({
            'success': True,
            'message': '员工添加成功'
        })

    except ValueError as e:
        db.session.rollback()
        if "does not match format" in str(e):
            return jsonify({
                'success': False,
                'message': '日期格式错误，请使用正确的日期格式'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'添加失败: {str(e)}'
            })
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"添加员工失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        })

@app.route('/api/data_management/employees/edit_form/<int:employee_id>')
@login_required
def api_data_management_employees_edit_form(employee_id):
    """获取员工编辑表单"""
    try:
        employee = Employee.query.get_or_404(employee_id)

        # 格式化日期
        hire_date_str = employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else ''
        leave_date_str = employee.leave_date.strftime('%Y-%m-%d') if employee.leave_date else ''

        form_html = f'''
        <form id="employee-edit-form" class="modal-form-container" data-employee-id="{employee.id}">
            <div class="modal-form-header">
                <h5>编辑员工</h5>
            </div>
            <div class="modal-form-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label for="employee-name" class="form-label small"><strong>姓名 <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control form-control-sm" id="employee-name" name="name" value="{employee.name}" required>
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-gender" class="form-label small"><strong>性别</strong></label>
                            <select class="form-select form-select-sm" id="employee-gender" name="gender">
                                <option value="male" {"selected" if employee.gender == "male" else ""}>男</option>
                                <option value="female" {"selected" if employee.gender == "female" else ""}>女</option>
                            </select>
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-phone" class="form-label small"><strong>电话</strong></label>
                            <input type="text" class="form-control form-control-sm" id="employee-phone" name="phone" value="{employee.phone or ''}">
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-address" class="form-label small"><strong>地址</strong></label>
                            <input type="text" class="form-control form-control-sm" id="employee-address" name="address" value="{employee.address or ''}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label for="employee-position" class="form-label small"><strong>职位 <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control form-control-sm" id="employee-position" name="position" value="{employee.position or ''}" required>
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-salary" class="form-label small"><strong>薪资</strong></label>
                            <input type="number" class="form-control form-control-sm" id="employee-salary" name="salary" step="0.01" value="{employee.salary or 0}">
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-hire-date" class="form-label small"><strong>入职日期</strong></label>
                            <input type="date" class="form-control form-control-sm" id="employee-hire-date" name="hire_date" value="{hire_date_str}">
                        </div>

                        <div class="form-group mb-2">
                            <label for="employee-leave-date" class="form-label small"><strong>离职日期</strong></label>
                            <input type="date" class="form-control form-control-sm" id="employee-leave-date" name="leave_date" value="{leave_date_str}">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-custom">
                <button type="submit" class="btn btn-modal-primary">保存</button>
                <button type="button" class="btn btn-modal-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </form>

        <style>
        /* 统一字体加粗加大 */
        .modal-form-body .form-label, .modal-form-body .form-control, .modal-form-body .form-select, .modal-footer .btn {{
            font-weight: bold !important;
            font-size: 1.0em !important;
        }}
        .form-label.small {{
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }}
        </style>
        '''

        return form_html

    except Exception as e:
        app.logger.error(f"获取员工编辑表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/employees/edit/<int:employee_id>', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_employees_edit(employee_id):
    """处理员工编辑"""
    try:
        employee = Employee.query.get_or_404(employee_id)

        name = request.form.get('name', '').strip()
        if not name:
            return jsonify({
                'success': False,
                'message': '员工姓名不能为空'
            })

        # 检查员工姓名是否重复（排除当前员工）
        existing_employee = Employee.query.filter(
            Employee.name == name,
            Employee.id != employee.id
        ).first()
        if existing_employee:
            return jsonify({
                'success': False,
                'message': '员工姓名已存在，请使用其他姓名'
            })

        # 获取并验证职位
        position = request.form.get('position', '').strip()
        if not position:
            return jsonify({
                'success': False,
                'message': '职位不能为空'
            })

        # 更新员工信息
        employee.name = name
        employee.gender = request.form.get('gender', 'male')
        employee.phone = request.form.get('phone', '').strip()
        employee.position = position
        employee.address = request.form.get('address', '').strip()

        # 处理数值字段
        employee.salary = float(request.form.get('salary', 0))

        # 处理日期字段
        hire_date_str = request.form.get('hire_date', '').strip()
        if hire_date_str:
            employee.hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d')

        leave_date_str = request.form.get('leave_date', '').strip()
        if leave_date_str:
            employee.leave_date = datetime.strptime(leave_date_str, '%Y-%m-%d')
        else:
            employee.leave_date = None

        # 根据离职日期更新员工状态
        employee.is_active = employee.leave_date is None

        employee.updated_at = datetime.now()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '员工信息更新成功'
        })

    except ValueError as e:
        db.session.rollback()
        if "does not match format" in str(e):
            return jsonify({
                'success': False,
                'message': '日期格式错误，请使用正确的日期格式'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'更新失败: {str(e)}'
            })
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"更新员工失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        })

# 账户管理表单API
@app.route('/api/data_management/accounts/add_form')
@login_required
def api_data_management_accounts_add_form():
    """获取账户添加表单"""
    try:
        form_html = '''
        <form id="account-add-form" class="modal-form-container">
            <div class="modal-form-header">
                <h5>添加账户</h5>
            </div>
            <div class="modal-form-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label for="account-name" class="form-label small"><strong>账户名称 <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control form-control-sm" id="account-name" name="name" required>
                        </div>

                        <div class="form-group mb-2">
                            <label for="account-notes" class="form-label small"><strong>备注</strong></label>
                            <textarea class="form-control form-control-sm" id="account-notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label for="account-bank-name" class="form-label small"><strong>银行名称</strong></label>
                            <input type="text" class="form-control form-control-sm" id="account-bank-name" name="bank_name">
                        </div>

                        <div class="form-group mb-2">
                            <label for="account-number" class="form-label small"><strong>账号</strong></label>
                            <input type="text" class="form-control form-control-sm" id="account-number" name="account_number">
                        </div>

                        <div class="form-group mb-2">
                            <label for="account-initial-balance" class="form-label small"><strong>初始余额</strong></label>
                            <input type="number" class="form-control form-control-sm" id="account-initial-balance" name="initial_balance" step="0.01" value="0">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-custom">
                <button type="submit" class="btn btn-modal-primary">添加账户</button>
                <button type="button" class="btn btn-modal-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </form>
        '''

        return form_html

    except Exception as e:
        app.logger.error(f"获取账户添加表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/accounts/add', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_accounts_add():
    """处理账户添加"""
    try:
        name = request.form.get('name', '').strip()
        if not name:
            return jsonify({
                'success': False,
                'message': '账户名称不能为空'
            })

        # 检查账户名称是否重复
        existing_account_name = Account.query.filter_by(name=name).first()
        if existing_account_name:
            return jsonify({
                'success': False,
                'message': '账户名称已存在，请使用其他名称'
            })

        account_number = request.form.get('account_number', '').strip()
        bank_name = request.form.get('bank_name', '').strip()
        initial_balance = float(request.form.get('initial_balance', 0))
        notes = request.form.get('notes', '').strip()

        # 创建账户记录
        account = Account(
            name=name,
            bank_name=bank_name,
            account_number=account_number,
            initial_balance=initial_balance,
            balance=initial_balance,  # 初始时当前余额等于初始余额
            notes=notes,
            is_active=True
        )

        db.session.add(account)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '账户添加成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"添加账户失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加失败: {str(e)}'
        })

@app.route('/api/data_management/accounts/edit_form/<int:account_id>')
@login_required
def api_data_management_accounts_edit_form(account_id):
    """获取账户编辑表单"""
    try:
        account = Account.query.get_or_404(account_id)

        form_html = f'''
        <form id="account-edit-form" class="modal-form-container" data-account-id="{account.id}">
            <div class="modal-form-header">
                <h5>编辑账户</h5>
            </div>
            <div class="modal-form-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label for="account-name" class="form-label small"><strong>账户名称 <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control form-control-sm" id="account-name" name="name" value="{account.name}" required>
                        </div>

                        <div class="form-group mb-2">
                            <label for="account-notes" class="form-label small"><strong>备注</strong></label>
                            <textarea class="form-control form-control-sm" id="account-notes" name="notes" rows="3">{account.notes or ''}</textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-2">
                            <label for="account-bank-name" class="form-label small"><strong>银行名称</strong></label>
                            <input type="text" class="form-control form-control-sm" id="account-bank-name" name="bank_name" value="{account.bank_name or ''}">
                        </div>

                        <div class="form-group mb-2">
                            <label for="account-number" class="form-label small"><strong>账号</strong></label>
                            <input type="text" class="form-control form-control-sm" id="account-number" name="account_number" value="{account.account_number or ''}">
                        </div>

                        <div class="form-group mb-2">
                            <label for="account-initial-balance" class="form-label small"><strong>初始余额 <span class="text-muted">(不可修改)</span></strong></label>
                            <input type="number" class="form-control form-control-sm" id="account-initial-balance" name="initial_balance" step="0.01" value="{account.initial_balance or 0}" readonly style="background-color: #f8f9fa;">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-custom">
                <button type="submit" class="btn btn-modal-primary">保存</button>
                <button type="button" class="btn btn-modal-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </form>
        '''

        return form_html

    except Exception as e:
        app.logger.error(f"获取账户编辑表单失败: {str(e)}")
        return f'<div class="alert alert-danger">加载表单失败: {str(e)}</div>'

@app.route('/api/data_management/accounts/edit/<int:account_id>', methods=['POST'])
@login_required
@csrf.exempt
def api_data_management_accounts_edit(account_id):
    """处理账户编辑"""
    try:
        account = Account.query.get_or_404(account_id)

        name = request.form.get('name', '').strip()
        if not name:
            return jsonify({
                'success': False,
                'message': '账户名称不能为空'
            })

        # 检查账户名称是否重复（排除当前账户）
        existing_account_name = Account.query.filter(
            Account.name == name,
            Account.id != account.id
        ).first()
        if existing_account_name:
            return jsonify({
                'success': False,
                'message': '账户名称已存在，请使用其他名称'
            })

        # 更新账户信息（不修改初始余额和当前余额）
        account.name = name
        account.bank_name = request.form.get('bank_name', '').strip()
        account.account_number = request.form.get('account_number', '').strip()
        account.notes = request.form.get('notes', '').strip()
        account.updated_at = datetime.now()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '账户信息更新成功'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"更新账户失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        })

# 运行程序
if __name__ == '__main__':
    with app.app_context():
        # 确保数据库表存在
        db.create_all()
        # 初始化应用数据
        init_app_context()

    # 设置主机和端口
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'

    # 在调试模式下运行应用
    app.run(host=host, port=port, debug=debug)
