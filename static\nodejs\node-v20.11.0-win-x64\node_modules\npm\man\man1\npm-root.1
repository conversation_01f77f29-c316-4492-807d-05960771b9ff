.TH "NPM-ROOT" "1" "November 2023" "" ""
.SH "NAME"
\fBnpm-root\fR - Display npm root
.SS "Synopsis"
.P
.RS 2
.nf
npm root
.fi
.RE
.P
Note: This command is unaware of workspaces.
.SS "Description"
.P
Print the effective \fBnode_modules\fR folder to standard out.
.P
Useful for using npm in shell scripts that do things with the \fBnode_modules\fR folder. For example:
.P
.RS 2
.nf
#!/bin/bash
global_node_modules="$(npm root --global)"
echo "Global packages installed in: ${global_node_modules}"
.fi
.RE
.SS "Configuration"
.SS "\fBglobal\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Operates in "global" mode, so that packages are installed into the \fBprefix\fR folder instead of the current working directory. See npm help folders for more on the differences in behavior.
.RS 0
.IP \(bu 4
packages are installed into the \fB{prefix}/lib/node_modules\fR folder, instead of the current working directory.
.IP \(bu 4
bin files are linked to \fB{prefix}/bin\fR
.IP \(bu 4
man pages are linked to \fB{prefix}/share/man\fR
.RE 0

.SS "See Also"
.RS 0
.IP \(bu 4
npm help prefix
.IP \(bu 4
npm help folders
.IP \(bu 4
npm help config
.IP \(bu 4
npm help npmrc
.RE 0
