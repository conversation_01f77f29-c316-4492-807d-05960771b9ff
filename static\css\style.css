/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 侧边栏样式 */
.sidebar {
    background-color: #343a40;
    min-height: 100vh;
    position: sticky;
    top: 0;
    padding-top: 15px;
    z-index: 1000;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.75);
    padding: 0.5rem 1rem;
    margin: 0.2rem 0;
}

.sidebar .nav-link:hover {
    color: rgba(255, 255, 255, 1);
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: #ffc107;
    border-radius: 5px;
}

.sidebar .nav-link i {
    margin-right: 5px;
    width: 20px;
    text-align: center;
}

/* 主内容区样式 */
.main-content {
    padding: 20px;
    padding-bottom: 40px;
}

/* 卡片样式 */
.card {
    transition: transform 0.2s, box-shadow 0.2s;
    margin-bottom: 20px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.03);
}

/* 删除冲突的表格样式 */

/* 按钮样式 */
.btn {
    border-radius: 5px;
    padding: 0.375rem 0.75rem;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-gold {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #343a40;
}

.btn-gold:hover {
    background-color: #e0a800;
    border-color: #e0a800;
    color: #343a40;
}

/* 表单样式 */
.form-control:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
}

/* 金色主题样式 */
.text-gold {
    color: #ffc107 !important;
}

.bg-gold {
    background-color: #ffc107 !important;
}

.border-gold {
    border-color: #ffc107 !important;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .sidebar {
        position: fixed;
        width: 100%;
        height: auto;
        min-height: auto;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
}

/* 订单页面样式 */
.order-header {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.order-items {
    margin-bottom: 20px;
}

.order-summary {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #999;
} 