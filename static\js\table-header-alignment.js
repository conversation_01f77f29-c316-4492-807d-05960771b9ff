/**
 * 表格标题容器与数据表格容器宽度对齐修复脚本
 * 解决标题容器比数据表格容器宽的问题
 */

(function() {
    'use strict';

    /**
     * 计算滚动条宽度
     */
    function getScrollbarWidth() {
        // 创建一个临时的div元素来测量滚动条宽度
        const outer = document.createElement('div');
        outer.style.visibility = 'hidden';
        outer.style.overflow = 'scroll';
        outer.style.msOverflowStyle = 'scrollbar';
        document.body.appendChild(outer);

        const inner = document.createElement('div');
        outer.appendChild(inner);

        const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
        outer.parentNode.removeChild(outer);

        return scrollbarWidth;
    }

    /**
     * 调整卡片标题容器宽度以匹配表格容器
     */
    function adjustCardHeaderWidth() {
        // 查找所有包含表格的卡片
        const tableResponsiveElements = document.querySelectorAll('.table-responsive');

        tableResponsiveElements.forEach(tableContainer => {
            const card = tableContainer.closest('.card');
            if (card) {
                adjustSingleCardHeader(card, tableContainer);
            }
        });
    }

    /**
     * 调整单个卡片的标题宽度
     */
    function adjustSingleCardHeader(card, tableContainer) {
        const cardHeader = card.querySelector('.card-header');
        if (!cardHeader) return;

        // 获取表格容器的实际尺寸
        const tableRect = tableContainer.getBoundingClientRect();
        const cardRect = card.getBoundingClientRect();

        // 检查表格容器是否有滚动条
        const hasVerticalScrollbar = tableContainer.scrollHeight > tableContainer.clientHeight;
        const hasHorizontalScrollbar = tableContainer.scrollWidth > tableContainer.clientWidth;

        // 计算滚动条宽度
        const scrollbarWidth = getScrollbarWidth();

        // 添加CSS类来标识有滚动条的卡片
        if (hasVerticalScrollbar || hasHorizontalScrollbar) {
            card.classList.add('card-with-scrollable-table');

            // 设置CSS变量
            document.documentElement.style.setProperty('--scrollbar-width', `${scrollbarWidth}px`);

            // 精确调整标题容器宽度
            const adjustedPadding = hasVerticalScrollbar ?
                `calc(1.5rem - ${scrollbarWidth}px)` : '1.5rem';

            cardHeader.style.paddingRight = adjustedPadding;
            cardHeader.style.boxSizing = 'border-box';
            cardHeader.style.width = '100%';

            // 确保表格容器也正确设置
            tableContainer.style.width = '100%';
            tableContainer.style.boxSizing = 'border-box';

        } else {
            // 没有滚动条时恢复默认样式
            card.classList.remove('card-with-scrollable-table');
            document.documentElement.style.setProperty('--scrollbar-width', '0px');

            cardHeader.style.paddingRight = '1.5rem';
            cardHeader.style.width = '100%';
            cardHeader.style.boxSizing = 'border-box';
        }

        // 强制重新计算布局
        cardHeader.offsetHeight;
        tableContainer.offsetHeight;
    }

    /**
     * 监听表格内容变化
     */
    function observeTableChanges() {
        const tableContainers = document.querySelectorAll('.table-responsive');
        
        tableContainers.forEach(container => {
            // 使用ResizeObserver监听容器大小变化
            if (window.ResizeObserver) {
                const resizeObserver = new ResizeObserver(() => {
                    adjustCardHeaderWidth();
                });
                resizeObserver.observe(container);
            }

            // 监听滚动事件
            container.addEventListener('scroll', () => {
                adjustCardHeaderWidth();
            });
        });
    }

    /**
     * 初始化函数
     */
    function init() {
        // 页面加载完成后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                adjustCardHeaderWidth();
                observeTableChanges();
            });
        } else {
            adjustCardHeaderWidth();
            observeTableChanges();
        }

        // 窗口大小变化时重新调整
        window.addEventListener('resize', () => {
            adjustCardHeaderWidth();
        });

        // 监听动态内容加载
        if (window.MutationObserver) {
            const observer = new MutationObserver((mutations) => {
                let shouldAdjust = false;
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        const addedNodes = Array.from(mutation.addedNodes);
                        const hasTableElements = addedNodes.some(node => 
                            node.nodeType === 1 && (
                                node.classList?.contains('table-responsive') ||
                                node.querySelector?.('.table-responsive')
                            )
                        );
                        if (hasTableElements) {
                            shouldAdjust = true;
                        }
                    }
                });
                
                if (shouldAdjust) {
                    adjustCardHeaderWidth();
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // 启动脚本
    init();

    // 导出函数供外部调用
    window.TableHeaderAlignment = {
        adjust: adjustCardHeaderWidth,
        init: init
    };

})();
