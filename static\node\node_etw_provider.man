<instrumentationManifest
    xmlns="http://schemas.microsoft.com/win/2004/08/events"
    xmlns:win="http://manifests.microsoft.com/win/2004/08/windows/events"
    xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <instrumentation>
        <events>
            <provider name="NodeJS-ETW-provider"
                guid="{77754E9B-264B-4D8D-B981-E4135C1ECB0C}"
                symbol="NODE_ETW_PROVIDER"
                message="$(string.NodeJS-ETW-provider.name)"
                resourceFileName="node.exe"
                messageFileName="node.exe">

                <tasks>
                  <task name="MethodRuntime" value="1"
                      symbol="JSCRIPT_METHOD_RUNTIME_TASK">
                    <opcodes>
                      <opcode name="MethodLoad" value="10"
                          symbol="JSCRIPT_METHOD_METHODLOAD_OPCODE"/>
                    </opcodes>
                  </task>
                </tasks>

                <opcodes>
                    <opcode name="NODE_HTTP_SERVER_REQUEST" value="10"/>
                    <opcode name="NODE_HTTP_SERVER_RESPONSE" value="11"/>
                    <opcode name="NODE_HTTP_CLIENT_REQUEST" value="12"/>
                    <opcode name="NODE_HTTP_CLIENT_RESPONSE" value="13"/>
                    <opcode name="NODE_NET_SERVER_CONNECTION" value="14"/>
                    <opcode name="NODE_NET_STREAM_END" value="15"/>
                    <opcode name="NODE_GC_START" value="16"/>
                    <opcode name="NODE_GC_DONE" value="17"/>
                    <opcode name="NODE_V8SYMBOL_REMOVE" value="21"/>
                    <opcode name="NODE_V8SYMBOL_MOVE" value="22"/>
                    <opcode name="NODE_V8SYMBOL_RESET" value="23"/>
                </opcodes>

                <templates>
                    <template tid="node_connection">
                        <data name="fd" inType="win:UInt32" />
                        <data name="port" inType="win:UInt32" />
                        <data name="remote" inType="win:AnsiString" />
                        <data name="buffered" inType="win:UInt32" />
                    </template>

                    <template tid="node_http_client_request">
                        <data name="url" inType="win:AnsiString" />
                        <data name="method" inType="win:AnsiString" />
                        <data name="fd" inType="win:UInt32" />
                        <data name="port" inType="win:UInt32" />
                        <data name="remote" inType="win:AnsiString" />
                        <data name="buffered" inType="win:UInt32" />
                    </template>

                    <template tid="node_http_server_request">
                        <data name="url" inType="win:AnsiString" />
                        <data name="method" inType="win:AnsiString" />
                        <data name="forwardedFor" inType="win:AnsiString" />
                        <data name="fd" inType="win:UInt32" />
                        <data name="port" inType="win:UInt32" />
                        <data name="remote" inType="win:AnsiString" />
                        <data name="buffered" inType="win:UInt32" />
                    </template>

                    <template tid="node_gc">
                        <data name="gctype" inType="win:UInt32" />
                        <data name="gccallbackflags" inType="win:UInt32" />
                    </template>

                    <template tid="V8AddressChange">
                        <data name="addr1" inType="win:Pointer" outType="win:HexInt64"/>
                        <data name="addr2" inType="win:Pointer" outType="win:HexInt64"/>
                    </template>

                    <template tid="MethodLoadUnload">
                        <data name="ScriptContextID" inType="win:Pointer" outType="win:HexInt64"/>
                        <data name="MethodStartAddress" inType="win:Pointer" outType="win:HexInt64" />
                        <data name="MethodSize" inType="win:UInt64" />
                        <data name="MethodID" inType="win:UInt32" />
                        <data name="MethodFlags" inType="win:UInt16" />
                        <data name="MethodAddressRangeID" inType="win:UInt16" />
                        <data name="SourceID" inType="win:UInt64" />
                        <data name="Line" inType="win:UInt32" outType="xs:unsignedInt" />
                        <data name="Column" inType="win:UInt32" outType="xs:unsignedInt" />
                        <data name="MethodName" inType="win:UnicodeString" outType="xs:string" />
                    </template>
                </templates>

                <events>
                    <event value="1"
                        opcode="NODE_HTTP_SERVER_REQUEST"
                        template="node_http_server_request"
                        symbol="NODE_HTTP_SERVER_REQUEST_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.1.message)"
                        level="win:Informational"/>
                    <event value="2"
                        opcode="NODE_HTTP_SERVER_RESPONSE"
                        template="node_connection"
                        symbol="NODE_HTTP_SERVER_RESPONSE_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.2.message)"
                        level="win:Informational"/>
                    <event value="3"
                        opcode="NODE_HTTP_CLIENT_REQUEST"
                        template="node_http_client_request"
                        symbol="NODE_HTTP_CLIENT_REQUEST_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.3.message)"
                        level="win:Informational"/>
                    <event value="4"
                        opcode="NODE_HTTP_CLIENT_RESPONSE"
                        template="node_connection"
                        symbol="NODE_HTTP_CLIENT_RESPONSE_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.4.message)"
                        level="win:Informational"/>
                    <event value="5"
                        opcode="NODE_NET_SERVER_CONNECTION"
                        template="node_connection"
                        symbol="NODE_NET_SERVER_CONNECTION_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.5.message)"
                        level="win:Informational"/>
                    <event value="6"
                        opcode="NODE_NET_STREAM_END"
                        template="node_connection"
                        symbol="NODE_NET_STREAM_END_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.6.message)"
                        level="win:Informational"/>
                    <event value="7"
                        opcode="NODE_GC_START"
                        template="node_gc"
                        symbol="NODE_GC_START_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.7.message)"
                        level="win:Informational"/>
                    <event value="8"
                        opcode="NODE_GC_DONE"
                        template="node_gc"
                        symbol="NODE_GC_DONE_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.8.message)"
                        level="win:Informational"/>
                    <event value="9"
                        level="win:Informational"
                        opcode="MethodLoad"
                        symbol="MethodLoad"
                        message="$(string.NodeJS-ETW-provider.event.9.message)"
                        task="MethodRuntime"
                        template="MethodLoadUnload"/>
                    <event value="21"
                        opcode="NODE_V8SYMBOL_REMOVE"
                        template="V8AddressChange"
                        symbol="NODE_V8SYMBOL_REMOVE_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.21.message)"
                        level="win:Informational" />
                    <event value="22"
                        opcode="NODE_V8SYMBOL_MOVE"
                        template="V8AddressChange"
                        symbol="NODE_V8SYMBOL_MOVE_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.22.message)"
                        level="win:Informational" />
                    <event value="23"
                        opcode="NODE_V8SYMBOL_RESET"
                        symbol="NODE_V8SYMBOL_RESET_EVENT"
                        message="$(string.NodeJS-ETW-provider.event.23.message)"
                        level="win:Informational" />
                </events>
            </provider>
        </events>
    </instrumentation>
    <localization>
        <resources culture="en-US">
            <stringTable>
                <string id="NodeJS-ETW-provider.name" value="Node.js ETW Provider"/>
                <string id="NodeJS-ETW-provider.event.1.message" value="Node.js HTTP Server Request%nMethod: %2%nRemote: %6%nPort: %5%nURL: %1"/>
                <string id="NodeJS-ETW-provider.event.2.message" value="Node.js HTTP Server Response%nRemote: %3%nPort: %2"/>
                <string id="NodeJS-ETW-provider.event.3.message" value="Node.js HTTP Client Request%nMethod: %2%nRemote: %5%nPort: %4%nURL: %1"/>
                <string id="NodeJS-ETW-provider.event.4.message" value="Node.js HTTP Client Response%nRemote: %3%nPort: %2"/>
                <string id="NodeJS-ETW-provider.event.5.message" value="Node.js Net Server Connection%nRemote: %3%nPort: %2"/>
                <string id="NodeJS-ETW-provider.event.6.message" value="Node.js Net Stream End%nRemote: %3%nPort: %2"/>
                <string id="NodeJS-ETW-provider.event.7.message" value="Node.js Garbage Collection Start"/>
                <string id="NodeJS-ETW-provider.event.8.message" value="Node.js Garbage Collection Done"/>
                <string id="NodeJS-ETW-provider.event.9.message" value="Node.js Function Compiled: %10"/>
                <string id="NodeJS-ETW-provider.event.21.message" value="Node.js V8 Symbol Remove"/>
                <string id="NodeJS-ETW-provider.event.22.message" value="Node.js V8 Symbol Move"/>
                <string id="NodeJS-ETW-provider.event.23.message" value="Node.js V8 Symbol Reset"/>
            </stringTable>
        </resources>
    </localization>
</instrumentationManifest>
