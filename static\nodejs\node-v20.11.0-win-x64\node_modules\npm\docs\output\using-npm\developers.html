<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>developers</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="developers">developers</h1>
<span class="description">Developer Guide</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#description">Description</a></li><li><a href="#about-these-documents">About These Documents</a></li><li><a href="#what-is-a-package">What is a Package</a></li><li><a href="#the-packagejson-file">The package.json File</a></li><li><a href="#keeping-files-out-of-your-package">Keeping files <em>out</em> of your Package</a></li><ul><li><a href="#testing-whether-your-npmignore-or-files-config-works">Testing whether your <code>.npmignore</code> or <code>files</code> config works</a></li></ul><li><a href="#link-packages">Link Packages</a></li><li><a href="#before-publishing-make-sure-your-package-installs-and-works">Before Publishing: Make Sure Your Package Installs and Works</a></li><li><a href="#create-a-user-account">Create a User Account</a></li><li><a href="#publish-your-package">Publish your Package</a></li><li><a href="#brag-about-it">Brag about it</a></li><li><a href="#see-also">See also</a></li></ul></div>
</section>

<div id="_content"><h3 id="description">Description</h3>
<p>So, you've decided to use npm to develop (and maybe publish/deploy)
your project.</p>
<p>Fantastic!</p>
<p>There are a few things that you need to do above the simple steps
that your users will do to install your program.</p>
<h3 id="about-these-documents">About These Documents</h3>
<p>These are man pages.  If you install npm, you should be able to
then do <code>man npm-thing</code> to get the documentation on a particular
topic, or <code>npm help thing</code> to see the same information.</p>
<h3 id="what-is-a-package">What is a Package</h3>
<p>A package is:</p>
<ul>
<li>a) a folder containing a program described by a package.json file</li>
<li>b) a gzipped tarball containing (a)</li>
<li>c) a url that resolves to (b)</li>
<li>d) a <code>&lt;name&gt;@&lt;version&gt;</code> that is published on the registry with (c)</li>
<li>e) a <code>&lt;name&gt;@&lt;tag&gt;</code> that points to (d)</li>
<li>f) a <code>&lt;name&gt;</code> that has a "latest" tag satisfying (e)</li>
<li>g) a <code>git</code> url that, when cloned, results in (a).</li>
</ul>
<p>Even if you never publish your package, you can still get a lot of
benefits of using npm if you just want to write a node program (a), and
perhaps if you also want to be able to easily install it elsewhere
after packing it up into a tarball (b).</p>
<p>Git urls can be of the form:</p>
<pre><code class="language-bash">git://github.com/user/project.git#commit-ish
git+ssh://user@hostname:project.git#commit-ish
git+http://user@hostname/project/blah.git#commit-ish
git+https://user@hostname/project/blah.git#commit-ish
</code></pre>
<p>The <code>commit-ish</code> can be any tag, sha, or branch which can be supplied as
an argument to <code>git checkout</code>.  The default is whatever the repository uses
as its default branch.</p>
<h3 id="the-packagejson-file">The package.json File</h3>
<p>You need to have a <code>package.json</code> file in the root of your project to do
much of anything with npm.  That is basically the whole interface.</p>
<p>See <a href="../configuring-npm/package-json.html"><code>package.json</code></a> for details about what
goes in that file.  At the very least, you need:</p>
<ul>
<li>
<p>name: This should be a string that identifies your project.  Please do
not use the name to specify that it runs on node, or is in JavaScript.
You can use the "engines" field to explicitly state the versions of node
(or whatever else) that your program requires, and it's pretty well
assumed that it's JavaScript.</p>
<p>It does not necessarily need to match your github repository name.</p>
<p>So, <code>node-foo</code> and <code>bar-js</code> are bad names.  <code>foo</code> or <code>bar</code> are better.</p>
</li>
<li>
<p>version: A semver-compatible version.</p>
</li>
<li>
<p>engines: Specify the versions of node (or whatever else) that your
program runs on.  The node API changes a lot, and there may be bugs or
new functionality that you depend on.  Be explicit.</p>
</li>
<li>
<p>author: Take some credit.</p>
</li>
<li>
<p>scripts: If you have a special compilation or installation script, then
you should put it in the <code>scripts</code> object.  You should definitely have at
least a basic smoke-test command as the "scripts.test" field.  See
<a href="../using-npm/scripts.html">scripts</a>.</p>
</li>
<li>
<p>main: If you have a single module that serves as the entry point to your
program (like what the "foo" package gives you at require("foo")), then
you need to specify that in the "main" field.</p>
</li>
<li>
<p>directories: This is an object mapping names to folders.  The best ones
to include are "lib" and "doc", but if you use "man" to specify a folder
full of man pages, they'll get installed just like these ones.</p>
</li>
</ul>
<p>You can use <code>npm init</code> in the root of your package in order to get you
started with a pretty basic package.json file.  See <a href="../commands/npm-init.html"><code>npm init</code></a> for more info.</p>
<h3 id="keeping-files-out-of-your-package">Keeping files <em>out</em> of your Package</h3>
<p>Use a <code>.npmignore</code> file to keep stuff out of your package.  If there's no
<code>.npmignore</code> file, but there <em>is</em> a <code>.gitignore</code> file, then npm will ignore
the stuff matched by the <code>.gitignore</code> file.  If you <em>want</em> to include
something that is excluded by your <code>.gitignore</code> file, you can create an
empty <code>.npmignore</code> file to override it. Like <code>git</code>, <code>npm</code> looks for
<code>.npmignore</code> and <code>.gitignore</code> files in all subdirectories of your package,
not only the root directory.</p>
<p><code>.npmignore</code> files follow the <a href="https://git-scm.com/book/en/v2/Git-Basics-Recording-Changes-to-the-Repository#_ignoring">same pattern
rules</a>
as <code>.gitignore</code> files:</p>
<ul>
<li>Blank lines or lines starting with <code>#</code> are ignored.</li>
<li>Standard glob patterns work.</li>
<li>You can end patterns with a forward slash <code>/</code> to specify a directory.</li>
<li>You can negate a pattern by starting it with an exclamation point <code>!</code>.</li>
</ul>
<p>By default, the following paths and files are ignored, so there's no
need to add them to <code>.npmignore</code> explicitly:</p>
<ul>
<li><code>.*.swp</code></li>
<li><code>._*</code></li>
<li><code>.DS_Store</code></li>
<li><code>.git</code></li>
<li><code>.gitignore</code></li>
<li><code>.hg</code></li>
<li><code>.npmignore</code></li>
<li><code>.npmrc</code></li>
<li><code>.lock-wscript</code></li>
<li><code>.svn</code></li>
<li><code>.wafpickle-*</code></li>
<li><code>config.gypi</code></li>
<li><code>CVS</code></li>
<li><code>npm-debug.log</code></li>
</ul>
<p>Additionally, everything in <code>node_modules</code> is ignored, except for
bundled dependencies. npm automatically handles this for you, so don't
bother adding <code>node_modules</code> to <code>.npmignore</code>.</p>
<p>The following paths and files are never ignored, so adding them to
<code>.npmignore</code> is pointless:</p>
<ul>
<li><code>package.json</code></li>
<li><code>README</code> (and its variants)</li>
<li><code>CHANGELOG</code> (and its variants)</li>
<li><code>LICENSE</code> / <code>LICENCE</code></li>
</ul>
<p>If, given the structure of your project, you find <code>.npmignore</code> to be a
maintenance headache, you might instead try populating the <code>files</code>
property of <code>package.json</code>, which is an array of file or directory names
that should be included in your package. Sometimes manually picking
which items to allow is easier to manage than building a block list.</p>
<h4 id="testing-whether-your-npmignore-or-files-config-works">Testing whether your <code>.npmignore</code> or <code>files</code> config works</h4>
<p>If you want to double check that your package will include only the files
you intend it to when published, you can run the <code>npm pack</code> command locally
which will generate a tarball in the working directory, the same way it
does for publishing.</p>
<h3 id="link-packages">Link Packages</h3>
<p><code>npm link</code> is designed to install a development package and see the
changes in real time without having to keep re-installing it.  (You do
need to either re-link or <code>npm rebuild -g</code> to update compiled packages,
of course.)</p>
<p>More info at <a href="../commands/npm-link.html"><code>npm link</code></a>.</p>
<h3 id="before-publishing-make-sure-your-package-installs-and-works">Before Publishing: Make Sure Your Package Installs and Works</h3>
<p><strong>This is important.</strong></p>
<p>If you can not install it locally, you'll have
problems trying to publish it.  Or, worse yet, you'll be able to
publish it, but you'll be publishing a broken or pointless package.
So don't do that.</p>
<p>In the root of your package, do this:</p>
<pre><code class="language-bash">npm install . -g
</code></pre>
<p>That'll show you that it's working.  If you'd rather just create a symlink
package that points to your working directory, then do this:</p>
<pre><code class="language-bash">npm link
</code></pre>
<p>Use <code>npm ls -g</code> to see if it's there.</p>
<p>To test a local install, go into some other folder, and then do:</p>
<pre><code class="language-bash">cd ../some-other-folder
npm install ../my-package
</code></pre>
<p>to install it locally into the node_modules folder in that other place.</p>
<p>Then go into the node-repl, and try using require("my-thing") to
bring in your module's main module.</p>
<h3 id="create-a-user-account">Create a User Account</h3>
<p>Create a user with the adduser command.  It works like this:</p>
<pre><code class="language-bash">npm adduser
</code></pre>
<p>and then follow the prompts.</p>
<p>This is documented better in <a href="../commands/npm-adduser.html">npm adduser</a>.</p>
<h3 id="publish-your-package">Publish your Package</h3>
<p>This part's easy.  In the root of your folder, do this:</p>
<pre><code class="language-bash">npm publish
</code></pre>
<p>You can give publish a url to a tarball, or a filename of a tarball,
or a path to a folder.</p>
<p>Note that pretty much <strong>everything in that folder will be exposed</strong>
by default.  So, if you have secret stuff in there, use a
<code>.npmignore</code> file to list out the globs to ignore, or publish
from a fresh checkout.</p>
<h3 id="brag-about-it">Brag about it</h3>
<p>Send emails, write blogs, blab in IRC.</p>
<p>Tell the world how easy it is to install your program!</p>
<h3 id="see-also">See also</h3>
<ul>
<li><a href="../commands/npm.html">npm</a></li>
<li><a href="../commands/npm-init.html">npm init</a></li>
<li><a href="../configuring-npm/package-json.html">package.json</a></li>
<li><a href="../using-npm/scripts.html">npm scripts</a></li>
<li><a href="../commands/npm-publish.html">npm publish</a></li>
<li><a href="../commands/npm-adduser.html">npm adduser</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/using-npm/developers.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>