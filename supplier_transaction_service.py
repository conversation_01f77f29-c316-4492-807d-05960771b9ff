#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应商往来交易服务模块
统一处理供应商往来交易的数据逻辑，避免重复累加问题
"""

from app import app, db
from models import (
    SupplierTransaction, MaterialTransaction, MoneyTransaction,
    Supplier, OldMaterial, Account, AccountTransaction
)
from datetime import datetime
import traceback
from decimal_utils import DecimalUtils, safe_round_2, safe_round_3, safe_add, safe_subtract


class SupplierTransactionService:
    """供应商往来交易服务类"""
    
    @staticmethod
    def calculate_material_changes(material_transactions_data):
        """
        计算物料交易对各种库存的影响
        返回: {
            'old_material_changes': {material_type: weight_change},
            'supplier_gold_changes': {
                'owed_gold_change': float,
                'deposit_gold_change': float
            }
        }
        """
        old_material_changes = {}
        owed_gold_change = 0.0
        deposit_gold_change = 0.0

        for mt_data in material_transactions_data:
            # 还料处理
            return_weight = float(mt_data.get('return_weight', 0))
            return_material_type = mt_data.get('return_material_type', '')
            return_source = mt_data.get('return_source', '')

            if return_weight > 0 and return_material_type:
                app.logger.info(f"还料处理: {return_material_type} {return_weight}克，来源: {return_source}")

                # 还料和存料不涉及料部库存的变动
                # 如果是旧料足金，减少当前供应商欠料
                if return_material_type == '旧料足金':
                    owed_gold_change -= return_weight  # 还料减少当前供应商欠料
                    app.logger.info(f"旧料足金还料减少当前供应商欠料: -{return_weight}克")

                    # 如果来源是存料抵扣，减少存料
                    if return_source == '存料抵扣':
                        deposit_gold_change -= return_weight
                        app.logger.info(f"存料抵扣减少当前供应商存料: -{return_weight}克")

            # 存料处理
            store_weight = float(mt_data.get('store_weight', 0))
            store_material_type = mt_data.get('store_material_type', '')

            if store_weight > 0 and store_material_type:
                app.logger.info(f"存料处理: {store_material_type} {store_weight}克")

                # 还料和存料不涉及料部库存的变动
                # 如果是旧料足金，增加当前供应商存料
                if store_material_type == '旧料足金':
                    deposit_gold_change += store_weight
                    app.logger.info(f"旧料足金存料增加当前供应商存料: +{store_weight}克")

            # 寄料处理
            deposit_weight = float(mt_data.get('deposit_weight', 0))
            actual_deposit_weight = float(mt_data.get('actual_deposit_weight', 0))
            deposit_material_type = mt_data.get('deposit_material_type', '')

            if deposit_weight > 0 and deposit_material_type:
                # 寄料减少旧料库存（数值为寄料克重）
                old_material_changes[deposit_material_type] = old_material_changes.get(deposit_material_type, 0) - deposit_weight
                app.logger.info(f"寄料减少库存: {deposit_material_type} -{deposit_weight}克")

                # 如果是旧料足金，减少当前供应商欠料（数值为寄料克重）
                if deposit_material_type == '旧料足金':
                    owed_gold_change -= deposit_weight  # 使用寄料克重，不是实际到料克重
                    app.logger.info(f"旧料足金寄料减少当前供应商欠料: -{deposit_weight}克")

        return {
            'old_material_changes': old_material_changes,
            'supplier_gold_changes': {
                'owed_gold_change': owed_gold_change,
                'deposit_gold_change': deposit_gold_change
            }
        }
    
    @staticmethod
    def calculate_money_changes(money_transactions_data):
        """
        计算款项交易对供应商资金的影响
        返回: {
            'owed_amount_change': float,
            'deposit_amount_change': float,
            'account_changes': {account_name: amount_change},
            'buy_material_gold_change': float  # 买料导致的欠料变化
        }
        """
        owed_amount_change = 0.0
        deposit_amount_change = 0.0
        account_changes = {}
        buy_material_gold_change = 0.0  # 买料导致的欠料变化

        for mt_data in money_transactions_data:
            # 还款处理
            return_amount = float(mt_data.get('return_amount', 0))
            return_source = mt_data.get('return_source', '')
            return_purpose = mt_data.get('return_purpose', '还款')
            return_material_type = mt_data.get('return_material_type', '')
            return_weight = float(mt_data.get('return_weight', 0))

            if return_amount > 0:
                # 所有还款都只减少欠款金额，不区分买料和其他用途
                owed_amount_change -= return_amount
                app.logger.info(f"还款减少欠款: -{return_amount}元 (用途: {return_purpose})")

                # 如果来源是存款抵扣，减少存款
                if return_source == '存款抵扣':
                    deposit_amount_change -= return_amount
                    app.logger.info(f"还款使用存款抵扣，减少存款: -{return_amount}元")

                # 还款表格不管还款用途是什么，都需要减少对应账户的余额
                if return_source and return_source not in ['现金', '存款抵扣']:
                    account_changes[return_source] = account_changes.get(return_source, 0) - return_amount
                    app.logger.info(f"还款减少账户 {return_source} 余额: -{return_amount}元")

            # 存款处理
            store_amount = float(mt_data.get('store_amount', 0))
            store_source = mt_data.get('store_source', '')

            if store_amount > 0:
                deposit_amount_change += store_amount  # 存款增加存款
                app.logger.info(f"存款增加存款: +{store_amount}元")

                # 如果指定了账户，减少账户余额
                if store_source and store_source not in ['现金']:
                    account_changes[store_source] = account_changes.get(store_source, 0) - store_amount
                    app.logger.info(f"存款减少账户 {store_source} 余额: -{store_amount}元")

        return {
            'owed_amount_change': owed_amount_change,
            'deposit_amount_change': deposit_amount_change,
            'account_changes': account_changes,
            'buy_material_gold_change': buy_material_gold_change
        }
    
    @staticmethod
    def apply_old_material_changes(old_material_changes):
        """记录旧料库存变化（库存由料部库存查询计算，此处仅记录日志）"""
        for material_type, weight_change in old_material_changes.items():
            if weight_change != 0:
                app.logger.info(f"旧料库存变化记录: {material_type} 变化{weight_change:+.3f}克（库存由料部库存查询计算）")
    
    @staticmethod
    def apply_supplier_changes(supplier, gold_changes, money_changes):
        """记录供应商信息变化（余额由供应商款料明细计算，此处仅记录日志）"""
        # 记录黄金相关变化（物料交易和买料还款都影响欠料）
        owed_gold_change = gold_changes['owed_gold_change'] + money_changes.get('buy_material_gold_change', 0)
        if owed_gold_change != 0:
            app.logger.info(f"供应商欠料变化记录: {supplier.name} 变化{owed_gold_change:+.3f}g（余额由供应商款料明细计算）")

        if gold_changes['deposit_gold_change'] != 0:
            app.logger.info(f"供应商存料变化记录: {supplier.name} 变化{gold_changes['deposit_gold_change']:+.3f}g（余额由供应商款料明细计算）")

        # 记录资金相关变化
        if money_changes['owed_amount_change'] != 0:
            app.logger.info(f"供应商欠款变化记录: {supplier.name} 变化¥{money_changes['owed_amount_change']:+.2f}（余额由供应商款料明细计算）")

        if money_changes['deposit_amount_change'] != 0:
            app.logger.info(f"供应商存款变化记录: {supplier.name} 变化¥{money_changes['deposit_amount_change']:+.2f}（余额由供应商款料明细计算）")
    
    @staticmethod
    def apply_account_changes(account_changes):
        """应用账户余额变化"""
        for account_name, amount_change in account_changes.items():
            if amount_change != 0:
                account = Account.query.filter_by(name=account_name).first()
                if account:
                    old_balance = account.balance or 0  # 确保初始值不为None
                    account.balance = round(old_balance + amount_change, 2)
                    app.logger.info(f"账户余额变化: {account_name} {old_balance} -> {account.balance} (变化{amount_change:+.2f})")
                    db.session.add(account)
                else:
                    app.logger.warning(f"未找到账户: {account_name}")
    
    @staticmethod
    def rollback_gold_supplier_changes(material_transactions_data):
        """
        回滚金料供应商欠料变化
        """
        for mt_data in material_transactions_data:
            # 回滚还料对金料供应商的影响
            return_weight = float(mt_data.get('return_weight', 0))
            return_material_type = mt_data.get('return_material_type', '')
            return_source = mt_data.get('return_source', '')

            # 回滚还料对来源供应商的影响（支持所有旧料类型）
            if (return_weight > 0 and
                return_material_type and
                return_source and
                return_source != '存料抵扣' and
                return_source != '库存'):

                # 查找来源供应商
                source_supplier = Supplier.query.filter_by(name=return_source).first()
                if source_supplier and source_supplier.supplier_type in ['gold', 'gold_material', 'material']:
                    # 回滚来源供应商的旧料余额（减少欠料）
                    SupplierTransactionService._update_supplier_material_balance(
                        source_supplier.id, return_material_type, -return_weight, 0
                    )
                    app.logger.info(f"回滚还料来源供应商: {return_source} {return_material_type}欠料减少 {return_weight}克")

            # 回滚存料对金料供应商的影响
            store_weight = float(mt_data.get('store_weight', 0))
            store_material_type = mt_data.get('store_material_type', '')
            store_source = mt_data.get('store_source', '')

            # 回滚存料对来源供应商的影响（支持所有旧料类型）
            if (store_weight > 0 and
                store_material_type and
                store_source and
                store_source != '库存'):

                # 查找来源供应商
                source_supplier = Supplier.query.filter_by(name=store_source).first()
                if source_supplier and source_supplier.supplier_type in ['gold', 'gold_material', 'material']:
                    # 回滚来源供应商的旧料余额（减少欠料）
                    SupplierTransactionService._update_supplier_material_balance(
                        source_supplier.id, store_material_type, -store_weight, 0
                    )
                    app.logger.info(f"回滚存料来源供应商: {store_source} {store_material_type}欠料减少 {store_weight}克")

    @staticmethod
    def rollback_transaction_effects(transaction_id):
        """
        回滚指定交易的所有影响
        返回回滚的变化量，用于日志记录
        """
        app.logger.info(f"开始回滚交易 {transaction_id} 的影响")

        # 获取原始交易数据
        material_transactions = MaterialTransaction.query.filter_by(transaction_id=transaction_id).all()
        money_transactions = MoneyTransaction.query.filter_by(transaction_id=transaction_id).all()

        # 构造原始数据格式
        material_data = []
        for mt in material_transactions:
            material_data.append({
                'return_weight': mt.return_weight or 0,
                'return_material_type': mt.return_material_type or '',
                'return_source': mt.return_source or '',
                'store_weight': mt.store_weight or 0,
                'store_material_type': mt.store_material_type or '',
                'store_source': mt.store_source or '',
                'deposit_weight': mt.deposit_weight or 0,
                'actual_deposit_weight': mt.actual_deposit_weight or 0,
                'deposit_material_type': mt.deposit_material_type or ''
            })

        money_data = []
        for mt in money_transactions:
            money_data.append({
                'return_amount': mt.return_amount or 0,
                'return_source': mt.return_source or '',
                'return_purpose': getattr(mt, 'return_purpose', '还款'),
                'return_material_type': getattr(mt, 'return_material_type', ''),
                'return_weight': getattr(mt, 'return_weight', 0),
                'store_amount': mt.store_amount or 0,
                'store_source': mt.store_source or ''
            })

        # 计算回滚变化（取负值）
        material_changes = SupplierTransactionService.calculate_material_changes(material_data)
        money_changes = SupplierTransactionService.calculate_money_changes(money_data)

        # 应用回滚（所有变化取负值）
        rollback_old_material_changes = {k: -v for k, v in material_changes['old_material_changes'].items()}
        rollback_gold_changes = {k: -v for k, v in material_changes['supplier_gold_changes'].items()}
        rollback_money_changes = {
            'owed_amount_change': -money_changes['owed_amount_change'],
            'deposit_amount_change': -money_changes['deposit_amount_change'],
            'buy_material_gold_change': -money_changes.get('buy_material_gold_change', 0)
        }
        rollback_account_changes = {k: -v for k, v in money_changes['account_changes'].items()}

        # 应用回滚变化
        SupplierTransactionService.apply_old_material_changes(rollback_old_material_changes)

        # 获取供应商并应用回滚
        transaction = SupplierTransaction.query.get(transaction_id)
        if transaction:
            supplier = Supplier.query.get(transaction.supplier_id)
            if supplier:
                SupplierTransactionService.apply_supplier_changes(supplier, rollback_gold_changes, rollback_money_changes)

        SupplierTransactionService.apply_account_changes(rollback_account_changes)

        # 回滚金料供应商欠料变化
        SupplierTransactionService.rollback_gold_supplier_changes(material_data)

        app.logger.info(f"交易 {transaction_id} 回滚完成")

        return {
            'old_material_changes': rollback_old_material_changes,
            'gold_changes': rollback_gold_changes,
            'money_changes': rollback_money_changes,
            'account_changes': rollback_account_changes
        }
    
    @staticmethod
    def apply_gold_supplier_changes(material_transactions_data):
        """
        处理金料供应商欠料变化
        如果旧料名称为旧料足金，存料或还料来源为某一金料供应商，
        则供应商信息表中对应的金料供应商欠料克重增加还料/存料克重的值
        """
        for mt_data in material_transactions_data:
            # 处理还料
            return_weight = float(mt_data.get('return_weight', 0))
            return_material_type = mt_data.get('return_material_type', '')
            return_source = mt_data.get('return_source', '')

            # 处理还料对来源供应商的影响（支持所有旧料类型）
            if (return_weight > 0 and
                return_material_type and
                return_source and
                return_source != '存料抵扣' and
                return_source != '库存'):

                # 查找来源供应商
                source_supplier = Supplier.query.filter_by(name=return_source).first()
                if source_supplier and source_supplier.supplier_type in ['gold', 'gold_material', 'material']:
                    # 更新来源供应商的旧料余额
                    SupplierTransactionService._update_supplier_material_balance(
                        source_supplier.id, return_material_type, return_weight, 0
                    )
                    app.logger.info(f"还料来源供应商: {return_source} {return_material_type}欠料增加 {return_weight}克")

            # 处理存料
            store_weight = float(mt_data.get('store_weight', 0))
            store_material_type = mt_data.get('store_material_type', '')
            store_source = mt_data.get('store_source', '')

            # 处理存料对来源供应商的影响（支持所有旧料类型）
            if (store_weight > 0 and
                store_material_type and
                store_source and
                store_source != '库存'):

                # 查找来源供应商
                source_supplier = Supplier.query.filter_by(name=store_source).first()
                if source_supplier and source_supplier.supplier_type in ['gold', 'gold_material', 'material']:
                    # 更新来源供应商的旧料余额
                    SupplierTransactionService._update_supplier_material_balance(
                        source_supplier.id, store_material_type, store_weight, 0
                    )
                    app.logger.info(f"存料来源供应商: {store_source} {store_material_type}欠料增加 {store_weight}克")

    @staticmethod
    def apply_transaction_effects(supplier_id, material_transactions_data, money_transactions_data):
        """
        应用交易的所有影响
        """
        app.logger.info(f"开始应用交易影响，供应商ID: {supplier_id}")

        # 计算所有变化
        material_changes = SupplierTransactionService.calculate_material_changes(material_transactions_data)
        money_changes = SupplierTransactionService.calculate_money_changes(money_transactions_data)

        # 应用变化
        SupplierTransactionService.apply_old_material_changes(material_changes['old_material_changes'])

        supplier = Supplier.query.get(supplier_id)
        if supplier:
            SupplierTransactionService.apply_supplier_changes(
                supplier,
                material_changes['supplier_gold_changes'],
                money_changes
            )

        SupplierTransactionService.apply_account_changes(money_changes['account_changes'])

        # 处理金料供应商欠料变化
        SupplierTransactionService.apply_gold_supplier_changes(material_transactions_data)

        app.logger.info(f"交易影响应用完成")

        return {
            'old_material_changes': material_changes['old_material_changes'],
            'gold_changes': material_changes['supplier_gold_changes'],
            'money_changes': money_changes,
            'account_changes': money_changes['account_changes']
        }

    @staticmethod
    def _update_supplier_material_balance(supplier_id, material_type, owed_change, stored_change):
        """更新供应商旧料余额"""
        from models import SupplierMaterialBalance

        # 查找或创建供应商旧料余额记录
        balance = SupplierMaterialBalance.query.filter_by(
            supplier_id=supplier_id,
            material_name=material_type
        ).first()

        if not balance:
            # 创建新记录
            balance = SupplierMaterialBalance(
                supplier_id=supplier_id,
                material_name=material_type,
                owed_weight=0,
                stored_weight=0
            )
            db.session.add(balance)

        # 更新余额
        if owed_change != 0:
            old_owed = balance.owed_weight or 0
            balance.owed_weight = round(old_owed + owed_change, 3)
            app.logger.info(f"供应商{supplier_id} {material_type}欠料: {old_owed} -> {balance.owed_weight} (变化{owed_change:+.3f})")

        if stored_change != 0:
            old_stored = balance.stored_weight or 0
            balance.stored_weight = round(old_stored + stored_change, 3)
            app.logger.info(f"供应商{supplier_id} {material_type}存料: {old_stored} -> {balance.stored_weight} (变化{stored_change:+.3f})")

        db.session.add(balance)





