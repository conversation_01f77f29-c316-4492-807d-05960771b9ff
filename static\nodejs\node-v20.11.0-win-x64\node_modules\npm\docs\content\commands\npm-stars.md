---
title: npm-stars
section: 1
description: View packages marked as favorites
---

### Synopsis

```bash
npm stars [<user>]
```

Note: This command is unaware of workspaces.

### Description

If you have starred a lot of neat things and want to find them again
quickly this command lets you do just that.

You may also want to see your friend's favorite packages, in this case
you will most certainly enjoy this command.

### Configuration

#### `registry`

* Default: "https://registry.npmjs.org/"
* Type: URL

The base URL of the npm registry.



### See Also

* [npm star](/commands/npm-star)
* [npm unstar](/commands/npm-unstar)
* [npm view](/commands/npm-view)
* [npm whoami](/commands/npm-whoami)
* [npm adduser](/commands/npm-adduser)
