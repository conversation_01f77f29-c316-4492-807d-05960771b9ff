#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户管理路由
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from models import db, User, Employee
import json
from datetime import datetime

# 创建蓝图
user_bp = Blueprint('user', __name__)

def generate_unique_username(employee_name):
    """生成唯一的用户名"""
    # 基础用户名：员工姓名的拼音或简化版
    base_username = employee_name.replace(' ', '').lower()

    # 如果用户名已存在，添加随机数字
    username = base_username
    counter = 1
    while User.query.filter_by(username=username).first():
        username = f"{base_username}{counter}"
        counter += 1

    return username

def create_user_for_employee(employee):
    """为员工自动创建用户账户"""
    # 检查该员工是否已有用户账户
    if employee.user:
        return

    # 生成唯一用户名
    username = generate_unique_username(employee.name)

    # 创建用户账户
    user = User(
        username=username,
        name=employee.name,
        employee_id=employee.id,
        role='user',
        password='123'  # 默认密码
    )

    # 设置初始权限：只能看到开单录入
    initial_permissions = DEFAULT_MENU_PERMISSIONS.copy()
    initial_permissions['orders'] = True  # 只给开单录入权限

    # 如果是老板，给予所有权限
    if employee.position == '老板':
        user.role = 'admin'
        initial_permissions = {key: True for key in DEFAULT_MENU_PERMISSIONS.keys()}

    user.menu_permissions = json.dumps(initial_permissions, ensure_ascii=False)

    db.session.add(user)
    db.session.commit()

# 默认菜单权限配置 - 包含所有菜单项
DEFAULT_MENU_PERMISSIONS = {
    # 基础信息管理
    'products': False,              # 产品信息
    'customers': False,             # 客户信息
    'old_materials': False,         # 旧料信息
    'suppliers': False,             # 供应商信息
    'employees': False,             # 员工信息
    'accounts': False,              # 账户信息

    # 单据管理
    'orders': False,                # 开单录入
    'purchases': False,             # 采购录入
    'supplier_transactions': False, # 供应商往来录入
    'customer_transactions': False, # 其他项目往来
    'material_adjustments': False,  # 货料调整录入

    # 报表中心
    'daily_reports': False,         # 每日报表查询
    'customer_reports': False,      # 客户往来查询
    'supplier_reports': False,      # 供应商往来查询
    'inventory_reports': False,     # 库存查询
    'account_reports': False,       # 账户查询

    # 系统管理
    'user_management': False,       # 用户管理
}

@user_bp.route('/users')
@login_required
def users():
    """用户列表页面"""
    if current_user.is_boss():
        # 老板可以看到所有用户
        users = User.query.all()
        return render_template('users/index.html', users=users, is_boss=True)
    else:
        # 普通用户只能看到自己的信息
        users = [current_user]
        return render_template('users/index.html', users=users, is_boss=False)

# 用户创建功能已整合到员工添加流程中

@user_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    """编辑用户"""
    # 创建FlaskForm实例用于CSRF保护
    form = FlaskForm()

    user = User.query.get_or_404(user_id)

    # 权限检查：老板可以编辑所有用户，普通用户只能编辑自己
    if not current_user.is_boss() and user.id != current_user.id:
        flash('您只能修改自己的账户信息', 'danger')
        return redirect(url_for('user.users'))

    if request.method == 'POST' and form.validate_on_submit():
        # 更新基本信息
        user.username = request.form['username']
        user.name = request.form['name']
        user.email = request.form.get('email', '')

        # 只有老板才能修改用户状态
        if current_user.is_boss():
            user.is_active = 'is_active' in request.form

        # 更新密码（如果提供）
        new_password = request.form.get('password')
        if new_password:
            user.password = new_password

        # 只有老板才能更新菜单权限
        if current_user.is_boss():
            permissions = {}
            for key in DEFAULT_MENU_PERMISSIONS.keys():
                permissions[key] = key in request.form
            user.set_menu_permissions(permissions)

        db.session.commit()
        flash('用户信息更新成功', 'success')
        return redirect(url_for('user.users'))

    return render_template('users/edit.html', user=user, menu_permissions=DEFAULT_MENU_PERMISSIONS, is_boss=current_user.is_boss(), form=form)

@user_bp.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
def delete_user(user_id):
    """删除用户"""
    if not current_user.is_boss():
        flash('只有老板才能删除用户', 'danger')
        return redirect(url_for('user.users'))
    
    user = User.query.get_or_404(user_id)
    
    # 不能删除自己
    if user.id == current_user.id:
        flash('不能删除自己的账户', 'danger')
        return redirect(url_for('user.users'))
    
    db.session.delete(user)
    db.session.commit()
    
    flash(f'用户 {user.name} 已删除', 'success')
    return redirect(url_for('user.users'))

@user_bp.route('/profile')
@login_required
def profile():
    """用户个人资料页面"""
    return render_template('users/profile.html')

@user_bp.route('/profile/update', methods=['POST'])
@login_required
def update_profile():
    """更新个人资料"""
    current_user.name = request.form['name']
    current_user.email = request.form.get('email', '')
    
    # 更新密码（如果提供）
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    
    if new_password:
        if not current_password:
            flash('请输入当前密码', 'danger')
            return redirect(url_for('user.profile'))
        
        if not current_user.verify_password(current_password):
            flash('当前密码错误', 'danger')
            return redirect(url_for('user.profile'))
        
        if new_password != confirm_password:
            flash('新密码确认不匹配', 'danger')
            return redirect(url_for('user.profile'))
        
        current_user.password = new_password
        flash('密码更新成功', 'success')
    
    db.session.commit()
    flash('个人资料更新成功', 'success')
    return redirect(url_for('user.profile'))

@user_bp.route('/api/users/<int:user_id>/permissions', methods=['GET', 'POST'])
@login_required
def user_permissions_api(user_id):
    """用户权限API"""
    if not current_user.is_boss():
        return jsonify({'success': False, 'message': '权限不足'})
    
    user = User.query.get_or_404(user_id)
    
    if request.method == 'GET':
        return jsonify({
            'success': True,
            'permissions': user.get_menu_permissions()
        })
    
    elif request.method == 'POST':
        permissions = request.json.get('permissions', {})
        user.set_menu_permissions(permissions)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '权限更新成功'
        })
