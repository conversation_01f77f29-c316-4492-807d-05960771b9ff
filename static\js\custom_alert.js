// 自定义的Swal对象，提供与SweetAlert相似的接口但使用原生alert
const Swal = {
    fire: function(options) {
        if (options.icon === 'success') {
            alert(options.text || options.title || '操作成功');
            // 返回一个带then方法的对象，模拟SweetAlert的Promise接口
            return {
                then: function(callback) {
                    if (typeof callback === 'function') {
                        callback();
                    }
                    return this;
                }
            };
        } else if (options.icon === 'error') {
            alert(options.text || options.title || '操作失败');
            return {
                then: function(callback) {
                    return this;
                }
            };
        } else {
            alert(options.text || options.title || '提示');
            return {
                then: function(callback) {
                    if (typeof callback === 'function') {
                        callback();
                    }
                    return this;
                }
            };
        }
    }
}; 